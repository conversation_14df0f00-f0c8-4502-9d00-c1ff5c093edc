(()=>{var t={140:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.DIFF_STATUS_UPDATED=e.DIFF_STATUS_REMOVED=e.DIFF_STATUS_KEYS_UPDATED=e.DIFF_STATUS_ARRAY_UPDATED=void 0;e.DIFF_STATUS_UPDATED="updated",e.DIFF_STATUS_REMOVED="removed",e.DIFF_STATUS_KEYS_UPDATED="updated_keys",e.DIFF_STATUS_ARRAY_UPDATED="updated_array"},368:t=>{var e=9007199254740991,r="[object Arguments]",n="[object Function]",o="[object GeneratorFunction]",i=/^(?:0|[1-9]\d*)$/;var a=Object.prototype,s=a.hasOwnProperty,l=a.toString,c=a.propertyIsEnumerable,u=Math.max;function d(t,e){var n=g(t)||function(t){return function(t){return function(t){return!!t&&"object"==typeof t}(t)&&v(t)}(t)&&s.call(t,"callee")&&(!c.call(t,"callee")||l.call(t)==r)}(t)?function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}(t.length,String):[],o=n.length,i=!!o;for(var a in t)!e&&!s.call(t,a)||i&&("length"==a||p(a,o))||n.push(a);return n}function f(t,e,r){var n=t[e];s.call(t,e)&&m(n,r)&&(void 0!==r||e in t)||(t[e]=r)}function h(t){if(!y(t))return function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e}(t);var e,r,n,o=(r=(e=t)&&e.constructor,n="function"==typeof r&&r.prototype||a,e===n),i=[];for(var l in t)("constructor"!=l||!o&&s.call(t,l))&&i.push(l);return i}function p(t,r){return!!(r=null==r?e:r)&&("number"==typeof t||i.test(t))&&t>-1&&t%1==0&&t<r}function m(t,e){return t===e||t!=t&&e!=e}var g=Array.isArray;function v(t){return null!=t&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=e}(t.length)&&!function(t){var e=y(t)?l.call(t):"";return e==n||e==o}(t)}function y(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}var b,E,w,S=(b=function(t,e){!function(t,e,r,n){r||(r={});for(var o=-1,i=e.length;++o<i;){var a=e[o],s=n?n(r[a],t[a],a,r,t):void 0;f(r,a,void 0===s?t[a]:s)}}(e,function(t){return v(t)?d(t,!0):h(t)}(e),t)},E=function(t,e){var r=-1,n=e.length,o=n>1?e[n-1]:void 0,i=n>2?e[2]:void 0;for(o=b.length>3&&"function"==typeof o?(n--,o):void 0,i&&function(t,e,r){if(!y(r))return!1;var n=typeof e;return!!("number"==n?v(r)&&p(e,r.length):"string"==n&&e in r)&&m(r[e],t)}(e[0],e[1],i)&&(o=n<3?void 0:o,n=1),t=Object(t);++r<n;){var a=e[r];a&&b(t,a,r,o)}return t},w=u(void 0===w?E.length-1:w,0),function(){for(var t=arguments,e=-1,r=u(t.length-w,0),n=Array(r);++e<r;)n[e]=t[w+e];e=-1;for(var o=Array(w+1);++e<w;)o[e]=t[e];return o[w]=n,function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}(E,this,o)});t.exports=S},609:function(t){var e;"undefined"!=typeof self&&self,e=()=>(()=>{"use strict";var t={0:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.mathRules=e.createCleanMathEl=void 0;const n=r(282);e.createCleanMathEl=(t,e,r,n)=>{const o=t.createElement("math");if(o.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),o.setAttribute("display",n?"block":"inline"),o.setAttribute("data-latex",r||""),null==e?void 0:e.mathml){const r=t.createElement("div");r.innerHTML=e.mathml;const n=r.querySelector("math");n&&(o.innerHTML=n.innerHTML)}else r&&(o.textContent=r);return o},e.mathRules=[{selector:n.mathSelectors,element:"math",transform:(t,r)=>{if(!function(t){return"classList"in t&&"getAttribute"in t&&"querySelector"in t}(t))return t;const o=(0,n.getMathMLFromElement)(t),i=(0,n.getBasicLatexFromElement)(t),a=(0,n.isBlockDisplay)(t),s=(0,e.createCleanMathEl)(r,o,i,a);return t.parentElement&&t.parentElement.querySelectorAll('\n\t\t\t\t\t/* MathJax scripts and previews */\n\t\t\t\t\tscript[type^="math/"],\n\t\t\t\t\t.MathJax_Preview,\n\n\t\t\t\t\t/* External math library scripts */\n\t\t\t\t\tscript[type="text/javascript"][src*="mathjax"],\n\t\t\t\t\tscript[type="text/javascript"][src*="katex"]\n\t\t\t\t').forEach((t=>t.remove())),s}}]},20:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.GrokExtractor=void 0;const n=r(181);class o extends n.ConversationExtractor{constructor(t,e){super(t,e),this.messageContainerSelector=".relative.group.flex.flex-col.justify-center.w-full",this.messageBubbles=t.querySelectorAll(this.messageContainerSelector),this.footnotes=[],this.footnoteCounter=0}canExtract(){return!!this.messageBubbles&&this.messageBubbles.length>0}extractMessages(){const t=[];return this.footnotes=[],this.footnoteCounter=0,this.messageBubbles&&0!==this.messageBubbles.length?(this.messageBubbles.forEach((e=>{var r;const n=e.classList.contains("items-end"),o=e.classList.contains("items-start");if(!n&&!o)return;const i=e.querySelector(".message-bubble");if(!i)return;let a="",s="",l="";if(n)a=i.textContent||"",s="user",l="You";else if(o){s="assistant",l="Grok";const t=i.cloneNode(!0);null===(r=t.querySelector(".relative.border.border-border-l1.bg-surface-base"))||void 0===r||r.remove(),a=t.innerHTML,a=this.processFootnotes(a)}a.trim()&&t.push({author:l,content:a.trim(),metadata:{role:s}})})),t):t}getFootnotes(){return this.footnotes}getMetadata(){var t;const e=this.getTitle(),r=(null===(t=this.messageBubbles)||void 0===t?void 0:t.length)||0;return{title:e,site:"Grok",url:this.url,messageCount:r,description:`Grok conversation with ${r} messages`}}getTitle(){var t,e;const r=null===(t=this.document.title)||void 0===t?void 0:t.trim();if(r&&"Grok"!==r&&!r.startsWith("Grok by "))return r.replace(/\s-\s*Grok$/,"").trim();const n=this.document.querySelector(`${this.messageContainerSelector}.items-end`);if(n){const t=n.querySelector(".message-bubble");if(t){const r=(null===(e=t.textContent)||void 0===e?void 0:e.trim())||"";return r.length>50?r.slice(0,50)+"...":r}}return"Grok Conversation"}processFootnotes(t){return t.replace(/<a\s+(?:[^>]*?\s+)?href="([^"]*)"[^>]*>(.*?)<\/a>/gi,((t,e,r)=>{if(!e||e.startsWith("#")||!e.match(/^https?:\/\//i))return t;let n;if(this.footnotes.find((t=>t.url===e)))n=this.footnotes.findIndex((t=>t.url===e))+1;else{this.footnoteCounter++,n=this.footnoteCounter;let t=e;try{const r=new URL(e).hostname.replace(/^www\./,"");t=`<a href="${e}" target="_blank" rel="noopener noreferrer">${r}</a>`}catch(r){t=`<a href="${e}" target="_blank" rel="noopener noreferrer">${e}</a>`}this.footnotes.push({url:e,text:t})}return`${r}<sup id="fnref:${n}" class="footnote-ref"><a href="#fn:${n}" class="footnote-link">${n}</a></sup>`}))}}e.GrokExtractor=o},181:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ConversationExtractor=void 0;const n=r(279),o=r(628);class i extends n.BaseExtractor{getFootnotes(){return[]}extract(){var t;const e=this.extractMessages(),r=this.getMetadata(),n=this.getFootnotes(),i=this.createContentHtml(e,n),a=document.implementation.createHTMLDocument(),s=a.createElement("article");s.innerHTML=i,a.body.appendChild(s);const l=new o.Defuddle(a).parse(),c=l.content;return{content:c,contentHtml:c,extractedContent:{messageCount:e.length.toString()},variables:{title:r.title||"Conversation",site:r.site,description:r.description||`${r.site} conversation with ${e.length} messages`,wordCount:(null===(t=l.wordCount)||void 0===t?void 0:t.toString())||""}}}createContentHtml(t,e){return`${t.map(((e,r)=>{const n=e.timestamp?`<div class="message-timestamp">${e.timestamp}</div>`:"",o=/<p[^>]*>[\s\S]*?<\/p>/i.test(e.content)?e.content:`<p>${e.content}</p>`,i=e.metadata?Object.entries(e.metadata).map((([t,e])=>`data-${t}="${e}"`)).join(" "):"";return`\n\t\t\t<div class="message message-${e.author.toLowerCase()}" ${i}>\n\t\t\t\t<div class="message-header">\n\t\t\t\t\t<p class="message-author"><strong>${e.author}</strong></p>\n\t\t\t\t\t${n}\n\t\t\t\t</div>\n\t\t\t\t<div class="message-content">\n\t\t\t\t\t${o}\n\t\t\t\t</div>\n\t\t\t</div>${r<t.length-1?"\n<hr>":""}`})).join("\n").trim()}\n${e.length>0?`\n\t\t\t<div id="footnotes">\n\t\t\t\t<ol>\n\t\t\t\t\t${e.map(((t,e)=>`\n\t\t\t\t\t\t<li class="footnote" id="fn:${e+1}">\n\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t<a href="${t.url}" target="_blank">${t.text}</a>&nbsp;<a href="#fnref:${e+1}" class="footnote-backref">↩</a>\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</li>\n\t\t\t\t\t`)).join("")}\n\t\t\t\t</ol>\n\t\t\t</div>`:""}`.trim()}}e.ConversationExtractor=i},248:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TwitterExtractor=void 0;const n=r(279);class o extends n.BaseExtractor{constructor(t,e){var r;super(t,e),this.mainTweet=null,this.threadTweets=[];const n=t.querySelector('[aria-label="Timeline: Conversation"]');if(!n){const e=t.querySelector('article[data-testid="tweet"]');return void(e&&(this.mainTweet=e))}const o=Array.from(n.querySelectorAll('article[data-testid="tweet"]')),i=null===(r=n.querySelector("section, h2"))||void 0===r?void 0:r.parentElement;i&&o.forEach(((t,e)=>{if(i.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING)return o.splice(e),!1})),this.mainTweet=o[0]||null,this.threadTweets=o.slice(1)}canExtract(){return!!this.mainTweet}extract(){const t=this.extractTweet(this.mainTweet),e=this.threadTweets.map((t=>this.extractTweet(t))).join("\n<hr>\n"),r=`\n\t\t\t<div class="tweet-thread">\n\t\t\t\t<div class="main-tweet">\n\t\t\t\t\t${t}\n\t\t\t\t</div>\n\t\t\t\t${e?`\n\t\t\t\t\t<hr>\n\t\t\t\t\t<div class="thread-tweets">\n\t\t\t\t\t\t${e}\n\t\t\t\t\t</div>\n\t\t\t\t`:""}\n\t\t\t</div>\n\t\t`.trim(),n=this.getTweetId(),o=this.getTweetAuthor();return{content:r,contentHtml:r,extractedContent:{tweetId:n,tweetAuthor:o},variables:{title:`Thread by ${o}`,author:o,site:"X (Twitter)",description:this.createDescription(this.mainTweet)}}}formatTweetText(t){if(!t)return"";const e=document.createElement("div");return e.innerHTML=t,e.querySelectorAll("a").forEach((t=>{var e;const r=(null===(e=t.textContent)||void 0===e?void 0:e.trim())||"";t.replaceWith(r)})),e.querySelectorAll("span, div").forEach((t=>{t.replaceWith(...Array.from(t.childNodes))})),e.innerHTML.split("\n").map((t=>t.trim())).filter((t=>t)).map((t=>`<p>${t}</p>`)).join("\n")}extractTweet(t){var e,r,n;if(!t)return"";const o=t.cloneNode(!0);o.querySelectorAll('img[src*="/emoji/"]').forEach((t=>{if("img"===t.tagName.toLowerCase()&&t.getAttribute("alt")){const e=t.getAttribute("alt");e&&t.replaceWith(e)}}));const i=(null===(e=o.querySelector('[data-testid="tweetText"]'))||void 0===e?void 0:e.innerHTML)||"",a=this.formatTweetText(i),s=this.extractImages(t),l=this.extractUserInfo(t),c=null===(n=null===(r=t.querySelector('[aria-labelledby*="id__"]'))||void 0===r?void 0:r.querySelector('[data-testid="User-Name"]'))||void 0===n?void 0:n.closest('[aria-labelledby*="id__"]'),u=c?this.extractTweet(c):"";return`\n\t\t\t<div class="tweet">\n\t\t\t\t<div class="tweet-header">\n\t\t\t\t\t<span class="tweet-author"><strong>${l.fullName}</strong> <span class="tweet-handle">${l.handle}</span></span>\n\t\t\t\t\t${l.date?`<a href="${l.permalink}" class="tweet-date">${l.date}</a>`:""}\n\t\t\t\t</div>\n\t\t\t\t${a?`<div class="tweet-text">${a}</div>`:""}\n\t\t\t\t${s.length?`\n\t\t\t\t\t<div class="tweet-media">\n\t\t\t\t\t\t${s.join("\n")}\n\t\t\t\t\t</div>\n\t\t\t\t`:""}\n\t\t\t\t${u?`\n\t\t\t\t\t<blockquote class="quoted-tweet">\n\t\t\t\t\t\t${u}\n\t\t\t\t\t</blockquote>\n\t\t\t\t`:""}\n\t\t\t</div>\n\t\t`.trim()}extractUserInfo(t){var e,r,n,o,i,a,s,l,c;const u=t.querySelector('[data-testid="User-Name"]');if(!u)return{fullName:"",handle:"",date:"",permalink:""};const d=u.querySelectorAll("a");let f=(null===(r=null===(e=null==d?void 0:d[0])||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim())||"",h=(null===(o=null===(n=null==d?void 0:d[1])||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||"";f&&h||(f=(null===(a=null===(i=u.querySelector('span[style*="color: rgb(15, 20, 25)"] span'))||void 0===i?void 0:i.textContent)||void 0===a?void 0:a.trim())||"",h=(null===(l=null===(s=u.querySelector('span[style*="color: rgb(83, 100, 113)"]'))||void 0===s?void 0:s.textContent)||void 0===l?void 0:l.trim())||"");const p=t.querySelector("time"),m=(null==p?void 0:p.getAttribute("datetime"))||"";return{fullName:f,handle:h,date:m?new Date(m).toISOString().split("T")[0]:"",permalink:(null===(c=null==p?void 0:p.closest("a"))||void 0===c?void 0:c.href)||""}}extractImages(t){var e,r;const n=['[data-testid="tweetPhoto"]','[data-testid="tweet-image"]','img[src*="media"]'],o=[],i=null===(r=null===(e=t.querySelector('[aria-labelledby*="id__"]'))||void 0===e?void 0:e.querySelector('[data-testid="User-Name"]'))||void 0===r?void 0:r.closest('[aria-labelledby*="id__"]');for(const a of n)t.querySelectorAll(a).forEach((t=>{var e,r;if(!(null==i?void 0:i.contains(t))&&"img"===t.tagName.toLowerCase()&&t.getAttribute("alt")){const n=(null===(e=t.getAttribute("src"))||void 0===e?void 0:e.replace(/&name=\w+$/,"&name=large"))||"",i=(null===(r=t.getAttribute("alt"))||void 0===r?void 0:r.replace(/\s+/g," ").trim())||"";o.push(`<img src="${n}" alt="${i}" />`)}}));return o}getTweetId(){const t=this.url.match(/status\/(\d+)/);return(null==t?void 0:t[1])||""}getTweetAuthor(){var t,e,r;const n=null===(t=this.mainTweet)||void 0===t?void 0:t.querySelector('[data-testid="User-Name"]'),o=null==n?void 0:n.querySelectorAll("a"),i=(null===(r=null===(e=null==o?void 0:o[1])||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim())||"";return i.startsWith("@")?i:`@${i}`}createDescription(t){var e;return t?((null===(e=t.querySelector('[data-testid="tweetText"]'))||void 0===e?void 0:e.textContent)||"").trim().slice(0,140).replace(/\s+/g," "):""}}e.TwitterExtractor=o},258:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.YoutubeExtractor=void 0;const n=r(279);class o extends n.BaseExtractor{constructor(t,e,r){super(t,e,r),this.videoElement=t.querySelector("video"),this.schemaOrgData=r}canExtract(){return!0}extract(){const t=this.getVideoData(),e=t.description||"",r=this.formatDescription(e),n=`<iframe width="560" height="315" src="https://www.youtube.com/embed/${this.getVideoId()}?si=_m0qv33lAuJFoGNh" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe><br>${r}`;return{content:n,contentHtml:n,extractedContent:{videoId:this.getVideoId(),author:t.author||""},variables:{title:t.name||"",author:t.author||"",site:"YouTube",image:Array.isArray(t.thumbnailUrl)&&t.thumbnailUrl[0]||"",published:t.uploadDate,description:e.slice(0,200).trim()}}}formatDescription(t){return`<p>${t.replace(/\n/g,"<br>")}</p>`}getVideoData(){return this.schemaOrgData&&(Array.isArray(this.schemaOrgData)?this.schemaOrgData.find((t=>"VideoObject"===t["@type"])):"VideoObject"===this.schemaOrgData["@type"]?this.schemaOrgData:null)||{}}getVideoId(){return new URLSearchParams(new URL(this.url).search).get("v")||""}}e.YoutubeExtractor=o},279:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BaseExtractor=void 0,e.BaseExtractor=class{constructor(t,e,r){this.document=t,this.url=e,this.schemaOrgData=r}}},282:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.mathSelectors=e.isBlockDisplay=e.getBasicLatexFromElement=e.getMathMLFromElement=void 0,e.getMathMLFromElement=t=>{if("math"===t.tagName.toLowerCase()){const e="block"===t.getAttribute("display");return{mathml:t.outerHTML,latex:t.getAttribute("alttext")||null,isBlock:e}}const e=t.getAttribute("data-mathml");if(e){const t=document.createElement("div");t.innerHTML=e;const r=t.querySelector("math");if(r){const t="block"===r.getAttribute("display");return{mathml:r.outerHTML,latex:r.getAttribute("alttext")||null,isBlock:t}}}const r=t.querySelector(".MJX_Assistive_MathML, mjx-assistive-mml");if(r){const t=r.querySelector("math");if(t){const e=t.getAttribute("display"),n=r.getAttribute("display"),o="block"===e||"block"===n;return{mathml:t.outerHTML,latex:t.getAttribute("alttext")||null,isBlock:o}}}const n=t.querySelector(".katex-mathml math");return n?{mathml:n.outerHTML,latex:null,isBlock:!1}:null},e.getBasicLatexFromElement=t=>{var e,r,n;const o=t.getAttribute("data-latex");if(o)return o;if("img"===t.tagName.toLowerCase()&&t.classList.contains("latex")){const e=t.getAttribute("alt");if(e)return e;const r=t.getAttribute("src");if(r){const t=r.match(/latex\.php\?latex=([^&]+)/);if(t)return decodeURIComponent(t[1]).replace(/\+/g," ").replace(/%5C/g,"\\")}}const i=t.querySelector('annotation[encoding="application/x-tex"]');if(null==i?void 0:i.textContent)return i.textContent.trim();if(t.matches(".katex")){const e=t.querySelector('.katex-mathml annotation[encoding="application/x-tex"]');if(null==e?void 0:e.textContent)return e.textContent.trim()}if(t.matches('script[type="math/tex"]')||t.matches('script[type="math/tex; mode=display"]'))return(null===(e=t.textContent)||void 0===e?void 0:e.trim())||null;if(t.parentElement){const e=t.parentElement.querySelector('script[type="math/tex"], script[type="math/tex; mode=display"]');if(e)return(null===(r=e.textContent)||void 0===r?void 0:r.trim())||null}return t.getAttribute("alt")||(null===(n=t.textContent)||void 0===n?void 0:n.trim())||null},e.isBlockDisplay=t=>{if("block"===t.getAttribute("display"))return!0;const e=t.className.toLowerCase();if(e.includes("display")||e.includes("block"))return!0;if(t.closest('.katex-display, .MathJax_Display, [data-display="block"]'))return!0;const r=t.previousElementSibling;if("p"===(null==r?void 0:r.tagName.toLowerCase()))return!0;if(t.matches(".mwe-math-fallback-image-display"))return!0;if(t.matches(".katex"))return null!==t.closest(".katex-display");if(t.hasAttribute("display"))return"true"===t.getAttribute("display");if(t.matches('script[type="math/tex; mode=display"]'))return!0;if(t.hasAttribute("display"))return"true"===t.getAttribute("display");const n=t.closest("[display]");return!!n&&"true"===n.getAttribute("display")},e.mathSelectors=['img.latex[src*="latex.php"]',"span.MathJax","mjx-container",'script[type="math/tex"]','script[type="math/tex; mode=display"]','.MathJax_Preview + script[type="math/tex"]',".MathJax_Display",".MathJax_SVG",".MathJax_MathML",".mwe-math-element",".mwe-math-fallback-image-inline",".mwe-math-fallback-image-display",".mwe-math-mathml-inline",".mwe-math-mathml-display",".katex",".katex-display",".katex-mathml",".katex-html","[data-katex]",'script[type="math/katex"]',"math","[data-math]","[data-latex]","[data-tex]",'script[type^="math/"]','annotation[encoding="application/x-tex"]'].join(",")},397:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ClaudeExtractor=void 0;const n=r(181);class o extends n.ConversationExtractor{constructor(t,e){super(t,e),this.articles=t.querySelectorAll('div[data-testid="user-message"], div[data-testid="assistant-message"], div.font-claude-message')}canExtract(){return!!this.articles&&this.articles.length>0}extractMessages(){const t=[];return this.articles?(this.articles.forEach((e=>{let r,n;if(e.hasAttribute("data-testid")){if("user-message"!==e.getAttribute("data-testid"))return;r="you",n=e.innerHTML}else{if(!e.classList.contains("font-claude-message"))return;r="assistant",n=e.innerHTML}n&&t.push({author:"you"===r?"You":"Claude",content:n.trim(),metadata:{role:r}})})),t):t}getMetadata(){const t=this.getTitle(),e=this.extractMessages();return{title:t,site:"Claude",url:this.url,messageCount:e.length,description:`Claude conversation with ${e.length} messages`}}getTitle(){var t,e,r,n,o;const i=null===(t=this.document.title)||void 0===t?void 0:t.trim();if(i&&"Claude"!==i)return i.replace(/ - Claude$/,"");const a=null===(r=null===(e=this.document.querySelector("header .font-tiempos"))||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim();if(a)return a;const s=null===(o=null===(n=this.articles)||void 0===n?void 0:n.item(0))||void 0===o?void 0:o.querySelector('[data-testid="user-message"]');if(s){const t=s.textContent||"";return t.length>50?t.slice(0,50)+"...":t}return"Claude Conversation"}}e.ClaudeExtractor=o},458:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.HackerNewsExtractor=void 0;const n=r(279);class o extends n.BaseExtractor{constructor(t,e){super(t,e),this.mainPost=t.querySelector(".fatitem"),this.isCommentPage=this.detectCommentPage(),this.mainComment=this.isCommentPage?this.findMainComment():null}detectCommentPage(){var t;return!!(null===(t=this.mainPost)||void 0===t?void 0:t.querySelector('.navs a[href*="parent"]'))}findMainComment(){var t;return(null===(t=this.mainPost)||void 0===t?void 0:t.querySelector(".comment"))||null}canExtract(){return!!this.mainPost}extract(){const t=this.getPostContent(),e=this.extractComments(),r=this.createContentHtml(t,e),n=this.getPostTitle(),o=this.getPostAuthor(),i=this.createDescription(),a=this.getPostDate();return{content:r,contentHtml:r,extractedContent:{postId:this.getPostId(),postAuthor:o},variables:{title:n,author:o,site:"Hacker News",description:i,published:a}}}createContentHtml(t,e){return`\n\t\t\t<div class="hackernews-post">\n\t\t\t\t<div class="post-content">\n\t\t\t\t\t${t}\n\t\t\t\t</div>\n\t\t\t\t${e?`\n\t\t\t\t\t<hr>\n\t\t\t\t\t<h2>Comments</h2>\n\t\t\t\t\t<div class="hackernews-comments">\n\t\t\t\t\t\t${e}\n\t\t\t\t\t</div>\n\t\t\t\t`:""}\n\t\t\t</div>\n\t\t`.trim()}getPostContent(){var t,e,r,n,o,i;if(!this.mainPost)return"";if(this.isCommentPage&&this.mainComment){const i=(null===(t=this.mainComment.querySelector(".hnuser"))||void 0===t?void 0:t.textContent)||"[deleted]",a=(null===(e=this.mainComment.querySelector(".commtext"))||void 0===e?void 0:e.innerHTML)||"",s=this.mainComment.querySelector(".age"),l=((null==s?void 0:s.getAttribute("title"))||"").split("T")[0]||"",c=(null===(n=null===(r=this.mainComment.querySelector(".score"))||void 0===r?void 0:r.textContent)||void 0===n?void 0:n.trim())||"",u=(null===(o=this.mainPost.querySelector('.navs a[href*="parent"]'))||void 0===o?void 0:o.getAttribute("href"))||"";return`\n\t\t\t\t<div class="comment main-comment">\n\t\t\t\t\t<div class="comment-metadata">\n\t\t\t\t\t\t<span class="comment-author"><strong>${i}</strong></span> •\n\t\t\t\t\t\t<span class="comment-date">${l}</span>\n\t\t\t\t\t\t${c?` • <span class="comment-points">${c}</span>`:""}\n\t\t\t\t\t\t${u?` • <a href="https://news.ycombinator.com/${u}" class="parent-link">parent</a>`:""}\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="comment-content">${a}</div>\n\t\t\t\t</div>\n\t\t\t`.trim()}const a=this.mainPost.querySelector("tr.athing"),s=(null==a||a.nextElementSibling,(null===(i=null==a?void 0:a.querySelector(".titleline a"))||void 0===i?void 0:i.getAttribute("href"))||"");let l="";s&&(l+=`<p><a href="${s}" target="_blank">${s}</a></p>`);const c=this.mainPost.querySelector(".toptext");return c&&(l+=`<div class="post-text">${c.innerHTML}</div>`),l}extractComments(){const t=Array.from(this.document.querySelectorAll("tr.comtr"));return this.processComments(t)}processComments(t){var e,r,n,o;let i="";const a=new Set;let s=-1,l=[];for(const c of t){const t=c.getAttribute("id");if(!t||a.has(t))continue;a.add(t);const u=(null===(e=c.querySelector(".ind img"))||void 0===e?void 0:e.getAttribute("width"))||"0",d=parseInt(u)/40,f=c.querySelector(".commtext"),h=(null===(r=c.querySelector(".hnuser"))||void 0===r?void 0:r.textContent)||"[deleted]",p=c.querySelector(".age"),m=(null===(o=null===(n=c.querySelector(".score"))||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||"";if(!f)continue;const g=`https://news.ycombinator.com/item?id=${t}`,v=((null==p?void 0:p.getAttribute("title"))||"").split("T")[0]||"";if(0===d){for(;l.length>0;)i+="</blockquote>",l.pop();i+="<blockquote>",l=[0],s=0}else if(d<s)for(;l.length>0&&l[l.length-1]>=d;)i+="</blockquote>",l.pop();else d>s&&(i+="<blockquote>",l.push(d));i+=`<div class="comment">\n\t<div class="comment-metadata">\n\t\t<span class="comment-author"><strong>${h}</strong></span> •\n\t\t<a href="${g}" class="comment-link">${v}</a>\n\t\t${m?` • <span class="comment-points">${m}</span>`:""}\n\t</div>\n\t<div class="comment-content">${f.innerHTML}</div>\n</div>`,s=d}for(;l.length>0;)i+="</blockquote>",l.pop();return i}getPostId(){const t=this.url.match(/id=(\d+)/);return(null==t?void 0:t[1])||""}getPostTitle(){var t,e,r,n,o;if(this.isCommentPage&&this.mainComment){const r=(null===(t=this.mainComment.querySelector(".hnuser"))||void 0===t?void 0:t.textContent)||"[deleted]",n=(null===(e=this.mainComment.querySelector(".commtext"))||void 0===e?void 0:e.textContent)||"";return`Comment by ${r}: ${n.trim().slice(0,50)+(n.length>50?"...":"")}`}return(null===(o=null===(n=null===(r=this.mainPost)||void 0===r?void 0:r.querySelector(".titleline"))||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||""}getPostAuthor(){var t,e,r;return(null===(r=null===(e=null===(t=this.mainPost)||void 0===t?void 0:t.querySelector(".hnuser"))||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim())||""}createDescription(){const t=this.getPostTitle(),e=this.getPostAuthor();return this.isCommentPage?`Comment by ${e} on Hacker News`:`${t} - by ${e} on Hacker News`}getPostDate(){if(!this.mainPost)return"";const t=this.mainPost.querySelector(".age");return((null==t?void 0:t.getAttribute("title"))||"").split("T")[0]||""}}e.HackerNewsExtractor=o},552:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isElement=function(t){return t.nodeType===r.ELEMENT_NODE},e.isTextNode=function(t){return t.nodeType===r.TEXT_NODE},e.isCommentNode=function(t){return t.nodeType===r.COMMENT_NODE},e.getComputedStyle=function(t){const e=n(t.ownerDocument);return e?e.getComputedStyle(t):null},e.getWindow=n,e.logDebug=function(t,...e){"undefined"!=typeof window&&window.defuddleDebug};const r={ELEMENT_NODE:1,ATTRIBUTE_NODE:2,TEXT_NODE:3,CDATA_SECTION_NODE:4,ENTITY_REFERENCE_NODE:5,ENTITY_NODE:6,PROCESSING_INSTRUCTION_NODE:7,COMMENT_NODE:8,DOCUMENT_NODE:9,DOCUMENT_TYPE_NODE:10,DOCUMENT_FRAGMENT_NODE:11,NOTATION_NODE:12};function n(t){return t.defaultView?t.defaultView:t.ownerWindow?t.ownerWindow:t.window?t.window:null}},608:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MetadataExtractor=void 0,e.MetadataExtractor=class{static extract(t,e,r){var n,o;let i="",a="";try{if(a=(null===(n=t.location)||void 0===n?void 0:n.href)||"",a||(a=this.getMetaContent(r,"property","og:url")||this.getMetaContent(r,"property","twitter:url")||this.getSchemaProperty(e,"url")||this.getSchemaProperty(e,"mainEntityOfPage.url")||this.getSchemaProperty(e,"mainEntity.url")||this.getSchemaProperty(e,"WebSite.url")||(null===(o=t.querySelector('link[rel="canonical"]'))||void 0===o?void 0:o.getAttribute("href"))||""),a)try{i=new URL(a).hostname.replace(/^www\./,"")}catch(t){}}catch(e){const r=t.querySelector("base[href]");if(r)try{a=r.getAttribute("href")||"",i=new URL(a).hostname.replace(/^www\./,"")}catch(t){}}return{title:this.getTitle(t,e,r),description:this.getDescription(t,e,r),domain:i,favicon:this.getFavicon(t,a,r),image:this.getImage(t,e,r),published:this.getPublished(t,e,r),author:this.getAuthor(t,e,r),site:this.getSite(t,e,r),schemaOrgData:e,wordCount:0,parseTime:0}}static getAuthor(t,e,r){let n;if(n=this.getMetaContent(r,"name","sailthru.author")||this.getMetaContent(r,"property","author")||this.getMetaContent(r,"name","author")||this.getMetaContent(r,"name","byl")||this.getMetaContent(r,"name","authorList"),n)return n;let o=this.getSchemaProperty(e,"author.name")||this.getSchemaProperty(e,"author.[].name");if(o){const t=o.split(",").map((t=>t.trim().replace(/,$/,"").trim())).filter(Boolean);if(t.length>0){let e=[...new Set(t)];return e.length>10&&(e=e.slice(0,10)),e.join(", ")}}const i=[];if(['[itemprop="author"]',".author",'[href*="author"]',".authors a"].forEach((e=>{t.querySelectorAll(e).forEach((t=>{var e;(e=t.textContent)&&e.split(",").forEach((t=>{const e=t.trim().replace(/,$/,"").trim(),r=e.toLowerCase();e&&"author"!==r&&"authors"!==r&&i.push(e)}))}))})),i.length>0){let t=[...new Set(i.map((t=>t.trim())).filter(Boolean))];if(t.length>0)return t.length>10&&(t=t.slice(0,10)),t.join(", ")}return n=this.getMetaContent(r,"name","copyright")||this.getSchemaProperty(e,"copyrightHolder.name")||this.getMetaContent(r,"property","og:site_name")||this.getSchemaProperty(e,"publisher.name")||this.getSchemaProperty(e,"sourceOrganization.name")||this.getSchemaProperty(e,"isPartOf.name")||this.getMetaContent(r,"name","twitter:creator")||this.getMetaContent(r,"name","application-name"),n||""}static getSite(t,e,r){return this.getSchemaProperty(e,"publisher.name")||this.getMetaContent(r,"property","og:site_name")||this.getSchemaProperty(e,"WebSite.name")||this.getSchemaProperty(e,"sourceOrganization.name")||this.getMetaContent(r,"name","copyright")||this.getSchemaProperty(e,"copyrightHolder.name")||this.getSchemaProperty(e,"isPartOf.name")||this.getMetaContent(r,"name","application-name")||this.getAuthor(t,e,r)||""}static getTitle(t,e,r){var n,o;const i=this.getMetaContent(r,"property","og:title")||this.getMetaContent(r,"name","twitter:title")||this.getSchemaProperty(e,"headline")||this.getMetaContent(r,"name","title")||this.getMetaContent(r,"name","sailthru.title")||(null===(o=null===(n=t.querySelector("title"))||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||"";return this.cleanTitle(i,this.getSite(t,e,r))}static cleanTitle(t,e){if(!t||!e)return t;const r=e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),n=[`\\s*[\\|\\-–—]\\s*${r}\\s*$`,`^\\s*${r}\\s*[\\|\\-–—]\\s*`];for(const o of n){const e=new RegExp(o,"i");if(e.test(t)){t=t.replace(e,"");break}}return t.trim()}static getDescription(t,e,r){return this.getMetaContent(r,"name","description")||this.getMetaContent(r,"property","description")||this.getMetaContent(r,"property","og:description")||this.getSchemaProperty(e,"description")||this.getMetaContent(r,"name","twitter:description")||this.getMetaContent(r,"name","sailthru.description")||""}static getImage(t,e,r){return this.getMetaContent(r,"property","og:image")||this.getMetaContent(r,"name","twitter:image")||this.getSchemaProperty(e,"image.url")||this.getMetaContent(r,"name","sailthru.image.full")||""}static getFavicon(t,e,r){var n,o;const i=this.getMetaContent(r,"property","og:image:favicon");if(i)return i;const a=null===(n=t.querySelector("link[rel='icon']"))||void 0===n?void 0:n.getAttribute("href");if(a)return a;const s=null===(o=t.querySelector("link[rel='shortcut icon']"))||void 0===o?void 0:o.getAttribute("href");if(s)return s;if(e)try{return new URL("/favicon.ico",e).href}catch(t){}return""}static getPublished(t,e,r){var n,o;return this.getSchemaProperty(e,"datePublished")||this.getMetaContent(r,"name","publishDate")||this.getMetaContent(r,"property","article:published_time")||(null===(o=null===(n=t.querySelector('abbr[itemprop="datePublished"]'))||void 0===n?void 0:n.title)||void 0===o?void 0:o.trim())||this.getTimeElement(t)||this.getMetaContent(r,"name","sailthru.date")||""}static getMetaContent(t,e,r){var n,o;const i=t.find((t=>{const n="name"===e?t.name:t.property;return(null==n?void 0:n.toLowerCase())===r.toLowerCase()}));return i&&null!==(o=null===(n=i.content)||void 0===n?void 0:n.trim())&&void 0!==o?o:""}static getTimeElement(t){var e,r,n,o;const i=Array.from(t.querySelectorAll("time"))[0];return i&&null!==(o=null!==(r=null===(e=i.getAttribute("datetime"))||void 0===e?void 0:e.trim())&&void 0!==r?r:null===(n=i.textContent)||void 0===n?void 0:n.trim())&&void 0!==o?o:""}static getSchemaProperty(t,e,r=""){if(!t)return r;const n=(t,e,r,o=!0)=>{if("string"==typeof t)return 0===e.length?[t]:[];if(!t||"object"!=typeof t)return[];if(Array.isArray(t)){const i=e[0];if(/^\\[\\d+\\]$/.test(i)){const a=parseInt(i.slice(1,-1));return t[a]?n(t[a],e.slice(1),r,o):[]}return 0===e.length&&t.every((t=>"string"==typeof t||"number"==typeof t))?t.map(String):t.flatMap((t=>n(t,e,r,o)))}const[i,...a]=e;if(!i)return"string"==typeof t?[t]:"object"==typeof t&&t.name?[t.name]:[];if(t.hasOwnProperty(i))return n(t[i],a,r?`${r}.${i}`:i,!0);if(!o){const o=[];for(const i in t)if("object"==typeof t[i]){const a=n(t[i],e,r?`${r}.${i}`:i,!1);o.push(...a)}if(o.length>0)return o}return[]};try{let o=n(t,e.split("."),"",!0);return 0===o.length&&(o=n(t,e.split("."),"",!1)),o.length>0?o.filter(Boolean).join(", "):r}catch(t){return r}}}},610:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.standardizeFootnotes=function(t){const e=t.ownerDocument;e&&new o(e).standardizeFootnotes(t)};const n=r(640);class o{constructor(t){this.doc=t}createFootnoteItem(t,e,r){const n="string"==typeof e?this.doc:e.ownerDocument,o=n.createElement("li");if(o.className="footnote",o.id=`fn:${t}`,"string"==typeof e){const t=n.createElement("p");t.innerHTML=e,o.appendChild(t)}else{const t=Array.from(e.querySelectorAll("p"));if(0===t.length){const t=n.createElement("p");t.innerHTML=e.innerHTML,o.appendChild(t)}else t.forEach((t=>{const e=n.createElement("p");e.innerHTML=t.innerHTML,o.appendChild(e)}))}const i=o.querySelector("p:last-of-type")||o;return r.forEach(((t,e)=>{const o=n.createElement("a");o.href=`#${t}`,o.title="return to article",o.className="footnote-backref",o.innerHTML="↩",e<r.length-1&&(o.innerHTML+=" "),i.appendChild(o)})),o}collectFootnotes(t){const e={};let r=1;const o=new Set;return t.querySelectorAll(n.FOOTNOTE_LIST_SELECTORS).forEach((t=>{if(t.matches('div.footnote[data-component-name="FootnoteToDOM"]')){const n=t.querySelector("a.footnote-number"),i=t.querySelector(".footnote-content");if(n&&i){const t=n.id.replace("footnote-","").toLowerCase();t&&!o.has(t)&&(e[r]={content:i,originalId:t,refs:[]},o.add(t),r++)}}else t.querySelectorAll('li, div[role="listitem"]').forEach((t=>{var n,i,a,s;let l="",c=null;const u=t.querySelector(".citations");if(null===(n=null==u?void 0:u.id)||void 0===n?void 0:n.toLowerCase().startsWith("r")){l=u.id.toLowerCase();const t=u.querySelector(".citation-content");t&&(c=t)}else{if(t.id.toLowerCase().startsWith("bib.bib"))l=t.id.replace("bib.bib","").toLowerCase();else if(t.id.toLowerCase().startsWith("fn:"))l=t.id.replace("fn:","").toLowerCase();else if(t.id.toLowerCase().startsWith("fn"))l=t.id.replace("fn","").toLowerCase();else if(t.hasAttribute("data-counter"))l=(null===(a=null===(i=t.getAttribute("data-counter"))||void 0===i?void 0:i.replace(/\.$/,""))||void 0===a?void 0:a.toLowerCase())||"";else{const e=null===(s=t.id.split("/").pop())||void 0===s?void 0:s.match(/cite_note-(.+)/);l=e?e[1].toLowerCase():t.id.toLowerCase()}c=t}l&&!o.has(l)&&(e[r]={content:c||t,originalId:l,refs:[]},o.add(l),r++)}))})),e}findOuterFootnoteContainer(t){let e=t,r=t.parentElement;for(;r&&("span"===r.tagName.toLowerCase()||"sup"===r.tagName.toLowerCase());)e=r,r=r.parentElement;return e}createFootnoteReference(t,e){const r=this.doc.createElement("sup");r.id=e;const n=this.doc.createElement("a");return n.href=`#fn:${t}`,n.textContent=t,r.appendChild(n),r}standardizeFootnotes(t){const e=this.collectFootnotes(t),r=t.querySelectorAll(n.FOOTNOTE_INLINE_REFERENCES),o=new Map;r.forEach((t=>{var r,n,i,a;if(!t)return;let s="",l="";if(t.matches('a[id^="ref-link"]'))s=(null===(r=t.textContent)||void 0===r?void 0:r.trim())||"";else if(t.matches('a[role="doc-biblioref"]')){const e=t.getAttribute("data-xml-rid");if(e)s=e;else{const e=t.getAttribute("href");(null==e?void 0:e.startsWith("#core-R"))&&(s=e.replace("#core-",""))}}else if(t.matches("a.footnote-anchor, span.footnote-hovercard-target a")){const e=(null===(n=t.id)||void 0===n?void 0:n.replace("footnote-anchor-",""))||"";e&&(s=e.toLowerCase())}else if(t.matches("cite.ltx_cite")){const e=t.querySelector("a");if(e){const t=e.getAttribute("href");if(t){const e=null===(i=t.split("/").pop())||void 0===i?void 0:i.match(/bib\.bib(\d+)/);e&&(s=e[1].toLowerCase())}}}else if(t.matches("sup.reference")){const e=t.querySelectorAll("a");Array.from(e).forEach((t=>{var e;const r=t.getAttribute("href");if(r){const t=null===(e=r.split("/").pop())||void 0===e?void 0:e.match(/(?:cite_note|cite_ref)-(.+)/);t&&(s=t[1].toLowerCase())}}))}else if(t.matches('sup[id^="fnref:"]'))s=t.id.replace("fnref:","").toLowerCase();else if(t.matches('sup[id^="fnr"]'))s=t.id.replace("fnr","").toLowerCase();else if(t.matches("span.footnote-reference"))s=t.getAttribute("data-footnote-id")||"";else if(t.matches("span.footnote-link"))s=t.getAttribute("data-footnote-id")||"",l=t.getAttribute("data-footnote-content")||"";else if(t.matches("a.citation"))s=(null===(a=t.textContent)||void 0===a?void 0:a.trim())||"",l=t.getAttribute("href")||"";else if(t.matches('a[id^="fnref"]'))s=t.id.replace("fnref","").toLowerCase();else{const e=t.getAttribute("href");if(e){const t=e.replace(/^[#]/,"");s=t.toLowerCase()}}if(s){const r=Object.entries(e).find((([t,e])=>e.originalId===s.toLowerCase()));if(r){const[e,n]=r,i=n.refs.length>0?`fnref:${e}-${n.refs.length+1}`:`fnref:${e}`;n.refs.push(i);const a=this.findOuterFootnoteContainer(t);"sup"===a.tagName.toLowerCase()?(o.has(a)||o.set(a,[]),o.get(a).push(this.createFootnoteReference(e,i))):a.replaceWith(this.createFootnoteReference(e,i))}}})),o.forEach(((t,e)=>{if(t.length>0){const r=this.doc.createDocumentFragment();t.forEach((t=>{const e=t.querySelector("a");if(e){const n=this.doc.createElement("sup");n.id=t.id,n.appendChild(e.cloneNode(!0)),r.appendChild(n)}})),e.replaceWith(r)}}));const i=this.doc.createElement("div");i.id="footnotes";const a=this.doc.createElement("ol");Object.entries(e).forEach((([t,e])=>{const r=this.createFootnoteItem(parseInt(t),e.content,e.refs);a.appendChild(r)})),t.querySelectorAll(n.FOOTNOTE_LIST_SELECTORS).forEach((t=>t.remove())),a.children.length>0&&(i.appendChild(a),t.appendChild(i))}}},628:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Defuddle=void 0;const n=r(608),o=r(917),i=r(640),a=r(840),s=r(968),l=r(552);e.Defuddle=class{constructor(t,e={}){this.doc=t,this.options=e,this.debug=e.debug||!1}parse(){const t=this.parseInternal();if(t.wordCount<200){const e=this.parseInternal({removePartialSelectors:!1});if(e.wordCount>t.wordCount)return this._log("Retry produced more content"),e}return t}parseInternal(t={}){var e,r,i;const l=Date.now(),c=Object.assign(Object.assign({removeExactSelectors:!0,removePartialSelectors:!0},this.options),t),u=this._extractSchemaOrgData(this.doc),d=[];this.doc.querySelectorAll("meta").forEach((t=>{const e=t.getAttribute("name"),r=t.getAttribute("property");let n=t.getAttribute("content");n&&d.push({name:e,property:r,content:this._decodeHTMLEntities(n)})}));const f=n.MetadataExtractor.extract(this.doc,u,d);try{const t=c.url||this.doc.URL,n=o.ExtractorRegistry.findExtractor(this.doc,t,u);if(n&&n.canExtract()){const t=n.extract(),o=Date.now();return{content:t.contentHtml,title:(null===(e=t.variables)||void 0===e?void 0:e.title)||f.title,description:f.description,domain:f.domain,favicon:f.favicon,image:f.image,published:(null===(r=t.variables)||void 0===r?void 0:r.published)||f.published,author:(null===(i=t.variables)||void 0===i?void 0:i.author)||f.author,site:f.site,schemaOrgData:f.schemaOrgData,wordCount:this.countWords(t.contentHtml),parseTime:Math.round(o-l),extractorType:n.constructor.name.replace("Extractor","").toLowerCase(),metaTags:d}}const h=this._evaluateMediaQueries(this.doc),p=this.findSmallImages(this.doc),m=this.doc.cloneNode(!0);this.applyMobileStyles(m,h);const g=this.findMainContent(m);if(!g){const t=Date.now();return Object.assign(Object.assign({content:this.doc.body.innerHTML},f),{wordCount:this.countWords(this.doc.body.innerHTML),parseTime:Math.round(t-l),metaTags:d})}this.removeSmallImages(m,p),this.removeHiddenElements(m),s.ContentScorer.scoreAndRemove(m,this.debug),(c.removeExactSelectors||c.removePartialSelectors)&&this.removeBySelector(m,c.removeExactSelectors,c.removePartialSelectors),(0,a.standardizeContent)(g,f,this.doc,this.debug);const v=g.outerHTML,y=Date.now();return Object.assign(Object.assign({content:v},f),{wordCount:this.countWords(v),parseTime:Math.round(y-l),metaTags:d})}catch(t){const e=Date.now();return Object.assign(Object.assign({content:this.doc.body.innerHTML},f),{wordCount:this.countWords(this.doc.body.innerHTML),parseTime:Math.round(e-l),metaTags:d})}}countWords(t){const e=this.doc.createElement("div");return e.innerHTML=t,(e.textContent||"").trim().replace(/\s+/g," ").split(" ").filter((t=>t.length>0)).length}_log(...t){this.debug}_evaluateMediaQueries(t){const e=[],r=/max-width[^:]*:\s*(\d+)/;try{Array.from(t.styleSheets).filter((t=>{try{return t.cssRules,!0}catch(t){return t instanceof DOMException&&t.name,!1}})).flatMap((t=>{try{return"undefined"==typeof CSSMediaRule?[]:Array.from(t.cssRules).filter((t=>t instanceof CSSMediaRule&&t.conditionText.includes("max-width")))}catch(t){return this.debug,[]}})).forEach((t=>{const n=t.conditionText.match(r);if(n){const r=parseInt(n[1]);i.MOBILE_WIDTH<=r&&Array.from(t.cssRules).filter((t=>t instanceof CSSStyleRule)).forEach((t=>{try{e.push({selector:t.selectorText,styles:t.style.cssText})}catch(t){this.debug}}))}}))}catch(t){}return e}applyMobileStyles(t,e){e.forEach((({selector:e,styles:r})=>{try{t.querySelectorAll(e).forEach((t=>{t.setAttribute("style",(t.getAttribute("style")||"")+r)}))}catch(t){}}))}removeHiddenElements(t){let e=0;const r=new Set,n=Array.from(t.getElementsByTagName("*"));for(let o=0;o<n.length;o+=100){const i=n.slice(o,o+100),a=i.map((e=>{var r,n;try{return null===(r=e.ownerDocument.defaultView)||void 0===r?void 0:r.getComputedStyle(e)}catch(r){const o=e.getAttribute("style");if(!o)return null;const i=t.createElement("style");i.textContent=`* { ${o} }`,t.head.appendChild(i);const a=null===(n=e.ownerDocument.defaultView)||void 0===n?void 0:n.getComputedStyle(e);return t.head.removeChild(i),a}}));i.forEach(((t,n)=>{const o=a[n];!o||"none"!==o.display&&"hidden"!==o.visibility&&"0"!==o.opacity||(r.add(t),e++)}))}this._log("Removed hidden elements:",e)}removeBySelector(t,e=!0,r=!0){const n=Date.now();let o=0,a=0;const s=new Set;if(e&&t.querySelectorAll(i.EXACT_SELECTORS.join(",")).forEach((t=>{(null==t?void 0:t.parentNode)&&(s.add(t),o++)})),r){const e=i.PARTIAL_SELECTORS.join("|"),r=new RegExp(e,"i"),n=i.TEST_ATTRIBUTES.map((t=>`[${t}]`)).join(",");t.querySelectorAll(n).forEach((t=>{if(s.has(t))return;const e=i.TEST_ATTRIBUTES.map((e=>"class"===e?t.className&&"string"==typeof t.className?t.className:"":"id"===e?t.id||"":t.getAttribute(e)||"")).join(" ").toLowerCase();e.trim()&&r.test(e)&&(s.add(t),a++)}))}s.forEach((t=>t.remove()));const l=Date.now();this._log("Removed clutter elements:",{exactSelectors:o,partialSelectors:a,total:s.size,processingTime:`${(l-n).toFixed(2)}ms`})}findSmallImages(t){const e=new Set,r=/scale\(([\d.]+)\)/,n=Date.now();let o=0;const i=[...Array.from(t.getElementsByTagName("img")),...Array.from(t.getElementsByTagName("svg"))];if(0===i.length)return e;const a=i.map((t=>({element:t,naturalWidth:"img"===t.tagName.toLowerCase()&&parseInt(t.getAttribute("width")||"0")||0,naturalHeight:"img"===t.tagName.toLowerCase()&&parseInt(t.getAttribute("height")||"0")||0,attrWidth:parseInt(t.getAttribute("width")||"0"),attrHeight:parseInt(t.getAttribute("height")||"0")})));for(let l=0;l<a.length;l+=50){const n=a.slice(l,l+50);try{const t=n.map((({element:t})=>{var e;try{return null===(e=t.ownerDocument.defaultView)||void 0===e?void 0:e.getComputedStyle(t)}catch(t){return null}})),i=n.map((({element:t})=>{try{return t.getBoundingClientRect()}catch(t){return null}}));n.forEach(((n,a)=>{var s;try{const l=t[a],c=i[a];if(!l)return;const u=l.transform,d=u?parseFloat((null===(s=u.match(r))||void 0===s?void 0:s[1])||"1"):1,f=[n.naturalWidth,n.attrWidth,parseInt(l.width)||0,c?c.width*d:0].filter((t=>"number"==typeof t&&t>0)),h=[n.naturalHeight,n.attrHeight,parseInt(l.height)||0,c?c.height*d:0].filter((t=>"number"==typeof t&&t>0));if(f.length>0&&h.length>0){const t=Math.min(...f),r=Math.min(...h);if(t<33||r<33){const t=this.getElementIdentifier(n.element);t&&(e.add(t),o++)}}}catch(t){this.debug}}))}catch(t){this.debug}}const s=Date.now();return this._log("Found small elements:",{count:o,processingTime:`${(s-n).toFixed(2)}ms`}),e}removeSmallImages(t,e){let r=0;["img","svg"].forEach((n=>{const o=t.getElementsByTagName(n);Array.from(o).forEach((t=>{const n=this.getElementIdentifier(t);n&&e.has(n)&&(t.remove(),r++)}))})),this._log("Removed small elements:",r)}getElementIdentifier(t){if("img"===t.tagName.toLowerCase()){const e=t.getAttribute("data-src");if(e)return`src:${e}`;const r=t.getAttribute("src")||"",n=t.getAttribute("srcset")||"",o=t.getAttribute("data-srcset");if(r)return`src:${r}`;if(n)return`srcset:${n}`;if(o)return`srcset:${o}`}const e=t.id||"",r=t.className||"",n="svg"===t.tagName.toLowerCase()&&t.getAttribute("viewBox")||"";return e?`id:${e}`:n?`viewBox:${n}`:r?`class:${r}`:null}findMainContent(t){const e=[];if(i.ENTRY_POINT_ELEMENTS.forEach(((r,n)=>{t.querySelectorAll(r).forEach((t=>{let r=40*(i.ENTRY_POINT_ELEMENTS.length-n);r+=s.ContentScorer.scoreElement(t),e.push({element:t,score:r})}))})),0===e.length)return this.findContentByScoring(t);if(e.sort(((t,e)=>e.score-t.score)),this.debug&&this._log("Content candidates:",e.map((t=>({element:t.element.tagName,selector:this.getElementSelector(t.element),score:t.score})))),1===e.length&&"body"===e[0].element.tagName.toLowerCase()){const e=this.findTableBasedContent(t);if(e)return e}return e[0].element}findTableBasedContent(t){if(!Array.from(t.getElementsByTagName("table")).some((t=>{const e=parseInt(t.getAttribute("width")||"0"),r=this.getComputedStyle(t);return e>400||(null==r?void 0:r.width.includes("px"))&&parseInt(r.width)>400||"center"===t.getAttribute("align")||t.className.toLowerCase().includes("content")||t.className.toLowerCase().includes("article")})))return null;const e=Array.from(t.getElementsByTagName("td"));return s.ContentScorer.findBestElement(e)}findContentByScoring(t){const e=[];return i.BLOCK_ELEMENTS.forEach((r=>{Array.from(t.getElementsByTagName(r)).forEach((t=>{const r=s.ContentScorer.scoreElement(t);r>0&&e.push({score:r,element:t})}))})),e.length>0?e.sort(((t,e)=>e.score-t.score))[0].element:null}getElementSelector(t){const e=[];let r=t;for(;r&&r!==this.doc.documentElement;){let t=r.tagName.toLowerCase();r.id?t+="#"+r.id:r.className&&"string"==typeof r.className&&(t+="."+r.className.trim().split(/\s+/).join(".")),e.unshift(t),r=r.parentElement}return e.join(" > ")}getComputedStyle(t){return(0,l.getComputedStyle)(t)}_extractSchemaOrgData(t){const e=t.querySelectorAll('script[type="application/ld+json"]'),r=[];e.forEach((t=>{let e=t.textContent||"";try{e=e.replace(/\/\*[\s\S]*?\*\/|^\s*\/\/.*$/gm,"").replace(/^\s*<!\[CDATA\[([\s\S]*?)\]\]>\s*$/,"$1").replace(/^\s*(\*\/|\/\*)\s*|\s*(\*\/|\/\*)\s*$/g,"").trim();const t=JSON.parse(e);t["@graph"]&&Array.isArray(t["@graph"])?r.push(...t["@graph"]):r.push(t)}catch(t){this.debug}}));const n=t=>{if("string"==typeof t)return this._decodeHTMLEntities(t);if(Array.isArray(t))return t.map(n);if("object"==typeof t&&null!==t){const e={};for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=n(t[r]));return e}return t};return r.map(n)}_decodeHTMLEntities(t){const e=this.doc.createElement("textarea");return e.innerHTML=t,e.value}}},632:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ChatGPTExtractor=void 0;const n=r(181);class o extends n.ConversationExtractor{constructor(t,e){super(t,e),this.articles=t.querySelectorAll('article[data-testid^="conversation-turn-"]'),this.footnotes=[],this.footnoteCounter=0}canExtract(){return!!this.articles&&this.articles.length>0}extractMessages(){const t=[];return this.footnotes=[],this.footnoteCounter=0,this.articles?(this.articles.forEach((e=>{var r,n;const o=e.querySelector("h5.sr-only, h6.sr-only"),i=(null===(n=null===(r=null==o?void 0:o.textContent)||void 0===r?void 0:r.trim())||void 0===n?void 0:n.replace(/:\s*$/,""))||"";let a="";const s=e.getAttribute("data-message-author-role");s&&(a=s);let l=e.innerHTML||"";l=l.replace(/\u200B/g,"");const c=document.createElement("div");c.innerHTML=l,c.querySelectorAll('h5.sr-only, h6.sr-only, span[data-state="closed"]').forEach((t=>t.remove())),l=c.innerHTML,l=l.replace(/(&ZeroWidthSpace;)?(<span[^>]*?>\s*<a(?=[^>]*?href="([^"]+)")(?=[^>]*?target="_blank")(?=[^>]*?rel="noopener")[^>]*?>[\s\S]*?<\/a>\s*<\/span>)/gi,((t,e,r,n)=>{let o="",i="";try{o=new URL(n).hostname.replace(/^www\./,"");const t=n.split("#:~:text=");if(t.length>1){i=decodeURIComponent(t[1]),i=i.replace(/%2C/g,",");const e=i.split(",");i=e.length>1&&e[0].trim()?` — ${e[0].trim()}...`:e[0].trim()?` — ${i.trim()}`:""}}catch(t){o=n}let a,s=this.footnotes.findIndex((t=>t.url===n));return-1===s?(this.footnoteCounter++,a=this.footnoteCounter,this.footnotes.push({url:n,text:`<a href="${n}">${o}</a>${i}`})):a=s+1,`<sup id="fnref:${a}"><a href="#fn:${a}">${a}</a></sup>`})),l=l.replace(/<p[^>]*>\s*<\/p>/g,""),t.push({author:i,content:l.trim(),metadata:{role:a||"unknown"}})})),t):t}getFootnotes(){return this.footnotes}getMetadata(){const t=this.getTitle(),e=this.extractMessages();return{title:t,site:"ChatGPT",url:this.url,messageCount:e.length,description:`ChatGPT conversation with ${e.length} messages`}}getTitle(){var t,e,r;const n=null===(t=this.document.title)||void 0===t?void 0:t.trim();if(n&&"ChatGPT"!==n)return n;const o=null===(r=null===(e=this.articles)||void 0===e?void 0:e.item(0))||void 0===r?void 0:r.querySelector(".text-message");if(o){const t=o.textContent||"";return t.length>50?t.slice(0,50)+"...":t}return"ChatGPT Conversation"}}e.ChatGPTExtractor=o},640:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ALLOWED_ATTRIBUTES_DEBUG=e.ALLOWED_ATTRIBUTES=e.ALLOWED_EMPTY_ELEMENTS=e.FOOTNOTE_LIST_SELECTORS=e.FOOTNOTE_INLINE_REFERENCES=e.PARTIAL_SELECTORS=e.TEST_ATTRIBUTES=e.EXACT_SELECTORS=e.INLINE_ELEMENTS=e.PRESERVE_ELEMENTS=e.BLOCK_ELEMENTS=e.MOBILE_WIDTH=e.ENTRY_POINT_ELEMENTS=void 0,e.ENTRY_POINT_ELEMENTS=["#post",".post-content",".article-content","#article-content",".article_post",".article-wrapper",".entry-content",".content-article",".post",".markdown-body","article",'[role="article"]',"main",'[role="main"]',"body"],e.MOBILE_WIDTH=600,e.BLOCK_ELEMENTS=["div","section","article","main","aside","header","footer","nav","content"],e.PRESERVE_ELEMENTS=new Set(["pre","code","table","thead","tbody","tr","td","th","ul","ol","li","dl","dt","dd","figure","figcaption","picture","details","summary","blockquote","form","fieldset"]),e.INLINE_ELEMENTS=new Set(["a","span","strong","em","i","b","u","code","br","small","sub","sup","mark","date","del","ins","q","abbr","cite","relative-time","time","font"]),e.EXACT_SELECTORS=["noscript",'script:not([type^="math/"])',"style","meta","link",'.ad:not([class*="gradient"])','[class^="ad-" i]','[class$="-ad" i]','[id^="ad-" i]','[id$="-ad" i]','[role="banner" i]','[alt*="advert" i]',".promo",".Promo","#barrier-page",".alert",'[id="comments" i]','[id="comment" i]',"header",".header:not(.banner)","#header","#Header","#banner","#Banner","nav",".navigation","#navigation",".hero",'[role="navigation" i]','[role="dialog" i]','[role*="complementary" i]','[class*="pagination" i]',".menu","#menu","#siteSub",".previous",".author",".Author",'[class$="_bio"]',"#categories",".contributor",".date","#date","[data-date]",".entry-meta",".meta",".tags","#tags",".toc",".Toc","#toc",".headline","#headline","#title","#Title","#articleTag",'[href*="/category"]','[href*="/categories"]','[href*="/tag/"]','[href*="/tags/"]','[href*="/topics"]','[href*="author"]','[href*="#toc"]','[href="#top"]','[href="#Top"]','[href="#page-header"]','[href="#content"]','[href="#site-content"]','[href="#main-content"]','[href^="#main"]','[src*="author"]',"footer",".aside","aside","button","canvas","date","dialog","fieldset","form",'input:not([type="checkbox"])',"label","option","select","textarea","time","relative-time","[hidden]",'[aria-hidden="true"]:not([class*="math"])','[style*="display: none"]:not([class*="math"])','[style*="display:none"]:not([class*="math"])','[style*="visibility: hidden"]','[style*="visibility:hidden"]',".hidden",".invisible","instaread-player",'iframe:not([src*="youtube"]):not([src*="youtu.be"]):not([src*="vimeo"]):not([src*="twitter"]):not([src*="x.com"]):not([src*="datawrapper"])','[class="logo" i]',"#logo","#Logo","#newsletter","#Newsletter",".subscribe",".noprint",'[data-print-layout="hide" i]','[data-block="donotprint" i]','[class*="clickable-icon" i]','li span[class*="ltx_tag" i][class*="ltx_tag_item" i]','a[href^="#"][class*="anchor" i]','a[href^="#"][class*="ref" i]','[data-container*="most-viewed" i]',".sidebar",".Sidebar","#sidebar","#Sidebar","#sitesub",'[data-link-name*="skip" i]','[aria-label*="skip" i]',"#skip-link",".copyright","#copyright","#rss","#feed",".gutter","#primaryaudio","#NYT_ABOVE_MAIN_CONTENT_REGION",'[data-testid="photoviewer-children-figure"] > span',"table.infobox",".pencraft:not(.pc-display-contents)",'[data-optimizely="related-articles-section" i]','[data-orientation="vertical"]'],e.TEST_ATTRIBUTES=["class","id","data-test","data-testid","data-test-id","data-qa","data-cy"],e.PARTIAL_SELECTORS=["a-statement","access-wall","activitypub","actioncall","addcomment","advert","adlayout","ad-tldr","ad-placement","ads-container","_ad_","after_content","after_main_article","afterpost","allterms","-alert-","alert-box","appendix","_archive","around-the-web","aroundpages","article-author","article-badges","article-banner","article-bottom-section","article-bottom","article-category","article-card","article-citation","article__copy","article_date","article-date","article-end ","article_header","article-header","article__header","article__hero","article__info","article-info","article-meta","article_meta","article__meta","articlename","article-subject","article_subject","article-snippet","article-separator","article--share","article--topics","articletags","article-tags","article_tags","articletitle","article-title","article_title","articletopics","article-topics","article--lede","articlewell","associated-people","audio-card","author-bio","author-box","author-info","author_info","authorm","author-mini-bio","author-name","author-publish-info","authored-by","avatar","back-to-top","backlink_container","backlinks-section","bio-block","biobox","blog-pager","bookmark-","-bookmark","bottominfo","bottomnav","bottom-of-article","bottom-wrapper","brand-bar","breadcrumb","brdcrumb","button-wrapper","buttons-container","btn-","-btn","byline","captcha","card-text","card-media","card-post","carouselcontainer","carousel-container","cat_header","catlinks","_categories","card-author","card-content","chapter-list","collections","comments","commentbox","comment-button","commentcomp","comment-content","comment-count","comment-form","comment-number","comment-respond","comment-thread","comment-wrap","complementary","consent","contact-","content-card","content-topics","contentpromo","context-bar","context-widget","core-collateral","cover-","created-date","creative-commons_","c-subscribe","_cta","-cta","cta-","cta_","current-issue","custom-list-number","dateline","dateheader","date-header","date-pub","disclaimer","disclosure","discussion","discuss_","disqus","donate","donation","dropdown","eletters","emailsignup","engagement-widget","enhancement","entry-author-info","entry-categories","entry-date","entry-title","entry-utility","-error","error-","eyebrow","expand-reduce","external-anchor","externallinkembedwrapper","extra-services","extra-title","facebook","fancy-box","favorite","featured-content","feature_feed","feedback","feed-links","field-site-sections","fixheader","floating-vid","follower","footer","footnote-back","footnoteback","form-group","for-you","frontmatter","further-reading","fullbleedheader","gated-","gh-feed","gist-meta","goog-","graph-view","hamburger","header_logo","header-logo","header-pattern","hero-list","hide-for-print","hide-print","hide-when-no-script","hidden-print","hidden-sidenote","hidden-accessibility","infoline","instacartIntegration","interlude","interaction","itemendrow","invisible","jumplink","jump-to-","keepreading","keep-reading","keep_reading","keyword_wrap","kicker","labstab","-labels","language-name","lastupdated","latest-content","-ledes-","-license","license-","lightbox-popup","like-button","link-box","links-grid","links-title","listing-dynamic-terms","list-tags","listinks","loading","loa-info","logo_container","ltx_role_refnum","ltx_tag_bibitem","ltx_error","masthead","marketing","media-inquiry","-menu","menu-","metadata","might-like","minibio","more-about","_modal","-modal","more-","morenews","morestories","more_wrapper","most-read","move-helper","mw-editsection","mw-cite-backlink","mw-indicators","mw-jump-link","nav-","nav_","navigation-post","next-","newsgallery","news-story-title","newsletter_","newsletterbanner","newslettercontainer","newsletter-form","newsletter-signup","newslettersignup","newsletterwidget","newsletterwrapper","not-found","notessection","nomobile","noprint","open-slideshow","originally-published","other-blogs","outline-view","pagehead","page-header","page-title","paywall_message","-partners","permission-","plea","popular","popup_links","pop_stories","pop-up","post-author","post-bottom","post__category","postcomment","postdate","post-date","post_date","post-details","post-feeds","postinfo","post-info","post_info","post-inline-date","post-links","postlist","post_list","post_meta","post-meta","postmeta","post_more","postnavi","post-navigation","postpath","post-preview","postsnippet","post_snippet","post-snippet","post-subject","posttax","post-tax","post_tax","posttag","post_tag","post-tag","post_time","posttitle","post-title","post_title","post__title","post-ufi-button","prev-post","prevnext","prev_next","prev-next","previousnext","press-inquiries","print-none","print-header","print:hidden","privacy-notice","privacy-settings","profile","promo_article","promo-bar","promo-box","pubdate","pub_date","pub-date","publish_date","publish-date","publication-date","publicationName","qr-code","qr_code","quick_up","_rail","ratingssection","read_also","readmore","read-next","read_next","read_time","read-time","reading_time","reading-time","reading-list","recent-","recent-articles","recentpost","recent_post","recent-post","recommend","redirectedfrom","recirc","register","related","relevant","reversefootnote","_rss","rss-link","screen-reader-text","scroll_to","scroll-to","_search","-search","section-nav","series-banner","share-box","sharedaddy","share-icons","sharelinks","share-post","share-print","share-section","show-for-print","sidebartitle","sidebar-content","sidebar-wrapper","sideitems","sidebar-author","sidebar-item","side-box","side-logo","sign-in-gate","similar-","similar_","similars-","site-index","site-header","siteheader","site-logo","site-name","site-wordpress","skip-content","skip-to-content","c-skip-link","_skip-link","-slider","slug-wrap","social-author","social-shar","social-date","speechify-ignore","speedbump","sponsor","springercitation","sr-only","_stats","story-date","story-navigation","storyreadtime","storysmall","storypublishdate","subject-label","subhead","submenu","-subscribe-","subscriber-drive","subscription-","_tags","tags__item","tag_list","taxonomy","table-of-contents","tabs-","terminaltout","time-rubric","timestamp","time-read","time-to-read","tip_off","tiptout","-tout-","toc-container","toggle-caption","tooltip","topbar","topic-list","topic-subnav","top-wrapper","tree-item","trending","trust-feat","trust-badge","trust-project","twitter","u-hide","upsell","viewbottom","visually-hidden","welcomebox","widget_pages"],e.FOOTNOTE_INLINE_REFERENCES=["sup.reference","cite.ltx_cite",'sup[id^="fnr"]','span[id^="fnr"]','span[class*="footnote_ref"]',"span.footnote-link","a.citation",'a[id^="ref-link"]','a[href^="#fn"]','a[href^="#cite"]','a[href^="#reference"]','a[href^="#footnote"]','a[href^="#r"]','a[href^="#b"]','a[href*="cite_note"]','a[href*="cite_ref"]',"a.footnote-anchor","span.footnote-hovercard-target a",'a[role="doc-biblioref"]','a[id^="fnref"]','a[id^="ref-link"]'].join(","),e.FOOTNOTE_LIST_SELECTORS=["div.footnote ol","div.footnotes ol",'div[role="doc-endnotes"]','div[role="doc-footnotes"]',"ol.footnotes-list","ol.footnotes","ol.references",'ol[class*="article-references"]',"section.footnotes ol",'section[role="doc-endnotes"]','section[role="doc-footnotes"]','section[role="doc-bibliography"]',"ul.footnotes-list","ul.ltx_biblist",'div.footnote[data-component-name="FootnoteToDOM"]'].join(","),e.ALLOWED_EMPTY_ELEMENTS=new Set(["area","audio","base","br","circle","col","defs","ellipse","embed","figure","g","hr","iframe","img","input","line","link","mask","meta","object","param","path","pattern","picture","polygon","polyline","rect","source","stop","svg","td","th","track","use","video","wbr"]),e.ALLOWED_ATTRIBUTES=new Set(["alt","allow","allowfullscreen","aria-label","checked","colspan","controls","data-latex","data-src","data-srcset","data-lang","dir","display","frameborder","headers","height","href","lang","role","rowspan","src","srcset","title","type","width","accent","accentunder","align","columnalign","columnlines","columnspacing","columnspan","data-mjx-texclass","depth","displaystyle","fence","frame","framespacing","linethickness","lspace","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","rowalign","rowlines","rowspacing","rowspan","rspace","scriptlevel","separator","stretchy","symmetric","voffset","xmlns"]),e.ALLOWED_ATTRIBUTES_DEBUG=new Set(["class","id"])},649:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.imageRules=void 0;const n=r(552),o=/^data:image\/([^;]+);base64,/,i=/\.(jpg|jpeg|png|webp)\s+\d/,a=/^\s*\S+\.(jpg|jpeg|png|webp)\S*\s*$/,s=/\.(jpg|jpeg|png|webp|gif|avif)(\?.*)?$/i,l=/\s(\d+)w/,c=/dpr=(\d+(?:\.\d+)?)/,u=/^([^\s]+)/,d=/^[\w\-\.\/\\]+\.(jpg|jpeg|png|gif|webp|svg)$/i,f=/^\d{4}-\d{2}-\d{2}$/;function h(t,e,r){const o=r.createElement("figure");o.appendChild(t.cloneNode(!0));const i=r.createElement("figcaption"),a=function(t){const e=[],r=new Set,o=t=>{var i;if((0,n.isTextNode)(t)){const n=(null===(i=t.textContent)||void 0===i?void 0:i.trim())||"";n&&!r.has(n)&&(e.push(n),r.add(n))}else if((0,n.isElement)(t)){const e=t.childNodes;for(let t=0;t<e.length;t++)o(e[t])}},i=t.childNodes;for(let n=0;n<i.length;n++)o(i[n]);return e.length>0?e.join(" "):t.innerHTML}(e);return i.innerHTML=a,o.appendChild(i),o}function p(t,e){e.setAttribute("srcset",t);const r=A(t);r&&y(r)&&e.setAttribute("src",r)}function m(t,e,r){for(let n=0;n<t.attributes.length;n++){const o=t.attributes[n];r.includes(o.name)||e.setAttribute(o.name,o.value)}}function g(t){const e=t.match(o);if(!e)return!1;if("svg+xml"===e[1])return!1;const r=e[0].length;return t.length-r<133}function v(t){return t.startsWith("data:image/svg+xml")}function y(t){return!t.startsWith("data:")&&!(!t||""===t.trim())&&(s.test(t)||t.includes("image")||t.includes("img")||t.includes("photo"))}function b(t){return!!E(t)||t.querySelectorAll("img, video, picture, source").length>0}function E(t){const e=t.tagName.toLowerCase();return"img"===e||"video"===e||"picture"===e||"source"===e}function w(t){if(E(t))return t;const e=t.querySelectorAll("picture");if(e.length>0)return e[0];const r=t.querySelectorAll("img"),n=[];for(let s=0;s<r.length;s++){const t=r[s],e=t.getAttribute("src")||"",o=t.getAttribute("alt")||"";e.includes("data:image/svg+xml")||g(e)||!o.trim()&&r.length>1||n.push(t)}if(n.length>0)return n[0];const o=t.querySelectorAll("video");if(o.length>0)return o[0];const i=t.querySelectorAll("source");if(i.length>0)return i[0];const a=t.querySelectorAll("img, picture, source, video");return a.length>0?a[0]:null}function S(t){var e,r,n,o;const i=t.querySelector("figcaption");if(i)return i;const a=new Set,s=['[class*="caption"]','[class*="description"]','[class*="alt"]','[class*="title"]','[class*="credit"]','[class*="text"]','[class*="post-thumbnail-text"]','[class*="image-caption"]','[class*="photo-caption"]',"[aria-label]","[title]"].join(", "),l=t.querySelectorAll(s);for(let d=0;d<l.length;d++){const t=l[d];if(E(t))continue;const r=null===(e=t.textContent)||void 0===e?void 0:e.trim();if(r&&r.length>0&&!a.has(r))return a.add(r),t}const c=t.querySelector("img");if(c&&c.hasAttribute("alt")){const e=c.getAttribute("alt");if(e&&e.trim().length>0){const r=t.ownerDocument.createElement("div");return r.textContent=e,r}}if(t.parentElement){const e=t.parentElement.children;for(let n=0;n<e.length;n++){const o=e[n];if(o!==t&&Array.from(o.classList).some((t=>t.includes("caption")||t.includes("credit")||t.includes("text")||t.includes("description")))){const t=null===(r=o.textContent)||void 0===r?void 0:r.trim();if(t&&t.length>0)return o}}}const u=t.querySelectorAll("img");for(let d=0;d<u.length;d++){const t=u[d];if(!t.parentElement)continue;let e=t.nextElementSibling;for(;e;){if(["EM","STRONG","SPAN","I","B","SMALL","CITE"].includes(e.tagName)){const t=null===(n=e.textContent)||void 0===n?void 0:n.trim();if(t&&t.length>0)return e}e=e.nextElementSibling}}for(let d=0;d<u.length;d++){const t=u[d],e=t.parentElement;if(!e)continue;const r=e.querySelectorAll("em, strong, span, i, b, small, cite");for(let n=0;n<r.length;n++){const e=r[n];if(e===t)continue;const i=null===(o=e.textContent)||void 0===o?void 0:o.trim();if(i&&i.length>0)return e}}return null}function C(t){var e;const r=(null===(e=t.textContent)||void 0===e?void 0:e.trim())||"";return!(r.length<10||r.startsWith("http://")||r.startsWith("https://")||d.test(r)||r.match(/^\d+$/)||f.test(r))}function T(t,e){const r=t.tagName.toLowerCase();if("img"===r)return _(t,e);if("picture"===r){const r=t.querySelector("img");return r?_(r,e):t.cloneNode(!0)}return"source"===r?function(t,e){const r=e.createElement("img"),n=t.getAttribute("srcset");n&&p(n,r);const o=t.parentElement;if(o){const t=o.querySelectorAll("img"),e=[];for(let r=0;r<t.length;r++){const n=t[r],o=n.getAttribute("src")||"";g(o)||v(o)||""===o||e.push(n)}if(e.length>0){if(m(e[0],r,["src","srcset"]),!r.hasAttribute("src")||!y(r.getAttribute("src")||"")){const t=e[0].getAttribute("src");t&&y(t)&&r.setAttribute("src",t)}}else{const t=o.querySelector("img[data-src]");if(t&&(m(t,r,["src","srcset"]),!r.hasAttribute("src")||!y(r.getAttribute("src")||""))){const e=t.getAttribute("data-src");e&&y(e)&&r.setAttribute("src",e)}}}return r}(t,e):t.cloneNode(!0)}function _(t,e){const r=t.getAttribute("src")||"";if(g(r)||v(r)){const r=t.parentElement;if(r){const n=r.querySelectorAll("source"),o=[];for(let t=0;t<n.length;t++){const e=n[t];e.hasAttribute("data-srcset")&&""!==e.getAttribute("data-srcset")&&o.push(e)}if(o.length>0){const r=e.createElement("img"),n=t.getAttribute("data-src");return n&&!v(n)&&r.setAttribute("src",n),m(t,r,["src"]),r}}}return t.cloneNode(!0)}function A(t){const e=t.split(",");if(0===e.length)return null;const r=e[0].trim().match(u);if(r&&r[1]){const t=r[1];if(v(t)){for(let t=1;t<e.length;t++){const r=e[t].trim().match(u);if(r&&r[1]&&!v(r[1]))return r[1]}return null}return t}return null}function x(t){if(0===t.length)return null;if(1===t.length)return t[0];for(let n=0;n<t.length;n++)if(!t[n].hasAttribute("media"))return t[n];let e=null,r=0;for(let n=0;n<t.length;n++){const o=t[n],i=o.getAttribute("srcset");if(!i)continue;const a=i.match(l),s=i.match(c);if(a&&a[1]){const t=parseInt(a[1],10)*(s?parseFloat(s[1]):1);t>r&&(r=t,e=o)}}return e||t[0]}e.imageRules=[{selector:"picture",element:"picture",transform:(t,e)=>{const r=t.querySelectorAll("source"),n=t.querySelector("img");if(!n){const n=x(r);if(n){const r=n.getAttribute("srcset");if(r){const n=e.createElement("img");return p(r,n),t.innerHTML="",t.appendChild(n),t}}return t}let o=null,i=null;if(r.length>0){const t=x(r);t&&(o=t.getAttribute("srcset"),o&&(i=A(o)))}if(o&&n.setAttribute("srcset",o),i&&y(i))n.setAttribute("src",i);else if(!n.hasAttribute("src")||!y(n.getAttribute("src")||"")){const t=A(n.getAttribute("srcset")||o||"");t&&y(t)&&n.setAttribute("src",t)}return r.forEach((t=>t.remove())),t}},{selector:"uni-image-full-width",element:"figure",transform:(t,e)=>{var r;const n=e.createElement("figure"),o=e.createElement("img"),i=t.querySelector("img");if(!i)return n;let a=i.getAttribute("src");const s=i.getAttribute("data-loading");if(s)try{const t=JSON.parse(s);t.desktop&&y(t.desktop)&&(a=t.desktop)}catch(t){}if(!a||!y(a))return n;o.setAttribute("src",a);let l=i.getAttribute("alt");l||(l=t.getAttribute("alt-text")),l&&o.setAttribute("alt",l),n.appendChild(o);const c=t.querySelector("figcaption");if(c){const t=null===(r=c.textContent)||void 0===r?void 0:r.trim();if(t&&t.length>5){const r=e.createElement("figcaption"),o=c.querySelector(".rich-text p");o?r.innerHTML=o.innerHTML:r.textContent=t,n.appendChild(r)}}return n}},{selector:'img[data-src], img[data-srcset], img[loading="lazy"], img.lazy, img.lazyload',element:"img",transform:(t,e)=>{const r=t.getAttribute("src")||"",n=function(t){if(t.hasAttribute("data-src")||t.hasAttribute("data-srcset"))return!0;for(let e=0;e<t.attributes.length;e++){const r=t.attributes[e];if("src"!==r.name){if(r.name.startsWith("data-")&&/\.(jpg|jpeg|png|webp|gif)(\?.*)?$/i.test(r.value))return!0;if(/\.(jpg|jpeg|png|webp|gif)(\?.*)?$/i.test(r.value))return!0}}return!1}(t);g(r)&&n&&t.removeAttribute("src");const o=t.getAttribute("data-src");o&&!t.getAttribute("src")&&t.setAttribute("src",o);const s=t.getAttribute("data-srcset");s&&!t.getAttribute("srcset")&&t.setAttribute("srcset",s);for(let l=0;l<t.attributes.length;l++){const e=t.attributes[l];"src"!==e.name&&"srcset"!==e.name&&"alt"!==e.name&&(i.test(e.value)?t.setAttribute("srcset",e.value):a.test(e.value)&&t.setAttribute("src",e.value))}return t.classList.remove("lazy","lazyload"),t.removeAttribute("data-ll-status"),t.removeAttribute("data-src"),t.removeAttribute("data-srcset"),t.removeAttribute("loading"),t}},{selector:"span:has(img)",element:"span",transform:(t,e)=>{try{if(!b(t))return t;const r=w(t);if(!r)return t;const n=S(t),o=T(r,e);if(n&&C(n)){const t=h(o,n,e);return n.parentNode&&n.parentNode.removeChild(n),t}return o}catch(e){return t}}},{selector:'figure, p:has([class*="caption"])',element:"figure",transform:(t,e)=>{try{if(!b(t))return t;const r=w(t);if(!r)return t;const n=S(t);if(n&&C(n)){const o=w(t);let i;return i=o||T(r,e),h(i,n,e)}return t}catch(e){return t}}}]},732:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.GeminiExtractor=void 0;const n=r(181);class o extends n.ConversationExtractor{constructor(t,e){super(t,e),this.messageCount=null,this.conversationContainers=t.querySelectorAll("div.conversation-container"),this.footnotes=[]}canExtract(){return!!this.conversationContainers&&this.conversationContainers.length>0}extractMessages(){this.messageCount=0;const t=[];return this.conversationContainers?(this.extractSources(),this.conversationContainers.forEach((e=>{const r=e.querySelector("user-query");if(r){const e=r.querySelector(".query-text");if(e){const r=e.innerHTML||"";t.push({author:"You",content:r.trim(),metadata:{role:"user"}})}}const n=e.querySelector("model-response");if(n){const e=n.querySelector(".model-response-text .markdown"),r=n.querySelector("#extended-response-markdown-content")||e;if(r){let e=r.innerHTML||"";const n=document.createElement("div");n.innerHTML=e,n.querySelectorAll(".table-content").forEach((t=>{t.classList.remove("table-content")})),e=n.innerHTML,t.push({author:"Gemini",content:e.trim(),metadata:{role:"assistant"}})}}})),this.messageCount=t.length,t):t}extractSources(){const t=this.document.querySelectorAll("browse-item");t&&t.length>0&&t.forEach((t=>{var e,r,n,o;const i=t.querySelector("a");if(i instanceof HTMLAnchorElement){const t=i.href,a=(null===(r=null===(e=i.querySelector(".domain"))||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim())||"",s=(null===(o=null===(n=i.querySelector(".title"))||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||"";t&&(a||s)&&this.footnotes.push({url:t,text:s?`${a}: ${s}`:a})}}))}getFootnotes(){return this.footnotes}getMetadata(){var t;const e=this.getTitle(),r=null!==(t=this.messageCount)&&void 0!==t?t:this.extractMessages().length;return{title:e,site:"Gemini",url:this.url,messageCount:r,description:`Gemini conversation with ${r} messages`}}getTitle(){var t,e,r,n,o;const i=null===(t=this.document.title)||void 0===t?void 0:t.trim();if(i&&"Gemini"!==i&&!i.includes("Gemini"))return i;const a=null===(r=null===(e=this.document.querySelector(".title-text"))||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim();if(a)return a;const s=null===(o=null===(n=this.conversationContainers)||void 0===n?void 0:n.item(0))||void 0===o?void 0:o.querySelector(".query-text");if(s){const t=s.textContent||"";return t.length>50?t.slice(0,50)+"...":t}return"Gemini Conversation"}}e.GeminiExtractor=o},754:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.codeBlockRules=void 0;const n=r(552),o=[/^language-(\w+)$/,/^lang-(\w+)$/,/^(\w+)-code$/,/^code-(\w+)$/,/^syntax-(\w+)$/,/^code-snippet__(\w+)$/,/^highlight-(\w+)$/,/^(\w+)-snippet$/,/(?:^|\s)(?:language|lang|brush|syntax)-(\w+)(?:\s|$)/i],i=new Set(["abap","actionscript","ada","adoc","agda","antlr4","applescript","arduino","armasm","asciidoc","aspnet","atom","bash","batch","c","clojure","cmake","cobol","coffeescript","cpp","c++","crystal","csharp","cs","dart","django","dockerfile","dotnet","elixir","elm","erlang","fortran","fsharp","gdscript","gitignore","glsl","golang","gradle","graphql","groovy","haskell","hs","haxe","hlsl","html","idris","java","javascript","js","jsx","jsdoc","json","jsonp","julia","kotlin","latex","lisp","elisp","livescript","lua","makefile","markdown","md","markup","masm","mathml","matlab","mongodb","mysql","nasm","nginx","nim","nix","objc","ocaml","pascal","perl","php","postgresql","powershell","prolog","puppet","python","regex","rss","ruby","rb","rust","scala","scheme","shell","sh","solidity","sparql","sql","ssml","svg","swift","tcl","terraform","tex","toml","typescript","ts","tsx","unrealscript","verilog","vhdl","webassembly","wasm","xml","yaml","yml","zig"]);e.codeBlockRules=[{selector:["pre",'div[class*="prismjs"]',".syntaxhighlighter",".highlight",".highlight-source",".wp-block-syntaxhighlighter-code",".wp-block-code",'div[class*="language-"]'].join(", "),element:"pre",transform:(t,e)=>{if(!(t=>"classList"in t&&"getAttribute"in t&&"querySelector"in t)(t))return t;const r=t=>{var e;const r=t.getAttribute("data-lang")||t.getAttribute("data-language");if(r)return r.toLowerCase();const n=Array.from(t.classList||[]);if(null===(e=t.classList)||void 0===e?void 0:e.contains("syntaxhighlighter")){const t=n.find((t=>!["syntaxhighlighter","nogutter"].includes(t)));if(t&&i.has(t.toLowerCase()))return t.toLowerCase()}for(const a of n)for(const t of o){const e=a.toLowerCase().match(t);if(e&&e[1]&&i.has(e[1].toLowerCase()))return e[1].toLowerCase()}for(const o of n)if(i.has(o.toLowerCase()))return o.toLowerCase();return""};let a="",s=t;for(;s&&!a;){a=r(s);const t=s.querySelector("code");!a&&t&&(a=r(t)),s=s.parentElement}const l=t=>{if((0,n.isTextNode)(t))return t.textContent||"";let e="";if((0,n.isElement)(t)){if("BR"===t.tagName)return"\n";if(t.matches('div[class*="line"], span[class*="line"], .ec-line, [data-line-number], [data-line]')){const e=t.querySelector('.code, .content, [class*="code-"], [class*="content-"]');if(e)return(e.textContent||"")+"\n";const r=t.querySelector('.line-number, .gutter, [class*="line-number"], [class*="gutter"]');return r?Array.from(t.childNodes).filter((t=>!r.contains(t))).map((t=>l(t))).join("")+"\n":t.textContent+"\n"}t.childNodes.forEach((t=>{e+=l(t)}))}return e};let c="";t.matches(".syntaxhighlighter, .wp-block-syntaxhighlighter-code")&&(c=(t=>{const e=t.querySelector(".syntaxhighlighter table .code .container");if(e)return Array.from(e.children).map((t=>{const e=Array.from(t.querySelectorAll("code")).map((t=>{var e;let r=t.textContent||"";return(null===(e=t.classList)||void 0===e?void 0:e.contains("spaces"))&&(r=" ".repeat(r.length)),r})).join("");return e||t.textContent||""})).join("\n");const r=t.querySelectorAll(".code .line");return r.length>0?Array.from(r).map((t=>Array.from(t.querySelectorAll("code")).map((t=>t.textContent||"")).join("")||t.textContent||"")).join("\n"):""})(t)),c||(c=l(t)),c=c.replace(/^\s+|\s+$/g,"").replace(/\t/g,"    ").replace(/\n{3,}/g,"\n\n").replace(/\u00a0/g," ").replace(/^\n+/,"").replace(/\n+$/,"");const u=e.createElement("pre"),d=e.createElement("code");return a&&(d.setAttribute("data-lang",a),d.setAttribute("class",`language-${a}`)),d.textContent=c,u.appendChild(d),u}}]},840:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.standardizeContent=function(t,e,r,o=!1){(function(t){const e=t=>{if((0,c.isElement)(t)){const e=t.tagName.toLowerCase();if("pre"===e||"code"===e)return}if((0,c.isTextNode)(t)){const e=t.textContent||"",r=e.replace(/\xA0+/g,(e=>{var r,n,o,i;if(1===e.length){const e=null===(n=null===(r=t.previousSibling)||void 0===r?void 0:r.textContent)||void 0===n?void 0:n.slice(-1),a=null===(i=null===(o=t.nextSibling)||void 0===o?void 0:o.textContent)||void 0===i?void 0:i.charAt(0);if((null==e?void 0:e.match(/\w/))&&(null==a?void 0:a.match(/\w/)))return" "}return" ".repeat(e.length)}));r!==e&&(t.textContent=r)}t.hasChildNodes()&&Array.from(t.childNodes).forEach(e)};e(t)})(t),function(t){let e=0;Array.from(t.getElementsByTagName("*")).forEach((t=>{Array.from(t.childNodes).forEach((t=>{(0,c.isCommentNode)(t)&&(t.remove(),e++)}))})),(0,c.logDebug)("Removed HTML comments:",e)}(t),function(t,e,r){const o=t=>t.replace(/\u00A0/g," ").replace(/\s+/g," ").trim().toLowerCase(),i=t.getElementsByTagName("h1");Array.from(i).forEach((t=>{var e;const o=r.createElement("h2");o.innerHTML=t.innerHTML,Array.from(t.attributes).forEach((t=>{n.ALLOWED_ATTRIBUTES.has(t.name)&&o.setAttribute(t.name,t.value)})),null===(e=t.parentNode)||void 0===e||e.replaceChild(o,t)}));const a=t.getElementsByTagName("h2");if(a.length>0){const t=a[0],r=o(t.textContent||""),n=o(e);n&&n===r&&t.remove()}}(t,e.title,r),(0,a.standardizeFootnotes)(t),function(t,e){let r=0;u.forEach((n=>{t.querySelectorAll(n.selector).forEach((t=>{if(n.transform){const o=n.transform(t,e);t.replaceWith(o),r++}}))})),t.querySelectorAll("lite-youtube").forEach((t=>{const n=t.getAttribute("videoid");if(!n)return;const o=e.createElement("iframe");o.width="560",o.height="315",o.src=`https://www.youtube.com/embed/${n}`,o.title=t.getAttribute("videotitle")||"YouTube video player",o.frameBorder="0",o.allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",o.setAttribute("allowfullscreen",""),t.replaceWith(o),r++})),(0,c.logDebug)("Converted embedded elements:",r)}(t,r),o?(f(t,o),d(t),h(t),(0,c.logDebug)("Debug mode: Skipping div flattening to preserve structure")):(p(t,r),f(t,o),function(t){let e=0,r=0,o=!0;for(;o;){r++,o=!1;const i=Array.from(t.getElementsByTagName("*")).filter((t=>{if(n.ALLOWED_EMPTY_ELEMENTS.has(t.tagName.toLowerCase()))return!1;const e=t.textContent||"",r=0===e.trim().length,o=e.includes(" "),i=!t.hasChildNodes()||Array.from(t.childNodes).every((t=>{if((0,c.isTextNode)(t)){const e=t.textContent||"";return 0===e.trim().length&&!e.includes(" ")}return!1}));if("div"===t.tagName.toLowerCase()){const e=Array.from(t.children);if(e.length>0&&e.every((t=>{var e;if("span"!==t.tagName.toLowerCase())return!1;const r=(null===(e=t.textContent)||void 0===e?void 0:e.trim())||"";return","===r||""===r||" "===r})))return!0}return r&&!o&&i}));i.length>0&&(i.forEach((t=>{t.remove(),e++})),o=!0)}(0,c.logDebug)("Removed empty elements:",e,"iterations:",r)}(t),d(t),p(t,r),h(t),function(t,e){let r=0;const n=Date.now(),o=t=>{var e;if((0,c.isElement)(t)){const e=t.tagName.toLowerCase();if("pre"===e||"code"===e)return}if(Array.from(t.childNodes).forEach(o),(0,c.isTextNode)(t)){const n=t.textContent||"";if(!n||n.match(/^[\u200C\u200B\u200D\u200E\u200F\uFEFF\xA0\s]*$/))null===(e=t.parentNode)||void 0===e||e.removeChild(t),r++;else{const e=n.replace(/\n{3,}/g,"\n\n").replace(/^[\n\r\t]+/,"").replace(/[\n\r\t]+$/,"").replace(/[ \t]*\n[ \t]*/g,"\n").replace(/[ \t]{3,}/g," ").replace(/^[ ]+$/," ").replace(/\s+([,.!?:;])/g,"$1").replace(/[\u200C\u200B\u200D\u200E\u200F\uFEFF]+/g,"").replace(/(?:\xA0){2,}/g," ");e!==n&&(t.textContent=e,r+=n.length-e.length)}}},i=t=>{var n;if(!(0,c.isElement)(t))return;const o=t.tagName.toLowerCase();if("pre"===o||"code"===o)return;Array.from(t.childNodes).filter(c.isElement).forEach(i),t.normalize();const a="block"===(null===(n=(0,c.getComputedStyle)(t))||void 0===n?void 0:n.display),s=a?/^[\n\r\t \u200C\u200B\u200D\u200E\u200F\uFEFF\xA0]*$/:/^[\n\r\t\u200C\u200B\u200D\u200E\u200F\uFEFF]*$/,l=a?/^[\n\r\t \u200C\u200B\u200D\u200E\u200F\uFEFF\xA0]*$/:/^[\n\r\t\u200C\u200B\u200D\u200E\u200F\uFEFF]*$/;for(;t.firstChild&&(0,c.isTextNode)(t.firstChild)&&(t.firstChild.textContent||"").match(s);)t.removeChild(t.firstChild),r++;for(;t.lastChild&&(0,c.isTextNode)(t.lastChild)&&(t.lastChild.textContent||"").match(l);)t.removeChild(t.lastChild),r++;if(!a){const r=Array.from(t.childNodes);for(let n=0;n<r.length-1;n++){const o=r[n],i=r[n+1];if((0,c.isElement)(o)||(0,c.isElement)(i)){const r=i.textContent||"",n=o.textContent||"",a=r.match(/^[,.!?:;)\]]/),s=n.match(/[,.!?:;(\[]\s*$/),l=(0,c.isTextNode)(o)&&(o.textContent||"").endsWith(" ")||(0,c.isTextNode)(i)&&(i.textContent||"").startsWith(" ");if(!a&&!s&&!l){const r=e.createTextNode(" ");t.insertBefore(r,i)}}}}};o(t),i(t);const a=Date.now();(0,c.logDebug)("Removed empty lines:",{charactersRemoved:r,processingTime:`${(a-n).toFixed(2)}ms`})}(t,r))};const n=r(640),o=r(0),i=r(754),a=r(610),s=r(864),l=r(649),c=r(552),u=[...o.mathRules,...i.codeBlockRules,...s.headingRules,...l.imageRules,{selector:'div[data-testid^="paragraph"], div[role="paragraph"]',element:"p",transform:(t,e)=>{const r=e.createElement("p");return r.innerHTML=t.innerHTML,Array.from(t.attributes).forEach((t=>{n.ALLOWED_ATTRIBUTES.has(t.name)&&r.setAttribute(t.name,t.value)})),r}},{selector:'div[role="list"]',element:"ul",transform:(t,e)=>{var r;const n=t.querySelector('div[role="listitem"] .label'),o=((null===(r=null==n?void 0:n.textContent)||void 0===r?void 0:r.trim())||"").match(/^\d+\)/),i=e.createElement(o?"ol":"ul");return t.querySelectorAll('div[role="listitem"]').forEach((t=>{const r=e.createElement("li"),n=t.querySelector(".content");n&&(n.querySelectorAll('div[role="paragraph"]').forEach((t=>{const r=e.createElement("p");r.innerHTML=t.innerHTML,t.replaceWith(r)})),n.querySelectorAll('div[role="list"]').forEach((t=>{var r;const n=t.querySelector('div[role="listitem"] .label'),o=((null===(r=null==n?void 0:n.textContent)||void 0===r?void 0:r.trim())||"").match(/^\d+\)/),i=e.createElement(o?"ol":"ul");t.querySelectorAll('div[role="listitem"]').forEach((t=>{const r=e.createElement("li"),n=t.querySelector(".content");n&&(n.querySelectorAll('div[role="paragraph"]').forEach((t=>{const r=e.createElement("p");r.innerHTML=t.innerHTML,t.replaceWith(r)})),r.innerHTML=n.innerHTML),i.appendChild(r)})),t.replaceWith(i)})),r.innerHTML=n.innerHTML),i.appendChild(r)})),i}},{selector:'div[role="listitem"]',element:"li",transform:(t,e)=>{const r=t.querySelector(".content");return r?(r.querySelectorAll('div[role="paragraph"]').forEach((t=>{const r=e.createElement("p");r.innerHTML=t.innerHTML,t.replaceWith(r)})),r):t}}];function d(t){let e=0;const r=e=>{let n="",o=e.nextSibling;for(;o;)((0,c.isTextNode)(o)||(0,c.isElement)(o))&&(n+=o.textContent||""),o=o.nextSibling;if(n.trim())return!0;const i=e.parentElement;return!(!i||i===t)&&r(i)};Array.from(t.querySelectorAll("h1, h2, h3, h4, h5, h6")).reverse().forEach((t=>{r(t)||(t.remove(),e++)})),e>0&&(0,c.logDebug)("Removed trailing headings:",e)}function f(t,e){let r=0;const o=t=>{if("svg"===t.tagName.toLowerCase()||"http://www.w3.org/2000/svg"===t.namespaceURI)return;const o=Array.from(t.attributes),i=t.tagName.toLowerCase();o.forEach((o=>{const a=o.name.toLowerCase(),s=o.value;"id"===a&&(s.startsWith("fnref:")||s.startsWith("fn:")||"footnotes"===s)||"class"===a&&("code"===i&&s.startsWith("language-")||"footnote-backref"===s)||(e?n.ALLOWED_ATTRIBUTES.has(a)||n.ALLOWED_ATTRIBUTES_DEBUG.has(a)||a.startsWith("data-")||(t.removeAttribute(o.name),r++):n.ALLOWED_ATTRIBUTES.has(a)||(t.removeAttribute(o.name),r++))}))};o(t),t.querySelectorAll("*").forEach(o),(0,c.logDebug)("Stripped attributes:",r)}function h(t){let e=0;const r=Date.now(),n=Array.from(t.getElementsByTagName("br"));let o=[];const i=()=>{if(o.length>2)for(let t=2;t<o.length;t++)o[t].remove(),e++;o=[]};n.forEach((t=>{var e;let r=!1;if(o.length>0){const n=o[o.length-1];let i=t.previousSibling;for(;i&&(0,c.isTextNode)(i)&&!(null===(e=i.textContent)||void 0===e?void 0:e.trim());)i=i.previousSibling;i===n&&(r=!0)}r?o.push(t):(i(),o=[t])})),i();const a=Date.now();(0,c.logDebug)("Standardized br elements:",{removed:e,processingTime:`${(a-r).toFixed(2)}ms`})}function p(t,e){let r=0;const o=Date.now();let i=!0;function a(t){var e;for(const r of t.childNodes){if((0,c.isTextNode)(r)&&(null===(e=r.textContent)||void 0===e?void 0:e.trim()))return!0;if((0,c.isElement)(r)&&n.INLINE_ELEMENTS.has(r.nodeName.toLowerCase()))return!0}return!1}const s=t=>{const e=t.tagName.toLowerCase();if(n.PRESERVE_ELEMENTS.has(e))return!0;const r=t.getAttribute("role");if(r&&["article","main","navigation","banner","contentinfo"].includes(r))return!0;const o=t.className;return!("string"!=typeof o||!o.toLowerCase().match(/(?:article|main|content|footnote|reference|bibliography)/))||!!Array.from(t.children).some((t=>n.PRESERVE_ELEMENTS.has(t.tagName.toLowerCase())||"article"===t.getAttribute("role")||t.className&&"string"==typeof t.className&&t.className.toLowerCase().match(/(?:article|main|content|footnote|reference|bibliography)/)))},l=t=>{var e;if(a(t))return!1;if(!(null===(e=t.textContent)||void 0===e?void 0:e.trim()))return!0;const r=Array.from(t.children);if(0===r.length)return!0;if(r.every((t=>{const e=t.tagName.toLowerCase();return n.BLOCK_ELEMENTS.includes(e)||"p"===e||"h1"===e||"h2"===e||"h3"===e||"h4"===e||"h5"===e||"h6"===e||"ul"===e||"ol"===e||"pre"===e||"blockquote"===e||"figure"===e})))return!0;const o=t.className.toLowerCase();if(/(?:wrapper|container|layout|row|col|grid|flex|outer|inner|content-area)/i.test(o))return!0;const i=Array.from(t.childNodes).filter((t=>{var e;return(0,c.isTextNode)(t)&&(null===(e=t.textContent)||void 0===e?void 0:e.trim())}));return 0===i.length||!(!(r.length>0)||r.some((t=>{const e=t.tagName.toLowerCase();return n.INLINE_ELEMENTS.has(e)})))},u=o=>{var i,u;if(!o.isConnected||s(o))return!1;const d=o.tagName.toLowerCase();if(!n.ALLOWED_EMPTY_ELEMENTS.has(d)&&!o.children.length&&!(null===(i=o.textContent)||void 0===i?void 0:i.trim()))return o.remove(),r++,!0;if(o.parentElement===t){const t=Array.from(o.children);if(t.length>0&&!t.some((t=>{const e=t.tagName.toLowerCase();return n.INLINE_ELEMENTS.has(e)}))){const t=e.createDocumentFragment();for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}}if(l(o)){if(!Array.from(o.children).some((t=>{const e=t.tagName.toLowerCase();return n.INLINE_ELEMENTS.has(e)}))){const t=e.createDocumentFragment();for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}const t=e.createDocumentFragment();for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}const f=Array.from(o.childNodes);if(f.length>0&&f.every((t=>(0,c.isTextNode)(t)||(0,c.isElement)(t)&&n.INLINE_ELEMENTS.has(t.nodeName.toLowerCase())))&&(null===(u=o.textContent)||void 0===u?void 0:u.trim())){const t=e.createElement("p");for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}if(1===o.children.length){const t=o.firstElementChild,e=t.tagName.toLowerCase();if(n.BLOCK_ELEMENTS.includes(e)&&!s(t))return o.replaceWith(t),r++,!0}let h=0,p=o.parentElement;for(;p;){const t=p.tagName.toLowerCase();n.BLOCK_ELEMENTS.includes(t)&&h++,p=p.parentElement}if(h>0&&!a(o)){const t=e.createDocumentFragment();for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}return!1},d=()=>{const e=Array.from(t.children).filter((t=>n.BLOCK_ELEMENTS.includes(t.tagName.toLowerCase())));let r=!1;return e.forEach((t=>{u(t)&&(r=!0)})),r},f=()=>{const e=Array.from(t.querySelectorAll(n.BLOCK_ELEMENTS.join(","))).sort(((t,e)=>{const r=t=>{let e=0,r=t.parentElement;for(;r;){const t=r.tagName.toLowerCase();n.BLOCK_ELEMENTS.includes(t)&&e++,r=r.parentElement}return e};return r(e)-r(t)}));let r=!1;return e.forEach((t=>{u(t)&&(r=!0)})),r},h=()=>{const o=Array.from(t.querySelectorAll(n.BLOCK_ELEMENTS.join(",")));let i=!1;return o.forEach((t=>{const n=Array.from(t.children);if(n.length>0&&n.every((t=>"p"===t.tagName.toLowerCase()))||!s(t)&&l(t)){const n=e.createDocumentFragment();for(;t.firstChild;)n.appendChild(t.firstChild);t.replaceWith(n),r++,i=!0}})),i};do{i=!1,d()&&(i=!0),f()&&(i=!0),h()&&(i=!0)}while(i);const p=Date.now();(0,c.logDebug)("Flattened wrapper elements:",{count:r,processingTime:`${(p-o).toFixed(2)}ms`})}},864:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.headingRules=void 0;const n=r(640);e.headingRules=[{selector:"h1, h2, h3, h4, h5, h6",element:"keep",transform:t=>{var e;const r=t.ownerDocument;if(!r)return t;const o=r.createElement(t.tagName);Array.from(t.attributes).forEach((t=>{n.ALLOWED_ATTRIBUTES.has(t.name)&&o.setAttribute(t.name,t.value)}));const i=t.cloneNode(!0),a=new Map;Array.from(i.querySelectorAll("*")).forEach((t=>{var e,r,n,o,s,l;let c=!1;if("a"===t.tagName.toLowerCase()){const r=t.getAttribute("href");((null==r?void 0:r.includes("#"))||(null==r?void 0:r.startsWith("#")))&&(a.set(t,(null===(e=t.textContent)||void 0===e?void 0:e.trim())||""),c=!0)}if(t.classList.contains("anchor")&&(a.set(t,(null===(r=t.textContent)||void 0===r?void 0:r.trim())||""),c=!0),"button"===t.tagName.toLowerCase()&&(c=!0),("span"===t.tagName.toLowerCase()||"div"===t.tagName.toLowerCase())&&t.querySelector('a[href^="#"]')){const e=t.querySelector('a[href^="#"]');e&&a.set(t,(null===(n=e.textContent)||void 0===n?void 0:n.trim())||""),c=!0}if(c){const e=t.parentElement;e&&e!==i&&(null===(o=e.textContent)||void 0===o?void 0:o.trim())===(null===(s=t.textContent)||void 0===s?void 0:s.trim())&&a.set(e,(null===(l=t.textContent)||void 0===l?void 0:l.trim())||"")}})),Array.from(i.querySelectorAll("*")).filter((t=>{if("a"===t.tagName.toLowerCase()){const e=t.getAttribute("href");return(null==e?void 0:e.includes("#"))||(null==e?void 0:e.startsWith("#"))}return!!t.classList.contains("anchor")||"button"===t.tagName.toLowerCase()||!("span"!==t.tagName.toLowerCase()&&"div"!==t.tagName.toLowerCase()||!t.querySelector('a[href^="#"]'))})).forEach((t=>t.remove()));let s=(null===(e=i.textContent)||void 0===e?void 0:e.trim())||"";return!s&&a.size>0&&(s=Array.from(a.values())[0]),o.textContent=s,o}}]},917:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ExtractorRegistry=void 0;const n=r(959),o=r(248),i=r(258),a=r(458),s=r(632),l=r(397),c=r(20),u=r(732);class d{static initialize(){this.register({patterns:["twitter.com",/\/x\.com\/.*/],extractor:o.TwitterExtractor}),this.register({patterns:["reddit.com","old.reddit.com","new.reddit.com",/^https:\/\/[^\/]+\.reddit\.com/],extractor:n.RedditExtractor}),this.register({patterns:["youtube.com","youtu.be",/youtube\.com\/watch\?v=.*/,/youtu\.be\/.*/],extractor:i.YoutubeExtractor}),this.register({patterns:[/news\.ycombinator\.com\/item\?id=.*/],extractor:a.HackerNewsExtractor}),this.register({patterns:[/^https?:\/\/chatgpt\.com\/(c|share)\/.*/],extractor:s.ChatGPTExtractor}),this.register({patterns:[/^https?:\/\/claude\.ai\/(chat|share)\/.*/],extractor:l.ClaudeExtractor}),this.register({patterns:[/^https?:\/\/grok\.com\/(chat|share)(\/.*)?$/],extractor:c.GrokExtractor}),this.register({patterns:[/^https?:\/\/gemini\.google\.com\/app\/.*/],extractor:u.GeminiExtractor})}static register(t){this.mappings.push(t)}static findExtractor(t,e,r){try{const n=new URL(e).hostname;if(this.domainCache.has(n)){const o=this.domainCache.get(n);return o?new o(t,e,r):null}for(const{patterns:o,extractor:i}of this.mappings)if(o.some((t=>t instanceof RegExp?t.test(e):n.includes(t))))return this.domainCache.set(n,i),new i(t,e,r);return this.domainCache.set(n,null),null}catch(t){return null}}static clearCache(){this.domainCache.clear()}}e.ExtractorRegistry=d,d.mappings=[],d.domainCache=new Map,d.initialize()},959:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.RedditExtractor=void 0;const n=r(279);class o extends n.BaseExtractor{constructor(t,e){super(t,e),this.shredditPost=t.querySelector("shreddit-post")}canExtract(){return!!this.shredditPost}extract(){var t,e;const r=this.getPostContent(),n=this.extractComments(),o=this.createContentHtml(r,n),i=(null===(e=null===(t=this.document.querySelector("h1"))||void 0===t?void 0:t.textContent)||void 0===e?void 0:e.trim())||"",a=this.getSubreddit(),s=this.getPostAuthor(),l=this.createDescription(r);return{content:o,contentHtml:o,extractedContent:{postId:this.getPostId(),subreddit:a,postAuthor:s},variables:{title:i,author:s,site:`r/${a}`,description:l}}}getPostContent(){var t,e,r,n;return((null===(e=null===(t=this.shredditPost)||void 0===t?void 0:t.querySelector('[slot="text-body"]'))||void 0===e?void 0:e.innerHTML)||"")+((null===(n=null===(r=this.shredditPost)||void 0===r?void 0:r.querySelector("#post-image"))||void 0===n?void 0:n.outerHTML)||"")}createContentHtml(t,e){return`\n\t\t\t<div class="reddit-post">\n\t\t\t\t<div class="post-content">\n\t\t\t\t\t${t}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t${e?`\n\t\t\t\t<hr>\n\t\t\t\t<h2>Comments</h2>\n\t\t\t\t<div class="reddit-comments">\n\t\t\t\t\t${e}\n\t\t\t\t</div>\n\t\t\t`:""}\n\t\t`.trim()}extractComments(){const t=Array.from(this.document.querySelectorAll("shreddit-comment"));return this.processComments(t)}getPostId(){const t=this.url.match(/comments\/([a-zA-Z0-9]+)/);return(null==t?void 0:t[1])||""}getSubreddit(){const t=this.url.match(/\/r\/([^/]+)/);return(null==t?void 0:t[1])||""}getPostAuthor(){var t;return(null===(t=this.shredditPost)||void 0===t?void 0:t.getAttribute("author"))||""}createDescription(t){var e;if(!t)return"";const r=document.createElement("div");return r.innerHTML=t,(null===(e=r.textContent)||void 0===e?void 0:e.trim().slice(0,140).replace(/\s+/g," "))||""}processComments(t){var e;let r="",n=-1,o=[];for(const i of t){const t=parseInt(i.getAttribute("depth")||"0"),a=i.getAttribute("author")||"",s=i.getAttribute("score")||"0",l=i.getAttribute("permalink")||"",c=(null===(e=i.querySelector('[slot="comment"]'))||void 0===e?void 0:e.innerHTML)||"",u=i.querySelector("faceplate-timeago"),d=(null==u?void 0:u.getAttribute("ts"))||"",f=d?new Date(d).toISOString().split("T")[0]:"";if(0===t){for(;o.length>0;)r+="</blockquote>",o.pop();r+="<blockquote>",o=[0],n=0}else if(t<n)for(;o.length>0&&o[o.length-1]>=t;)r+="</blockquote>",o.pop();else t>n&&(r+="<blockquote>",o.push(t));r+=`<div class="comment">\n\t<div class="comment-metadata">\n\t\t<span class="comment-author"><strong>${a}</strong></span> •\n\t\t<a href="https://reddit.com${l}" class="comment-link">${s} points</a> •\n\t\t<span class="comment-date">${f}</span>\n\t</div>\n\t<div class="comment-content">${c}</div>\n</div>`,n=t}for(;o.length>0;)r+="</blockquote>",o.pop();return r}}e.RedditExtractor=o},968:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ContentScorer=void 0;const n=r(640),o=["admonition","article","content","entry","image","img","font","figure","figcaption","pre","main","post","story","table"],i=["advertisement","all rights reserved","banner","cookie","comments","copyright","follow me","follow us","footer","header","homepage","login","menu","more articles","more like this","most read","nav","navigation","newsletter","newsletter","popular","privacy","recommended","register","related","responses","share","sidebar","sign in","sign up","signup","social","sponsored","subscribe","subscribe","terms","trending"],a=["ad","banner","cookie","copyright","footer","header","homepage","menu","nav","newsletter","popular","privacy","recommended","related","rights","share","sidebar","social","sponsored","subscribe","terms","trending","widget"];class s{constructor(t,e=!1){this.doc=t,this.debug=e}static scoreElement(t){let e=0;const r=t.textContent||"",o=r.split(/\s+/).length;e+=o,e+=10*t.getElementsByTagName("p").length,e-=t.getElementsByTagName("a").length/(o||1)*5,e-=t.getElementsByTagName("img").length/(o||1)*3;try{const r=t.getAttribute("style")||"",n=t.getAttribute("align")||"";(r.includes("float: right")||r.includes("text-align: right")||"right"===n)&&(e+=5)}catch(t){}/\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2},?\s+\d{4}\b/i.test(r)&&(e+=10),/\b(?:by|written by|author:)\s+[A-Za-z\s]+\b/i.test(r)&&(e+=10);const i=t.className.toLowerCase();if((i.includes("content")||i.includes("article")||i.includes("post"))&&(e+=15),t.querySelector(n.FOOTNOTE_INLINE_REFERENCES)&&(e+=10),t.querySelector(n.FOOTNOTE_LIST_SELECTORS)&&(e+=10),e-=5*t.getElementsByTagName("table").length,"td"===t.tagName.toLowerCase()){const r=t.closest("table");if(r){const n=parseInt(r.getAttribute("width")||"0"),o=r.getAttribute("align")||"",i=r.className.toLowerCase();if(n>400||"center"===o||i.includes("content")||i.includes("article")){const n=Array.from(r.getElementsByTagName("td")),o=n.indexOf(t);o>0&&o<n.length-1&&(e+=10)}}}return e}static findBestElement(t,e=50){let r=null,n=0;return t.forEach((t=>{const e=this.scoreElement(t);e>n&&(n=e,r=t)})),n>e?r:null}static scoreAndRemove(t,e=!1){Date.now();const r=new Set;Array.from(t.querySelectorAll(n.BLOCK_ELEMENTS.join(","))).forEach((t=>{r.has(t)||s.isLikelyContent(t)||s.scoreNonContentBlock(t)<0&&r.add(t)})),r.forEach((t=>t.remove())),Date.now()}static isLikelyContent(t){const e=t.getAttribute("role");if(e&&["article","main","contentinfo"].includes(e))return!0;const r=t.className.toLowerCase(),n=t.id.toLowerCase();for(const s of o)if(r.includes(s)||n.includes(s))return!0;const i=(t.textContent||"").split(/\s+/).length,a=t.getElementsByTagName("p").length;return i>50&&a>1||i>100||i>30&&a>0}static scoreNonContentBlock(t){if(t.querySelector(n.FOOTNOTE_LIST_SELECTORS))return 0;let e=0;const r=t.textContent||"",o=r.split(/\s+/).length;if(o<3)return 0;for(const n of i)r.toLowerCase().includes(n)&&(e-=10);const s=t.getElementsByTagName("a").length;s/(o||1)>.5&&(e-=15);const l=t.getElementsByTagName("ul").length+t.getElementsByTagName("ol").length;l>0&&s>3*l&&(e-=10);const c=t.className.toLowerCase(),u=t.id.toLowerCase();for(const n of a)(c.includes(n)||u.includes(n))&&(e-=8);return e}}e.ContentScorer=s}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return t[n](i,i.exports,r),i.exports}var n={};return(()=>{var t=n;const e=r(628);t.default=e.Defuddle})(),n.default})(),t.exports=e()},1732:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=l(r(368)),o=r(9529),i=r(7575),a=l(r(3807)),s=r(6183);function l(t){return t&&t.__esModule?t:{default:t}}function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,d(n.key),n)}}function d(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}var f="\nLooks like there is an error in the background page. You might want to inspect your background page for more details.\n",h={channelName:o.DEFAULT_CHANNEL_NAME,state:{},serializer:i.noop,deserializer:i.noop,patchStrategy:a.default},p=function(){return t=function t(){var e=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h,n=r.channelName,a=void 0===n?h.channelName:n,l=r.state,c=void 0===l?h.state:l,u=r.serializer,d=void 0===u?h.serializer:u,f=r.deserializer,p=void 0===f?h.deserializer:f,m=r.patchStrategy,g=void 0===m?h.patchStrategy:m;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),!a)throw new Error("channelName is required in options");if("function"!=typeof d)throw new Error("serializer must be a function");if("function"!=typeof p)throw new Error("deserializer must be a function");if("function"!=typeof g)throw new Error("patchStrategy must be one of the included patching strategies or a custom patching function");this.channelName=a,this.readyResolved=!1,this.readyPromise=new Promise((function(t){return e.readyResolve=t})),this.browserAPI=(0,s.getBrowserAPI)(),this.initializeStore=this.initializeStore.bind(this),this.browserAPI.runtime.sendMessage({type:o.FETCH_STATE_TYPE,channelName:a},void 0,this.initializeStore),this.deserializer=p,this.serializedPortListener=(0,i.withDeserializer)(p)((function(){var t;return(t=e.browserAPI.runtime.onMessage).addListener.apply(t,arguments)})),this.serializedMessageSender=(0,i.withSerializer)(d)((function(){var t;return(t=e.browserAPI.runtime).sendMessage.apply(t,arguments)})),this.listeners=[],this.state=c,this.patchStrategy=g,this.serializedPortListener((function(t){if(t&&t.channelName===e.channelName)switch(t.type){case o.STATE_TYPE:e.replaceState(t.payload),e.readyResolved||(e.readyResolved=!0,e.readyResolve());break;case o.PATCH_STATE_TYPE:e.patchState(t.payload)}}),(function(t){return Boolean(t)&&"string"==typeof t.type&&t.channelName===e.channelName})),this.dispatch=this.dispatch.bind(this),this.getState=this.getState.bind(this),this.subscribe=this.subscribe.bind(this)},e=[{key:"ready",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null!==t?this.readyPromise.then(t):this.readyPromise}},{key:"subscribe",value:function(t){var e=this;return this.listeners.push(t),function(){e.listeners=e.listeners.filter((function(e){return e!==t}))}}},{key:"patchState",value:function(t){this.state=this.patchStrategy(this.state,t),this.listeners.forEach((function(t){return t()}))}},{key:"replaceState",value:function(t){this.state=t,this.listeners.forEach((function(t){return t()}))}},{key:"getState",value:function(){return this.state}},{key:"replaceReducer",value:function(){}},{key:"dispatch",value:function(t){var e=this;return new Promise((function(r,i){e.serializedMessageSender({type:o.DISPATCH_TYPE,channelName:e.channelName,payload:t},null,(function(t){if(t){var o=t.error,a=t.value;if(o){var s=new Error("".concat(f).concat(o));i((0,n.default)(s,o))}else r(a&&a.payload)}else{var l=e.browserAPI.runtime.lastError,c=new Error("".concat(f).concat(l));i((0,n.default)(c,l))}}))}))}},{key:"initializeStore",value:function(t){t&&t.type===o.FETCH_STATE_TYPE&&(this.replaceState(t.payload),this.readyResolved||(this.readyResolved=!0,this.readyResolve()))}}],e&&u(t.prototype,e),r&&u(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();e.default=p},3207:(t,e,r)=>{"use strict";Object.defineProperty(e,"il",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"nK",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(e,"Tw",{enumerable:!0,get:function(){return o.default}});var n=s(r(1732)),o=s(r(9449)),i=s(r(6745)),a=s(r(3988));function s(t){return t&&t.__esModule?t:{default:t}}},3807:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var r=Object.assign({},t);return e.forEach((function(t){var e=t.change,o=t.key,i=t.value;switch(e){case n.DIFF_STATUS_UPDATED:r[o]=i;break;case n.DIFF_STATUS_REMOVED:Reflect.deleteProperty(r,o)}})),r};var n=r(140)},3988:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default=function(t){return function(){return function(e){return function(r){var n=t[r.type];return e(n?n(r):r)}}}}},6183:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getBrowserAPI=function(){var t;try{t=self.chrome||self.browser||browser}catch(e){t=browser}if(!t)throw new Error("Browser API is not present");return t}},6745:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n,o=r(9529),i=r(7575),a=r(6183),s=(n=r(8642))&&n.__esModule?n:{default:n},l=r(8571);function c(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return u(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){s=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var d={channelName:o.DEFAULT_CHANNEL_NAME,dispatchResponder:function(t,e){Promise.resolve(t).then((function(t){e({error:null,value:t})})).catch((function(t){e({error:t.message,value:null})}))},serializer:i.noop,deserializer:i.noop,diffStrategy:s.default};e.default=function(){var t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:d).channelName,e=void 0===t?d.channelName:t,r=(0,a.getBrowserAPI)(),s=function(t){return t.type===o.DISPATCH_TYPE&&t.channelName===e},u=(0,l.createDeferredListener)((function(t){return t.type===o.FETCH_STATE_TYPE&&t.channelName===e})),f=(0,l.createDeferredListener)(s);return r.runtime.onMessage.addListener(u.listener),r.runtime.onMessage.addListener(f.listener),function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:d,l=a.dispatchResponder,h=void 0===l?d.dispatchResponder:l,p=a.serializer,m=void 0===p?d.serializer:p,g=a.deserializer,v=void 0===g?d.deserializer:g,y=a.diffStrategy,b=void 0===y?d.diffStrategy:y;if("function"!=typeof m)throw new Error("serializer must be a function");if("function"!=typeof v)throw new Error("deserializer must be a function");if("function"!=typeof b)throw new Error("diffStrategy must be one of the included diffing strategies or a custom diff function");var E=(0,i.withSerializer)(m)((function(){for(var t,e=arguments.length,n=new Array(e),o=0;o<e;o++)n[o]=arguments[o];var i=function(){r.runtime.lastError};return(t=r.runtime).sendMessage.apply(t,n.concat([i])),r.tabs.query({},(function(t){var e,o=c(t);try{for(o.s();!(e=o.n()).done;){var a,s=e.value;(a=r.tabs).sendMessage.apply(a,[s.id].concat(n,[i]))}}catch(l){o.e(l)}finally{o.f()}}))})),w=t.getState();t.subscribe((function(){var r=t.getState(),n=b(w,r);n.length&&(w=r,E({type:o.PATCH_STATE_TYPE,payload:n,channelName:e}))})),E({type:o.STATE_TYPE,payload:w,channelName:e}),u.setListener((function(e,r,n){var i=t.getState();n({type:o.FETCH_STATE_TYPE,payload:i})})),(0,i.withDeserializer)(v)(f.setListener)((function(e,r,o){var i=Object.assign({},e.payload,{_sender:r}),a=null;try{a=t.dispatch(i)}catch(n){a=Promise.reject(n.message)}h(a,o)}),s)}}},7575:(t,e)=>{"use strict";function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach((function(e){i(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function i(t,e,n){return(e=function(t){var e=function(t,e){if("object"!=r(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==r(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}Object.defineProperty(e,"__esModule",{value:!0}),e.withSerializer=e.withDeserializer=e.noop=void 0;var a=e.noop=function(t){return t},s=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return o(o({},t),t.payload?{payload:e(t.payload)}:{})};e.withDeserializer=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a;return function(e){return function(r,n){return e(function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,r=arguments.length>2?arguments[2]:void 0;return r?function(n){for(var o=arguments.length,i=new Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];return r.apply(void 0,[n].concat(i))?t.apply(void 0,[s(n,e)].concat(i)):t.apply(void 0,[n].concat(i))}:function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];return t.apply(void 0,[s(r,e)].concat(o))}}(r,t,n))}}},e.withSerializer=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a;return function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];if(o.length<=r)throw new Error("Message in request could not be serialized. "+"Expected message in position ".concat(r," but only received ").concat(o.length," args."));return o[r]=s(o[r],t),e.apply(void 0,o)}}}},8571:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createDeferredListener=void 0;e.createDeferredListener=function(t){var e=function(){},r=new Promise((function(t){return e=t}));return{setListener:e,listener:function(e,n,o){if(t(e,n,o))return r.then((function(t){t(e,n,o)})),!0}}}},8642:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var r=[];return Object.keys(e).forEach((function(o){t[o]!==e[o]&&r.push({key:o,value:e[o],change:n.DIFF_STATUS_UPDATED})})),Object.keys(t).forEach((function(t){e.hasOwnProperty(t)||r.push({key:t,change:n.DIFF_STATUS_REMOVED})})),r};var n=r(140)},9448:function(t,e,r){!function(t){"use strict";function e(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}function n(t,e){Object.defineProperty(this,"kind",{value:t,enumerable:!0}),e&&e.length&&Object.defineProperty(this,"path",{value:e,enumerable:!0})}function o(t,e,r){o.super_.call(this,"E",t),Object.defineProperty(this,"lhs",{value:e,enumerable:!0}),Object.defineProperty(this,"rhs",{value:r,enumerable:!0})}function i(t,e){i.super_.call(this,"N",t),Object.defineProperty(this,"rhs",{value:e,enumerable:!0})}function a(t,e){a.super_.call(this,"D",t),Object.defineProperty(this,"lhs",{value:e,enumerable:!0})}function s(t,e,r){s.super_.call(this,"A",t),Object.defineProperty(this,"index",{value:e,enumerable:!0}),Object.defineProperty(this,"item",{value:r,enumerable:!0})}function l(t,e,r){var n=t.slice((r||e)+1||t.length);return t.length=e<0?t.length+e:e,t.push.apply(t,n),t}function c(t){var e=void 0===t?"undefined":O(t);return"object"!==e?e:t===Math?"math":null===t?"null":Array.isArray(t)?"array":"[object Date]"===Object.prototype.toString.call(t)?"date":"function"==typeof t.toString&&/^\/.*\//.test(t.toString())?"regexp":"object"}function u(t,e,r,n,d,f,h){h=h||[];var p=(d=d||[]).slice(0);if(void 0!==f){if(n){if("function"==typeof n&&n(p,f))return;if("object"===(void 0===n?"undefined":O(n))){if(n.prefilter&&n.prefilter(p,f))return;if(n.normalize){var m=n.normalize(p,f,t,e);m&&(t=m[0],e=m[1])}}}p.push(f)}"regexp"===c(t)&&"regexp"===c(e)&&(t=t.toString(),e=e.toString());var g=void 0===t?"undefined":O(t),v=void 0===e?"undefined":O(e),y="undefined"!==g||h&&h[h.length-1].lhs&&h[h.length-1].lhs.hasOwnProperty(f),b="undefined"!==v||h&&h[h.length-1].rhs&&h[h.length-1].rhs.hasOwnProperty(f);if(!y&&b)r(new i(p,e));else if(!b&&y)r(new a(p,t));else if(c(t)!==c(e))r(new o(p,t,e));else if("date"===c(t)&&t-e!==0)r(new o(p,t,e));else if("object"===g&&null!==t&&null!==e)if(h.filter((function(e){return e.lhs===t})).length)t!==e&&r(new o(p,t,e));else{if(h.push({lhs:t,rhs:e}),Array.isArray(t)){var E;for(t.length,E=0;E<t.length;E++)E>=e.length?r(new s(p,E,new a(void 0,t[E]))):u(t[E],e[E],r,n,p,E,h);for(;E<e.length;)r(new s(p,E,new i(void 0,e[E++])))}else{var w=Object.keys(t),S=Object.keys(e);w.forEach((function(o,i){var a=S.indexOf(o);a>=0?(u(t[o],e[o],r,n,p,o,h),S=l(S,a)):u(t[o],void 0,r,n,p,o,h)})),S.forEach((function(t){u(void 0,e[t],r,n,p,t,h)}))}h.length=h.length-1}else t!==e&&("number"===g&&isNaN(t)&&isNaN(e)||r(new o(p,t,e)))}function d(t,e,r,n){return n=n||[],u(t,e,(function(t){t&&n.push(t)}),r),n.length?n:void 0}function f(t,e,r){if(r.path&&r.path.length){var n,o=t[e],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":f(o[r.path[n]],r.index,r.item);break;case"D":delete o[r.path[n]];break;case"E":case"N":o[r.path[n]]=r.rhs}}else switch(r.kind){case"A":f(t[e],r.index,r.item);break;case"D":t=l(t,e);break;case"E":case"N":t[e]=r.rhs}return t}function h(t,e,r){if(t&&e&&r&&r.kind){for(var n=t,o=-1,i=r.path?r.path.length-1:0;++o<i;)void 0===n[r.path[o]]&&(n[r.path[o]]="number"==typeof r.path[o]?[]:{}),n=n[r.path[o]];switch(r.kind){case"A":f(r.path?n[r.path[o]]:n,r.index,r.item);break;case"D":delete n[r.path[o]];break;case"E":case"N":n[r.path[o]]=r.rhs}}}function p(t,e,r){if(r.path&&r.path.length){var n,o=t[e],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":p(o[r.path[n]],r.index,r.item);break;case"D":case"E":o[r.path[n]]=r.lhs;break;case"N":delete o[r.path[n]]}}else switch(r.kind){case"A":p(t[e],r.index,r.item);break;case"D":case"E":t[e]=r.lhs;break;case"N":t=l(t,e)}return t}function m(t,e,r){if(t&&e&&r&&r.kind){var n,o,i=t;for(o=r.path.length-1,n=0;n<o;n++)void 0===i[r.path[n]]&&(i[r.path[n]]={}),i=i[r.path[n]];switch(r.kind){case"A":p(i[r.path[n]],r.index,r.item);break;case"D":case"E":i[r.path[n]]=r.lhs;break;case"N":delete i[r.path[n]]}}}function g(t,e,r){t&&e&&u(t,e,(function(n){r&&!r(t,e,n)||h(t,e,n)}))}function v(t){return"color: "+P[t].color+"; font-weight: bold"}function y(t){var e=t.kind,r=t.path,n=t.lhs,o=t.rhs,i=t.index,a=t.item;switch(e){case"E":return[r.join("."),n,"→",o];case"N":return[r.join("."),o];case"D":return[r.join(".")];case"A":return[r.join(".")+"["+i+"]",a];default:return[]}}function b(t,e,r,n){var o=d(t,e);try{n?r.groupCollapsed("diff"):r.group("diff")}catch(t){r.log("diff")}o?o.forEach((function(t){var e=t.kind,n=y(t);r.log.apply(r,["%c "+P[e].text,v(e)].concat(M(n)))})):r.log("—— no diff ——");try{r.groupEnd()}catch(t){r.log("—— diff end —— ")}}function E(t,e,r,n){switch(void 0===t?"undefined":O(t)){case"object":return"function"==typeof t[n]?t[n].apply(t,M(r)):t[n];case"function":return t(e);default:return t}}function w(t){var e=t.timestamp,r=t.duration;return function(t,n,o){var i=["action"];return i.push("%c"+String(t.type)),e&&i.push("%c@ "+n),r&&i.push("%c(in "+o.toFixed(2)+" ms)"),i.join(" ")}}function S(t,e){var r=e.logger,n=e.actionTransformer,o=e.titleFormatter,i=void 0===o?w(e):o,a=e.collapsed,s=e.colors,l=e.level,c=e.diff,u=void 0===e.titleFormatter;t.forEach((function(o,d){var f=o.started,h=o.startedTime,p=o.action,m=o.prevState,g=o.error,v=o.took,y=o.nextState,w=t[d+1];w&&(y=w.prevState,v=w.started-f);var S=n(p),C="function"==typeof a?a((function(){return y}),p,o):a,T=N(h),_=s.title?"color: "+s.title(S)+";":"",A=["color: gray; font-weight: lighter;"];A.push(_),e.timestamp&&A.push("color: gray; font-weight: lighter;"),e.duration&&A.push("color: gray; font-weight: lighter;");var x=i(S,T,v);try{C?s.title&&u?r.groupCollapsed.apply(r,["%c "+x].concat(A)):r.groupCollapsed(x):s.title&&u?r.group.apply(r,["%c "+x].concat(A)):r.group(x)}catch(t){r.log(x)}var L=E(l,S,[m],"prevState"),O=E(l,S,[S],"action"),M=E(l,S,[g,m],"error"),k=E(l,S,[y],"nextState");if(L)if(s.prevState){var P="color: "+s.prevState(m)+"; font-weight: bold";r[L]("%c prev state",P,m)}else r[L]("prev state",m);if(O)if(s.action){var D="color: "+s.action(S)+"; font-weight: bold";r[O]("%c action    ",D,S)}else r[O]("action    ",S);if(g&&M)if(s.error){var j="color: "+s.error(g,m)+"; font-weight: bold;";r[M]("%c error     ",j,g)}else r[M]("error     ",g);if(k)if(s.nextState){var R="color: "+s.nextState(y)+"; font-weight: bold";r[k]("%c next state",R,y)}else r[k]("next state",y);c&&b(m,y,r,C);try{r.groupEnd()}catch(t){r.log("—— log end ——")}}))}function C(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=Object.assign({},D,t),r=e.logger,n=e.stateTransformer,o=e.errorTransformer,i=e.predicate,a=e.logErrors,s=e.diffPredicate;if(void 0===r)return function(){return function(t){return function(e){return t(e)}}};if(t.getState&&t.dispatch)return function(){return function(t){return function(e){return t(e)}}};var l=[];return function(t){var r=t.getState;return function(t){return function(c){if("function"==typeof i&&!i(r,c))return t(c);var u={};l.push(u),u.started=L.now(),u.startedTime=new Date,u.prevState=n(r()),u.action=c;var d=void 0;if(a)try{d=t(c)}catch(t){u.error=o(t)}else d=t(c);u.took=L.now()-u.started,u.nextState=n(r());var f=e.diff&&"function"==typeof s?s(r,c):e.diff;if(S(l,Object.assign({},e,{diff:f})),l.length=0,u.error)throw u.error;return d}}}}var T,_,A=function(t,e){return new Array(e+1).join(t)},x=function(t,e){return A("0",e-t.toString().length)+t},N=function(t){return x(t.getHours(),2)+":"+x(t.getMinutes(),2)+":"+x(t.getSeconds(),2)+"."+x(t.getMilliseconds(),3)},L="undefined"!=typeof performance&&null!==performance&&"function"==typeof performance.now?performance:Date,O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M=function(t){if(Array.isArray(t)){for(var e=0,r=Array(t.length);e<t.length;e++)r[e]=t[e];return r}return Array.from(t)},k=[];T="object"===(void 0===r.g?"undefined":O(r.g))&&r.g?r.g:"undefined"!=typeof window?window:{},(_=T.DeepDiff)&&k.push((function(){void 0!==_&&T.DeepDiff===d&&(T.DeepDiff=_,_=void 0)})),e(o,n),e(i,n),e(a,n),e(s,n),Object.defineProperties(d,{diff:{value:d,enumerable:!0},observableDiff:{value:u,enumerable:!0},applyDiff:{value:g,enumerable:!0},applyChange:{value:h,enumerable:!0},revertChange:{value:m,enumerable:!0},isConflict:{value:function(){return void 0!==_},enumerable:!0},noConflict:{value:function(){return k&&(k.forEach((function(t){t()})),k=null),d},enumerable:!0}});var P={E:{color:"#2196F3",text:"CHANGED:"},N:{color:"#4CAF50",text:"ADDED:"},D:{color:"#F44336",text:"DELETED:"},A:{color:"#2196F3",text:"ARRAY:"}},D={level:"log",logger:console,logErrors:!0,collapsed:void 0,predicate:void 0,duration:!1,timestamp:!0,stateTransformer:function(t){return t},actionTransformer:function(t){return t},errorTransformer:function(t){return t},colors:{title:function(){return"inherit"},prevState:function(){return"#9E9E9E"},action:function(){return"#03A9F4"},nextState:function(){return"#4CAF50"},error:function(){return"#F20404"}},diff:!1,diffPredicate:void 0,transformer:void 0},j=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.dispatch,r=t.getState;return"function"==typeof e||"function"==typeof r?C()({dispatch:e,getState:r}):void 0};t.defaults=D,t.createLogger=C,t.logger=j,t.default=j,Object.defineProperty(t,"__esModule",{value:!0})}(e)},9449:(t,e)=>{"use strict";function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function n(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return 0===e.length?function(t){return t}:1===e.length?e[0]:e.reduce((function(t,e){return function(){return t(e.apply(void 0,arguments))}}))}Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){for(var e=arguments.length,o=new Array(e>1?e-1:0),i=1;i<e;i++)o[i-1]=arguments[i];var a=function(){throw new Error("Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.")},s={getState:t.getState.bind(t),dispatch:function(){return a.apply(void 0,arguments)}};return o=(o||[]).map((function(t){return t(s)})),a=n.apply(void 0,function(t){return function(t){if(Array.isArray(t))return r(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return r(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(o))(t.dispatch),t.dispatch=a,t}},9529:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.STATE_TYPE=e.PATCH_STATE_TYPE=e.FETCH_STATE_TYPE=e.DISPATCH_TYPE=e.DEFAULT_CHANNEL_NAME=void 0;e.DISPATCH_TYPE="webext.dispatch",e.FETCH_STATE_TYPE="webext.fetch_state",e.STATE_TYPE="webext.state",e.PATCH_STATE_TYPE="webext.patch_state",e.DEFAULT_CHANNEL_NAME="webext.channel"}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return t[n].call(i.exports,i,i.exports,r),i.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),(()=>{"use strict";function t(t){if("object"!=typeof t||null===t)return!1;let e=t;for(;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e||null===Object.getPrototypeOf(t)}function e(...t){return 0===t.length?t=>t:1===t.length?t[0]:t.reduce(((t,e)=>(...r)=>t(e(...r))))}var n=Symbol.for("immer-nothing"),o=Symbol.for("immer-draftable"),i=Symbol.for("immer-state");function a(t,...e){throw new Error(`[Immer] minified error nr: ${t}. Full error at: https://bit.ly/3cXEKWf`)}var s=Object.getPrototypeOf;function l(t){return!!t&&!!t[i]}function c(t){return!!t&&(d(t)||Array.isArray(t)||!!t[o]||!!t.constructor?.[o]||g(t)||v(t))}var u=Object.prototype.constructor.toString();function d(t){if(!t||"object"!=typeof t)return!1;const e=s(t);if(null===e)return!0;const r=Object.hasOwnProperty.call(e,"constructor")&&e.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===u}function f(t,e){0===h(t)?Reflect.ownKeys(t).forEach((r=>{e(r,t[r],t)})):t.forEach(((r,n)=>e(n,r,t)))}function h(t){const e=t[i];return e?e.type_:Array.isArray(t)?1:g(t)?2:v(t)?3:0}function p(t,e){return 2===h(t)?t.has(e):Object.prototype.hasOwnProperty.call(t,e)}function m(t,e,r){const n=h(t);2===n?t.set(e,r):3===n?t.add(r):t[e]=r}function g(t){return t instanceof Map}function v(t){return t instanceof Set}function y(t){return t.copy_||t.base_}function b(t,e){if(g(t))return new Map(t);if(v(t))return new Set(t);if(Array.isArray(t))return Array.prototype.slice.call(t);const r=d(t);if(!0===e||"class_only"===e&&!r){const e=Object.getOwnPropertyDescriptors(t);delete e[i];let r=Reflect.ownKeys(e);for(let n=0;n<r.length;n++){const o=r[n],i=e[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(e[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:t[o]})}return Object.create(s(t),e)}{const e=s(t);if(null!==e&&r)return{...t};const n=Object.create(e);return Object.assign(n,t)}}function E(t,e=!1){return S(t)||l(t)||!c(t)||(h(t)>1&&(t.set=t.add=t.clear=t.delete=w),Object.freeze(t),e&&Object.entries(t).forEach((([t,e])=>E(e,!0)))),t}function w(){a(2)}function S(t){return Object.isFrozen(t)}var C,T={};function _(t){const e=T[t];return e||a(0),e}function A(){return C}function x(t,e){e&&(_("Patches"),t.patches_=[],t.inversePatches_=[],t.patchListener_=e)}function N(t){L(t),t.drafts_.forEach(M),t.drafts_=null}function L(t){t===C&&(C=t.parent_)}function O(t){return C={drafts_:[],parent_:C,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function M(t){const e=t[i];0===e.type_||1===e.type_?e.revoke_():e.revoked_=!0}function k(t,e){e.unfinalizedDrafts_=e.drafts_.length;const r=e.drafts_[0];return void 0!==t&&t!==r?(r[i].modified_&&(N(e),a(4)),c(t)&&(t=P(e,t),e.parent_||j(e,t)),e.patches_&&_("Patches").generateReplacementPatches_(r[i].base_,t,e.patches_,e.inversePatches_)):t=P(e,r,[]),N(e),e.patches_&&e.patchListener_(e.patches_,e.inversePatches_),t!==n?t:void 0}function P(t,e,r){if(S(e))return e;const n=e[i];if(!n)return f(e,((o,i)=>D(t,n,e,o,i,r))),e;if(n.scope_!==t)return e;if(!n.modified_)return j(t,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const e=n.copy_;let o=e,i=!1;3===n.type_&&(o=new Set(e),e.clear(),i=!0),f(o,((o,a)=>D(t,n,e,o,a,r,i))),j(t,e,!1),r&&t.patches_&&_("Patches").generatePatches_(n,r,t.patches_,t.inversePatches_)}return n.copy_}function D(t,e,r,n,o,i,a){if(l(o)){const a=P(t,o,i&&e&&3!==e.type_&&!p(e.assigned_,n)?i.concat(n):void 0);if(m(r,n,a),!l(a))return;t.canAutoFreeze_=!1}else a&&r.add(o);if(c(o)&&!S(o)){if(!t.immer_.autoFreeze_&&t.unfinalizedDrafts_<1)return;P(t,o),e&&e.scope_.parent_||"symbol"==typeof n||!Object.prototype.propertyIsEnumerable.call(r,n)||j(t,o)}}function j(t,e,r=!1){!t.parent_&&t.immer_.autoFreeze_&&t.canAutoFreeze_&&E(e,r)}var R={get(t,e){if(e===i)return t;const r=y(t);if(!p(r,e))return function(t,e,r){const n=I(e,r);return n?"value"in n?n.value:n.get?.call(t.draft_):void 0}(t,r,e);const n=r[e];return t.finalized_||!c(n)?n:n===$(t.base_,e)?(B(t),t.copy_[e]=H(n,t)):n},has:(t,e)=>e in y(t),ownKeys:t=>Reflect.ownKeys(y(t)),set(t,e,r){const n=I(y(t),e);if(n?.set)return n.set.call(t.draft_,r),!0;if(!t.modified_){const n=$(y(t),e),s=n?.[i];if(s&&s.base_===r)return t.copy_[e]=r,t.assigned_[e]=!1,!0;if(((o=r)===(a=n)?0!==o||1/o==1/a:o!=o&&a!=a)&&(void 0!==r||p(t.base_,e)))return!0;B(t),F(t)}var o,a;return t.copy_[e]===r&&(void 0!==r||e in t.copy_)||Number.isNaN(r)&&Number.isNaN(t.copy_[e])||(t.copy_[e]=r,t.assigned_[e]=!0),!0},deleteProperty:(t,e)=>(void 0!==$(t.base_,e)||e in t.base_?(t.assigned_[e]=!1,B(t),F(t)):delete t.assigned_[e],t.copy_&&delete t.copy_[e],!0),getOwnPropertyDescriptor(t,e){const r=y(t),n=Reflect.getOwnPropertyDescriptor(r,e);return n?{writable:!0,configurable:1!==t.type_||"length"!==e,enumerable:n.enumerable,value:r[e]}:n},defineProperty(){a(11)},getPrototypeOf:t=>s(t.base_),setPrototypeOf(){a(12)}},q={};function $(t,e){const r=t[i];return(r?y(r):t)[e]}function I(t,e){if(!(e in t))return;let r=s(t);for(;r;){const t=Object.getOwnPropertyDescriptor(r,e);if(t)return t;r=s(r)}}function F(t){t.modified_||(t.modified_=!0,t.parent_&&F(t.parent_))}function B(t){t.copy_||(t.copy_=b(t.base_,t.scope_.immer_.useStrictShallowCopy_))}f(R,((t,e)=>{q[t]=function(){return arguments[0]=arguments[0][0],e.apply(this,arguments)}})),q.deleteProperty=function(t,e){return q.set.call(this,t,e,void 0)},q.set=function(t,e,r){return R.set.call(this,t[0],e,r,t[0])};function H(t,e){const r=g(t)?_("MapSet").proxyMap_(t,e):v(t)?_("MapSet").proxySet_(t,e):function(t,e){const r=Array.isArray(t),n={type_:r?1:0,scope_:e?e.scope_:A(),modified_:!1,finalized_:!1,assigned_:{},parent_:e,base_:t,draft_:null,copy_:null,revoke_:null,isManual_:!1};let o=n,i=R;r&&(o=[n],i=q);const{revoke:a,proxy:s}=Proxy.revocable(o,i);return n.draft_=s,n.revoke_=a,s}(t,e);return(e?e.scope_:A()).drafts_.push(r),r}function z(t){if(!c(t)||S(t))return t;const e=t[i];let r;if(e){if(!e.modified_)return e.base_;e.finalized_=!0,r=b(t,e.scope_.immer_.useStrictShallowCopy_)}else r=b(t,!0);return f(r,((t,e)=>{m(r,t,z(e))})),e&&(e.finalized_=!1),r}var U=new class{constructor(t){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,e,r)=>{if("function"==typeof t&&"function"!=typeof e){const r=e;e=t;const n=this;return function(t=r,...o){return n.produce(t,(t=>e.call(this,t,...o)))}}let o;if("function"!=typeof e&&a(6),void 0!==r&&"function"!=typeof r&&a(7),c(t)){const n=O(this),i=H(t,void 0);let a=!0;try{o=e(i),a=!1}finally{a?N(n):L(n)}return x(n,r),k(o,n)}if(!t||"object"!=typeof t){if(o=e(t),void 0===o&&(o=t),o===n&&(o=void 0),this.autoFreeze_&&E(o,!0),r){const e=[],n=[];_("Patches").generateReplacementPatches_(t,o,e,n),r(e,n)}return o}a(1)},this.produceWithPatches=(t,e)=>{if("function"==typeof t)return(e,...r)=>this.produceWithPatches(e,(e=>t(e,...r)));let r,n;return[this.produce(t,e,((t,e)=>{r=t,n=e})),r,n]},"boolean"==typeof t?.autoFreeze&&this.setAutoFreeze(t.autoFreeze),"boolean"==typeof t?.useStrictShallowCopy&&this.setUseStrictShallowCopy(t.useStrictShallowCopy)}createDraft(t){c(t)||a(8),l(t)&&(t=function(t){l(t)||a(10);return z(t)}(t));const e=O(this),r=H(t,void 0);return r[i].isManual_=!0,L(e),r}finishDraft(t,e){const r=t&&t[i];r&&r.isManual_||a(9);const{scope_:n}=r;return x(n,e),k(void 0,n)}setAutoFreeze(t){this.autoFreeze_=t}setUseStrictShallowCopy(t){this.useStrictShallowCopy_=t}applyPatches(t,e){let r;for(r=e.length-1;r>=0;r--){const n=e[r];if(0===n.path.length&&"replace"===n.op){t=n.value;break}}r>-1&&(e=e.slice(r+1));const n=_("Patches").applyPatches_;return l(t)?n(t,e):this.produce(t,(t=>n(t,e)))}},W=U.produce;U.produceWithPatches.bind(U),U.setAutoFreeze.bind(U),U.setUseStrictShallowCopy.bind(U),U.applyPatches.bind(U),U.createDraft.bind(U),U.finishDraft.bind(U);function Y(t,e="expected a function, instead received "+typeof t){if("function"!=typeof t)throw new TypeError(e)}var G=t=>Array.isArray(t)?t:[t];function V(t){const e=Array.isArray(t[0])?t[0]:t;return function(t,e="expected all items to be functions, instead received the following types: "){if(!t.every((t=>"function"==typeof t))){const r=t.map((t=>"function"==typeof t?`function ${t.name||"unnamed"}()`:typeof t)).join(", ");throw new TypeError(`${e}[${r}]`)}}(e,"createSelector expects all input-selectors to be functions, but received the following types: "),e}Symbol(),Object.getPrototypeOf({});var K="undefined"!=typeof WeakRef?WeakRef:class{constructor(t){this.value=t}deref(){return this.value}};function X(t,e={}){let r={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:n}=e;let o,i=0;function a(){let e=r;const{length:a}=arguments;for(let t=0,r=a;t<r;t++){const r=arguments[t];if("function"==typeof r||"object"==typeof r&&null!==r){let t=e.o;null===t&&(e.o=t=new WeakMap);const n=t.get(r);void 0===n?(e={s:0,v:void 0,o:null,p:null},t.set(r,e)):e=n}else{let t=e.p;null===t&&(e.p=t=new Map);const n=t.get(r);void 0===n?(e={s:0,v:void 0,o:null,p:null},t.set(r,e)):e=n}}const s=e;let l;if(1===e.s)l=e.v;else if(l=t.apply(null,arguments),i++,n){const t=o?.deref?.()??o;null!=t&&n(t,l)&&(l=t,0!==i&&i--);o="object"==typeof l&&null!==l||"function"==typeof l?new K(l):l}return s.s=1,s.v=l,l}return a.clearCache=()=>{r={s:0,v:void 0,o:null,p:null},a.resetResultsCount()},a.resultsCount=()=>i,a.resetResultsCount=()=>{i=0},a}function J(t,...e){const r="function"==typeof t?{memoize:t,memoizeOptions:e}:t,n=(...t)=>{let e,n=0,o=0,i={},a=t.pop();"object"==typeof a&&(i=a,a=t.pop()),Y(a,`createSelector expects an output function after the inputs, but received: [${typeof a}]`);const s={...r,...i},{memoize:l,memoizeOptions:c=[],argsMemoize:u=X,argsMemoizeOptions:d=[],devModeChecks:f={}}=s,h=G(c),p=G(d),m=V(t),g=l((function(){return n++,a.apply(null,arguments)}),...h);const v=u((function(){o++;const t=function(t,e){const r=[],{length:n}=t;for(let o=0;o<n;o++)r.push(t[o].apply(null,e));return r}(m,arguments);return e=g.apply(null,t),e}),...p);return Object.assign(v,{resultFunc:a,memoizedResultFunc:g,dependencies:m,dependencyRecomputations:()=>o,resetDependencyRecomputations:()=>{o=0},lastResult:()=>e,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:l,argsMemoize:u})};return Object.assign(n,{withTypes:()=>n}),n}var Q=J(X),Z=Object.assign(((t,e=Q)=>{!function(t,e="expected an object, instead received "+typeof t){if("object"!=typeof t)throw new TypeError(e)}(t,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof t);const r=Object.keys(t);return e(r.map((e=>t[e])),((...t)=>t.reduce(((t,e,n)=>(t[r[n]]=e,t)),{})))}),{withTypes:()=>Z});function tt(t){return({dispatch:e,getState:r})=>n=>o=>"function"==typeof o?o(e,r,t):n(o)}var et=tt();"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__,"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function rt(e,r){function n(...t){if(r){let n=r(...t);if(!n)throw new Error(ht(0));return{type:e,payload:n.payload,..."meta"in n&&{meta:n.meta},..."error"in n&&{error:n.error}}}return{type:e,payload:t[0]}}return n.toString=()=>`${e}`,n.type=e,n.match=r=>function(e){return t(e)&&"type"in e&&"string"==typeof e.type}(r)&&r.type===e,n}Symbol.species;function nt(t){return c(t)?W(t,(()=>{})):t}function ot(t,e,r){return t.has(e)?t.get(e):t.set(e,r(e)).get(e)}function it(t){const e={},r=[];let n;const o={addCase(t,r){const n="string"==typeof t?t:t.type;if(!n)throw new Error(ht(28));if(n in e)throw new Error(ht(29));return e[n]=r,o},addMatcher:(t,e)=>(r.push({matcher:t,reducer:e}),o),addDefaultCase:t=>(n=t,o)};return t(o),[e,r,n]}var at=Symbol.for("rtk-slice-createasyncthunk");function st(t,e){return`${t}/${e}`}function lt({creators:t}={}){const e=t?.asyncThunk?.[at];return function(t){const{name:r,reducerPath:n=r}=t;if(!r)throw new Error(ht(11));const o=("function"==typeof t.reducers?t.reducers(function(){function t(t,e){return{_reducerDefinitionType:"asyncThunk",payloadCreator:t,...e}}return t.withTypes=()=>t,{reducer:t=>Object.assign({[t.name]:(...e)=>t(...e)}[t.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(t,e)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:t,reducer:e}),asyncThunk:t}}()):t.reducers)||{},i=Object.keys(o),a={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},s={addCase(t,e){const r="string"==typeof t?t:t.type;if(!r)throw new Error(ht(12));if(r in a.sliceCaseReducersByType)throw new Error(ht(13));return a.sliceCaseReducersByType[r]=e,s},addMatcher:(t,e)=>(a.sliceMatchers.push({matcher:t,reducer:e}),s),exposeAction:(t,e)=>(a.actionCreators[t]=e,s),exposeCaseReducer:(t,e)=>(a.sliceCaseReducersByName[t]=e,s)};function u(){const[e={},r=[],n]="function"==typeof t.extraReducers?it(t.extraReducers):[t.extraReducers],o={...e,...a.sliceCaseReducersByType};return function(t,e){let r,[n,o,i]=it(e);if("function"==typeof t)r=()=>nt(t());else{const e=nt(t);r=()=>e}function a(t=r(),e){let a=[n[e.type],...o.filter((({matcher:t})=>t(e))).map((({reducer:t})=>t))];return 0===a.filter((t=>!!t)).length&&(a=[i]),a.reduce(((t,r)=>{if(r){if(l(t)){const n=r(t,e);return void 0===n?t:n}if(c(t))return W(t,(t=>r(t,e)));{const n=r(t,e);if(void 0===n){if(null===t)return t;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}}return t}),t)}return a.getInitialState=r,a}(t.initialState,(t=>{for(let e in o)t.addCase(e,o[e]);for(let e of a.sliceMatchers)t.addMatcher(e.matcher,e.reducer);for(let e of r)t.addMatcher(e.matcher,e.reducer);n&&t.addDefaultCase(n)}))}i.forEach((n=>{const i=o[n],a={reducerName:n,type:st(r,n),createNotation:"function"==typeof t.reducers};!function(t){return"asyncThunk"===t._reducerDefinitionType}(i)?function({type:t,reducerName:e,createNotation:r},n,o){let i,a;if("reducer"in n){if(r&&!function(t){return"reducerWithPrepare"===t._reducerDefinitionType}(n))throw new Error(ht(17));i=n.reducer,a=n.prepare}else i=n;o.addCase(t,i).exposeCaseReducer(e,i).exposeAction(e,a?rt(t,a):rt(t))}(a,i,s):function({type:t,reducerName:e},r,n,o){if(!o)throw new Error(ht(18));const{payloadCreator:i,fulfilled:a,pending:s,rejected:l,settled:c,options:u}=r,d=o(t,i,u);n.exposeAction(e,d),a&&n.addCase(d.fulfilled,a);s&&n.addCase(d.pending,s);l&&n.addCase(d.rejected,l);c&&n.addMatcher(d.settled,c);n.exposeCaseReducer(e,{fulfilled:a||dt,pending:s||dt,rejected:l||dt,settled:c||dt})}(a,i,s,e)}));const d=t=>t,f=new Map,h=new WeakMap;let p;function m(t,e){return p||(p=u()),p(t,e)}function g(){return p||(p=u()),p.getInitialState()}function v(e,r=!1){function n(t){let o=t[e];return void 0===o&&r&&(o=ot(h,n,g)),o}function o(e=d){const n=ot(f,r,(()=>new WeakMap));return ot(n,e,(()=>{const n={};for(const[o,i]of Object.entries(t.selectors??{}))n[o]=ct(i,e,(()=>ot(h,e,g)),r);return n}))}return{reducerPath:e,getSelectors:o,get selectors(){return o(n)},selectSlice:n}}const y={name:r,reducer:m,actions:a.actionCreators,caseReducers:a.sliceCaseReducersByName,getInitialState:g,...v(n),injectInto(t,{reducerPath:e,...r}={}){const o=e??n;return t.inject({reducerPath:o,reducer:m},r),{...y,...v(o,!0)}}};return y}}function ct(t,e,r,n){function o(o,...i){let a=e(o);return void 0===a&&n&&(a=r()),t(a,...i)}return o.unwrapped=t,o}var ut=lt();function dt(){}var{assign:ft}=Object;Symbol.for("rtk-state-proxy-original");function ht(t){return`Minified Redux Toolkit error #${t}; visit https://redux-toolkit.js.org/Errors?code=${t} for the full message or use the non-minified dev environment for full errors. `}const pt={isLoaded:!1},mt=ut({name:"content",initialState:pt,reducers:{reset:()=>pt,contentLoaded:t=>{t.isLoaded=!0}}}),{actions:gt,reducer:vt}=mt;var yt,bt,Et=r(9448),wt=r(3207);!function(t){t.Default="default",t.ConfirmDeleteCard="confirmDeleteCard"}(yt||(yt={})),function(t){t.Default="default"}(bt||(bt={}));const St={isOpen:!1},Ct=ut({name:"sidePanel",initialState:St,reducers:{reset:()=>St}}),{actions:Tt,reducer:_t}=Ct,At={},xt=((0,wt.nK)(At),Et.logger,[(0,wt.nK)(At),et,()=>t=>e=>t(e),Et.logger]);(0,wt.nK)(At),Et.logger;var Nt;!function(t){t.ContentPort="content",t.SidePanelPort="sidePanel",t.SAVE_NOTE_TO_FILE="save-note-to-file"}(Nt||(Nt={}));const Lt=Nt;var Ot=r(609),Mt=r.n(Ot),kt=/highlight-(?:text|source)-([a-z0-9]+)/;function Pt(t){t.addRule("highlightedCodeBlock",{filter:function(t){var e=t.firstChild;return"DIV"===t.nodeName&&kt.test(t.className)&&e&&"PRE"===e.nodeName},replacement:function(t,e,r){var n=((e.className||"").match(kt)||[null,""])[1];return"\n\n"+r.fence+n+"\n"+e.firstChild.textContent+"\n"+r.fence+"\n\n"}})}function Dt(t){t.addRule("strikethrough",{filter:["del","s","strike"],replacement:function(t){return"~"+t+"~"}})}var jt=Array.prototype.indexOf,Rt=Array.prototype.every,qt={};function $t(t){var e,r,n=t.parentNode;return"THEAD"===n.nodeName||n.firstChild===t&&("TABLE"===n.nodeName||(r=(e=n).previousSibling,"TBODY"===e.nodeName&&(!r||"THEAD"===r.nodeName&&/^\s*$/i.test(r.textContent))))&&Rt.call(t.childNodes,(function(t){return"TH"===t.nodeName}))}function It(t,e){var r=" ";return 0===jt.call(e.parentNode.childNodes,e)&&(r="| "),r+t+" |"}function Ft(t){for(var e in t.keep((function(t){return"TABLE"===t.nodeName&&!$t(t.rows[0])})),qt)t.addRule(e,qt[e])}function Bt(t){t.addRule("taskListItems",{filter:function(t){return"checkbox"===t.type&&"LI"===t.parentNode.nodeName},replacement:function(t,e){return(e.checked?"[x]":"[ ]")+" "}})}function Ht(t){t.use([Pt,Dt,Ft,Bt])}function zt(t,e){return Array(e+1).join(t)}qt.tableCell={filter:["th","td"],replacement:function(t,e){return It(t,e)}},qt.tableRow={filter:"tr",replacement:function(t,e){var r="",n={left:":--",right:"--:",center:":-:"};if($t(e))for(var o=0;o<e.childNodes.length;o++){var i="---",a=(e.childNodes[o].getAttribute("align")||"").toLowerCase();a&&(i=n[a]||i),r+=It(i,e.childNodes[o])}return"\n"+t+(r?"\n"+r:"")}},qt.table={filter:function(t){return"TABLE"===t.nodeName&&$t(t.rows[0])},replacement:function(t){return"\n\n"+(t=t.replace("\n\n","\n"))+"\n\n"}},qt.tableSection={filter:["thead","tbody","tfoot"],replacement:function(t){return t}};var Ut=["ADDRESS","ARTICLE","ASIDE","AUDIO","BLOCKQUOTE","BODY","CANVAS","CENTER","DD","DIR","DIV","DL","DT","FIELDSET","FIGCAPTION","FIGURE","FOOTER","FORM","FRAMESET","H1","H2","H3","H4","H5","H6","HEADER","HGROUP","HR","HTML","ISINDEX","LI","MAIN","MENU","NAV","NOFRAMES","NOSCRIPT","OL","OUTPUT","P","PRE","SECTION","TABLE","TBODY","TD","TFOOT","TH","THEAD","TR","UL"];function Wt(t){return Kt(t,Ut)}var Yt=["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"];function Gt(t){return Kt(t,Yt)}var Vt=["A","TABLE","THEAD","TBODY","TFOOT","TH","TD","IFRAME","SCRIPT","AUDIO","VIDEO"];function Kt(t,e){return e.indexOf(t.nodeName)>=0}function Xt(t,e){return t.getElementsByTagName&&e.some((function(e){return t.getElementsByTagName(e).length}))}var Jt={};function Qt(t){return t?t.replace(/(\n+\s*)+/g,"\n"):""}function Zt(t){for(var e in this.options=t,this._keep=[],this._remove=[],this.blankRule={replacement:t.blankReplacement},this.keepReplacement=t.keepReplacement,this.defaultRule={replacement:t.defaultReplacement},this.array=[],t.rules)this.array.push(t.rules[e])}function te(t,e,r){for(var n=0;n<t.length;n++){var o=t[n];if(ee(o,e,r))return o}}function ee(t,e,r){var n=t.filter;if("string"==typeof n){if(n===e.nodeName.toLowerCase())return!0}else if(Array.isArray(n)){if(n.indexOf(e.nodeName.toLowerCase())>-1)return!0}else{if("function"!=typeof n)throw new TypeError("`filter` needs to be a string, array, or function");if(n.call(t,e,r))return!0}}function re(t){var e=t.nextSibling||t.parentNode;return t.parentNode.removeChild(t),e}function ne(t,e,r){return t&&t.parentNode===e||r(e)?e.nextSibling||e.parentNode:e.firstChild||e.nextSibling||e.parentNode}Jt.paragraph={filter:"p",replacement:function(t){return"\n\n"+t+"\n\n"}},Jt.lineBreak={filter:"br",replacement:function(t,e,r){return r.br+"\n"}},Jt.heading={filter:["h1","h2","h3","h4","h5","h6"],replacement:function(t,e,r){var n=Number(e.nodeName.charAt(1));return"setext"===r.headingStyle&&n<3?"\n\n"+t+"\n"+zt(1===n?"=":"-",t.length)+"\n\n":"\n\n"+zt("#",n)+" "+t+"\n\n"}},Jt.blockquote={filter:"blockquote",replacement:function(t){return"\n\n"+(t=(t=t.replace(/^\n+|\n+$/g,"")).replace(/^/gm,"> "))+"\n\n"}},Jt.list={filter:["ul","ol"],replacement:function(t,e){var r=e.parentNode;return"LI"===r.nodeName&&r.lastElementChild===e?"\n"+t:"\n\n"+t+"\n\n"}},Jt.listItem={filter:"li",replacement:function(t,e,r){t=t.replace(/^\n+/,"").replace(/\n+$/,"\n").replace(/\n/gm,"\n    ");var n=r.bulletListMarker+"   ",o=e.parentNode;if("OL"===o.nodeName){var i=o.getAttribute("start"),a=Array.prototype.indexOf.call(o.children,e);n=(i?Number(i)+a:a+1)+".  "}return n+t+(e.nextSibling&&!/\n$/.test(t)?"\n":"")}},Jt.indentedCodeBlock={filter:function(t,e){return"indented"===e.codeBlockStyle&&"PRE"===t.nodeName&&t.firstChild&&"CODE"===t.firstChild.nodeName},replacement:function(t,e,r){return"\n\n    "+e.firstChild.textContent.replace(/\n/g,"\n    ")+"\n\n"}},Jt.fencedCodeBlock={filter:function(t,e){return"fenced"===e.codeBlockStyle&&"PRE"===t.nodeName&&t.firstChild&&"CODE"===t.firstChild.nodeName},replacement:function(t,e,r){for(var n,o=((e.firstChild.getAttribute("class")||"").match(/language-(\S+)/)||[null,""])[1],i=e.firstChild.textContent,a=r.fence.charAt(0),s=3,l=new RegExp("^"+a+"{3,}","gm");n=l.exec(i);)n[0].length>=s&&(s=n[0].length+1);var c=zt(a,s);return"\n\n"+c+o+"\n"+i.replace(/\n$/,"")+"\n"+c+"\n\n"}},Jt.horizontalRule={filter:"hr",replacement:function(t,e,r){return"\n\n"+r.hr+"\n\n"}},Jt.inlineLink={filter:function(t,e){return"inlined"===e.linkStyle&&"A"===t.nodeName&&t.getAttribute("href")},replacement:function(t,e){var r=e.getAttribute("href");r&&(r=r.replace(/([()])/g,"\\$1"));var n=Qt(e.getAttribute("title"));return n&&(n=' "'+n.replace(/"/g,'\\"')+'"'),"["+t+"]("+r+n+")"}},Jt.referenceLink={filter:function(t,e){return"referenced"===e.linkStyle&&"A"===t.nodeName&&t.getAttribute("href")},replacement:function(t,e,r){var n,o,i=e.getAttribute("href"),a=Qt(e.getAttribute("title"));switch(a&&(a=' "'+a+'"'),r.linkReferenceStyle){case"collapsed":n="["+t+"][]",o="["+t+"]: "+i+a;break;case"shortcut":n="["+t+"]",o="["+t+"]: "+i+a;break;default:var s=this.references.length+1;n="["+t+"]["+s+"]",o="["+s+"]: "+i+a}return this.references.push(o),n},references:[],append:function(t){var e="";return this.references.length&&(e="\n\n"+this.references.join("\n")+"\n\n",this.references=[]),e}},Jt.emphasis={filter:["em","i"],replacement:function(t,e,r){return t.trim()?r.emDelimiter+t+r.emDelimiter:""}},Jt.strong={filter:["strong","b"],replacement:function(t,e,r){return t.trim()?r.strongDelimiter+t+r.strongDelimiter:""}},Jt.code={filter:function(t){var e=t.previousSibling||t.nextSibling,r="PRE"===t.parentNode.nodeName&&!e;return"CODE"===t.nodeName&&!r},replacement:function(t){if(!t)return"";t=t.replace(/\r?\n|\r/g," ");for(var e=/^`|^ .*?[^ ].* $|`$/.test(t)?" ":"",r="`",n=t.match(/`+/gm)||[];-1!==n.indexOf(r);)r+="`";return r+e+t+e+r}},Jt.image={filter:"img",replacement:function(t,e){var r=Qt(e.getAttribute("alt")),n=e.getAttribute("src")||"",o=Qt(e.getAttribute("title"));return n?"!["+r+"]("+n+(o?' "'+o+'"':"")+")":""}},Zt.prototype={add:function(t,e){this.array.unshift(e)},keep:function(t){this._keep.unshift({filter:t,replacement:this.keepReplacement})},remove:function(t){this._remove.unshift({filter:t,replacement:function(){return""}})},forNode:function(t){return t.isBlank?this.blankRule:(e=te(this.array,t,this.options))||(e=te(this._keep,t,this.options))||(e=te(this._remove,t,this.options))?e:this.defaultRule;var e},forEach:function(t){for(var e=0;e<this.array.length;e++)t(this.array[e],e)}};var oe="undefined"!=typeof window?window:{};var ie,ae,se=function(){var t=oe.DOMParser,e=!1;try{(new t).parseFromString("","text/html")&&(e=!0)}catch(r){}return e}()?oe.DOMParser:(ie=function(){},function(){var t=!1;try{document.implementation.createHTMLDocument("").open()}catch(e){oe.ActiveXObject&&(t=!0)}return t}()?ie.prototype.parseFromString=function(t){var e=new window.ActiveXObject("htmlfile");return e.designMode="on",e.open(),e.write(t),e.close(),e}:ie.prototype.parseFromString=function(t){var e=document.implementation.createHTMLDocument("");return e.open(),e.write(t),e.close(),e},ie);function le(t,e){var r;"string"==typeof t?r=(ae=ae||new se).parseFromString('<x-turndown id="turndown-root">'+t+"</x-turndown>","text/html").getElementById("turndown-root"):r=t.cloneNode(!0);return function(t){var e=t.element,r=t.isBlock,n=t.isVoid,o=t.isPre||function(t){return"PRE"===t.nodeName};if(e.firstChild&&!o(e)){for(var i=null,a=!1,s=null,l=ne(s,e,o);l!==e;){if(3===l.nodeType||4===l.nodeType){var c=l.data.replace(/[ \r\n\t]+/g," ");if(i&&!/ $/.test(i.data)||a||" "!==c[0]||(c=c.substr(1)),!c){l=re(l);continue}l.data=c,i=l}else{if(1!==l.nodeType){l=re(l);continue}r(l)||"BR"===l.nodeName?(i&&(i.data=i.data.replace(/ $/,"")),i=null,a=!1):n(l)||o(l)?(i=null,a=!0):i&&(a=!1)}var u=ne(s,l,o);s=l,l=u}i&&(i.data=i.data.replace(/ $/,""),i.data||re(i))}}({element:r,isBlock:Wt,isVoid:Gt,isPre:e.preformattedCode?ce:null}),r}function ce(t){return"PRE"===t.nodeName||"CODE"===t.nodeName}function ue(t,e){return t.isBlock=Wt(t),t.isCode="CODE"===t.nodeName||t.parentNode.isCode,t.isBlank=function(t){return!Gt(t)&&!function(t){return Kt(t,Vt)}(t)&&/^\s*$/i.test(t.textContent)&&!function(t){return Xt(t,Yt)}(t)&&!function(t){return Xt(t,Vt)}(t)}(t),t.flankingWhitespace=function(t,e){if(t.isBlock||e.preformattedCode&&t.isCode)return{leading:"",trailing:""};var r=(n=t.textContent,o=n.match(/^(([ \t\r\n]*)(\s*))(?:(?=\S)[\s\S]*\S)?((\s*?)([ \t\r\n]*))$/),{leading:o[1],leadingAscii:o[2],leadingNonAscii:o[3],trailing:o[4],trailingNonAscii:o[5],trailingAscii:o[6]});var n,o;r.leadingAscii&&de("left",t,e)&&(r.leading=r.leadingNonAscii);r.trailingAscii&&de("right",t,e)&&(r.trailing=r.trailingNonAscii);return{leading:r.leading,trailing:r.trailing}}(t,e),t}function de(t,e,r){var n,o,i;return"left"===t?(n=e.previousSibling,o=/ $/):(n=e.nextSibling,o=/^ /),n&&(3===n.nodeType?i=o.test(n.nodeValue):r.preformattedCode&&"CODE"===n.nodeName?i=!1:1!==n.nodeType||Wt(n)||(i=o.test(n.textContent))),i}var fe=Array.prototype.reduce,he=[[/\\/g,"\\\\"],[/\*/g,"\\*"],[/^-/g,"\\-"],[/^\+ /g,"\\+ "],[/^(=+)/g,"\\$1"],[/^(#{1,6}) /g,"\\$1 "],[/`/g,"\\`"],[/^~~~/g,"\\~~~"],[/\[/g,"\\["],[/\]/g,"\\]"],[/^>/g,"\\>"],[/_/g,"\\_"],[/^(\d+)\. /g,"$1\\. "]];function pe(t){if(!(this instanceof pe))return new pe(t);var e={rules:Jt,headingStyle:"setext",hr:"* * *",bulletListMarker:"*",codeBlockStyle:"indented",fence:"```",emDelimiter:"_",strongDelimiter:"**",linkStyle:"inlined",linkReferenceStyle:"full",br:"  ",preformattedCode:!1,blankReplacement:function(t,e){return e.isBlock?"\n\n":""},keepReplacement:function(t,e){return e.isBlock?"\n\n"+e.outerHTML+"\n\n":e.outerHTML},defaultReplacement:function(t,e){return e.isBlock?"\n\n"+t+"\n\n":t}};this.options=function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)r.hasOwnProperty(n)&&(t[n]=r[n])}return t}({},e,t),this.rules=new Zt(this.options)}function me(t){var e=this;return fe.call(t.childNodes,(function(t,r){var n="";return 3===(r=new ue(r,e.options)).nodeType?n=r.isCode?r.nodeValue:e.escape(r.nodeValue):1===r.nodeType&&(n=ve.call(e,r)),ye(t,n)}),"")}function ge(t){var e=this;return this.rules.forEach((function(r){"function"==typeof r.append&&(t=ye(t,r.append(e.options)))})),t.replace(/^[\t\r\n]+/,"").replace(/[\t\r\n\s]+$/,"")}function ve(t){var e=this.rules.forNode(t),r=me.call(this,t),n=t.flankingWhitespace;return(n.leading||n.trailing)&&(r=r.trim()),n.leading+e.replacement(r,t,this.options)+n.trailing}function ye(t,e){var r=function(t){for(var e=t.length;e>0&&"\n"===t[e-1];)e--;return t.substring(0,e)}(t),n=e.replace(/^\n*/,""),o=Math.max(t.length-r.length,e.length-n.length);return r+"\n\n".substring(0,o)+n}pe.prototype={turndown:function(t){if(!function(t){return null!=t&&("string"==typeof t||t.nodeType&&(1===t.nodeType||9===t.nodeType||11===t.nodeType))}(t))throw new TypeError(t+" is not a string, or an element/document/fragment node.");if(""===t)return"";var e=me.call(this,new le(t,this.options));return ge.call(this,e)},use:function(t){if(Array.isArray(t))for(var e=0;e<t.length;e++)this.use(t[e]);else{if("function"!=typeof t)throw new TypeError("plugin must be a Function or an Array of Functions");t(this)}return this},addRule:function(t,e){return this.rules.add(t,e),this},keep:function(t){return this.rules.keep(t),this},remove:function(t){return this.rules.remove(t),this},escape:function(t){return he.reduce((function(t,e){return t.replace(e[0],e[1])}),t)}};const be=pe;(async()=>{try{if("chrome:"===window.location.protocol||"chrome-extension:"===window.location.protocol)return;const e=(t=>{const e=new wt.il({channelName:t});return(0,wt.Tw)(e,...xt),e})(Lt.ContentPort);try{await e.ready(),e.dispatch((async(t,e)=>{const{isLoaded:r}=e().content||{};r||await t(mt.actions.contentLoaded())}))}catch(t){}}catch(e){}})(),chrome.runtime.onMessage.addListener(((t,e,r)=>{if("DEFUDDLE_PAGE_CONTENT"===t.type){let t=null;try{if("application/pdf"===document.contentType)return r({success:!1,error:"Cannot defuddle PDF content directly. Please save or copy text manually.",title:document.title||"PDF Document"}),!0;if(void 0===Mt())return r({success:!1,error:"Defuddle library not available.",title:document.title}),!0;const e=new(Mt())(document,{markdown:!1,url:document.location.href}).parse();if(void 0===be)return r({success:!1,error:"TurndownService library not available.",title:document.title}),!0;t=new be({headingStyle:"atx",codeBlockStyle:"fenced"}).use(Ht);const n=t.turndown(e.content||""),o=document.querySelector("h1, h2, h3")?.textContent?.trim(),i=document.title||"Untitled Note";r({success:!0,title:o||e.title||i,content:n})}catch(n){r({success:!1,error:n.message,title:document.title})}return!0}}))})()})();