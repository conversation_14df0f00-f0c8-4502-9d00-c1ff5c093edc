"use strict";(globalThis.webpackChunkchromepanion=globalThis.webpackChunkchromepanion||[]).push([[100],{1100:(t,e,i)=>{i.r(e),i.d(e,{AbortException:()=>tt,AnnotationEditorLayer:()=>rn,AnnotationEditorParamsType:()=>f,AnnotationEditorType:()=>m,AnnotationEditorUIManager:()=>Gt,AnnotationLayer:()=>Fs,AnnotationMode:()=>g,AnnotationType:()=>E,ColorPicker:()=>Vs,DOMSVGFactory:()=>ns,DrawLayer:()=>on,FeatureTest:()=>st,GlobalWorkerOptions:()=>ui,ImageKind:()=>S,InvalidPDFException:()=>Q,MathClamp:()=>ct,OPS:()=>D,OutputScale:()=>Dt,PDFDataRangeTransport:()=>Vi,PDFDateString:()=>Ct,PDFWorker:()=>Xi,PasswordResponses:()=>O,PermissionFlag:()=>b,PixelsPerInch:()=>gt,RenderingCancelledException:()=>bt,ResponseException:()=>J,SignatureExtractor:()=>tn,SupportedImageMimeTypes:()=>Rt,TextLayer:()=>Ui,TouchManager:()=>jt,Util:()=>at,VerbosityLevel:()=>k,XfaLayer:()=>as,build:()=>Zi,createValidAbsoluteUrl:()=>V,fetchData:()=>mt,getDocument:()=>$i,getFilenameFromUrl:()=>wt,getPdfFilenameFromUrl:()=>yt,getUuid:()=>ht,getXfaPageViewport:()=>Tt,isDataScheme:()=>vt,isPdfFile:()=>At,isValidExplicitDest:()=>ne,noContextMenu:()=>St,normalizeUnicode:()=>lt,setLayerDimensions:()=>kt,shadow:()=>q,stopEvent:()=>Et,updateUrlHash:()=>W,version:()=>Ji});const s=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type),n=[.001,0,0,.001,0,0],a=1.35,r=1,o=2,l=4,h=16,d=32,c=64,u=128,p=256,g={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},m={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15,SIGNATURE:101},f={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35,DRAW_STEP:41},b={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},v=0,A=1,w=2,y=3,_=3,x=4,S={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},E={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},C=1,T=2,M=3,P=4,I=5,k={ERRORS:0,WARNINGS:1,INFOS:5},D={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93,rawFillPath:94},R=0,L=1,F=2,N=3,O={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let B=k.WARNINGS;function z(t){Number.isInteger(t)&&(B=t)}function H(){return B}function U(t){k.INFOS}function G(t){k.WARNINGS}function $(t){throw new Error(t)}function j(t,e){t||$(e)}function V(t,e=null,i=null){if(!t)return null;if(i&&"string"==typeof t){if(i.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);e?.length>=2&&(t=`http://${t}`)}if(i.tryConvertEncoding)try{t=decodeURIComponent(escape(t))}catch{}}const s=e?URL.parse(t,e):URL.parse(t);return function(t){switch(t?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(s)?s:null}function W(t,e,i=!1){const s=URL.parse(t);return s?(s.hash=e,s.href):i&&V(t,"http://example.com")?t.split("#",1)[0]+""+(e?`#${e}`:""):""}function q(t,e,i,s=!1){return Object.defineProperty(t,e,{value:i,enumerable:!s,configurable:!0,writable:!1}),i}const X=function(){function t(t,e){this.message=t,this.name=e}return t.prototype=new Error,t.constructor=t,t}();class K extends X{constructor(t,e){super(t,"PasswordException"),this.code=e}}class Y extends X{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class Q extends X{constructor(t){super(t,"InvalidPDFException")}}class J extends X{constructor(t,e,i){super(t,"ResponseException"),this.status=e,this.missing=i}}class Z extends X{constructor(t){super(t,"FormatError")}}class tt extends X{constructor(t){super(t,"AbortException")}}function et(t){"object"==typeof t&&void 0!==t?.length||$("Invalid argument for bytesToString");const e=t.length,i=8192;if(e<i)return String.fromCharCode.apply(null,t);const s=[];for(let n=0;n<e;n+=i){const a=Math.min(n+i,e),r=t.subarray(n,a);s.push(String.fromCharCode.apply(null,r))}return s.join("")}function it(t){"string"!=typeof t&&$("Invalid argument for stringToBytes");const e=t.length,i=new Uint8Array(e);for(let s=0;s<e;++s)i[s]=255&t.charCodeAt(s);return i}class st{static get isLittleEndian(){return q(this,"isLittleEndian",function(){const t=new Uint8Array(4);return t[0]=1,1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return q(this,"isEvalSupported",function(){try{return new Function(""),!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return q(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get isImageDecoderSupported(){return q(this,"isImageDecoderSupported","undefined"!=typeof ImageDecoder)}static get platform(){const{platform:t,userAgent:e}=navigator;return q(this,"platform",{isAndroid:e.includes("Android"),isLinux:t.includes("Linux"),isMac:t.includes("Mac"),isWindows:t.includes("Win"),isFirefox:e.includes("Firefox")})}static get isCSSRoundSupported(){return q(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const nt=Array.from(Array(256).keys(),(t=>t.toString(16).padStart(2,"0")));class at{static makeHexColor(t,e,i){return`#${nt[t]}${nt[e]}${nt[i]}`}static scaleMinMax(t,e){let i;t[0]?(t[0]<0&&(i=e[0],e[0]=e[2],e[2]=i),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(i=e[1],e[1]=e[3],e[3]=i),e[1]*=t[3],e[3]*=t[3]):(i=e[0],e[0]=e[1],e[1]=i,i=e[2],e[2]=e[3],e[3]=i,t[1]<0&&(i=e[1],e[1]=e[3],e[3]=i),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(i=e[0],e[0]=e[2],e[2]=i),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e,i=0){const s=t[i],n=t[i+1];t[i]=s*e[0]+n*e[2]+e[4],t[i+1]=s*e[1]+n*e[3]+e[5]}static applyTransformToBezier(t,e,i=0){const s=e[0],n=e[1],a=e[2],r=e[3],o=e[4],l=e[5];for(let h=0;h<6;h+=2){const e=t[i+h],d=t[i+h+1];t[i+h]=e*s+d*a+o,t[i+h+1]=e*n+d*r+l}}static applyInverseTransform(t,e){const i=t[0],s=t[1],n=e[0]*e[3]-e[1]*e[2];t[0]=(i*e[3]-s*e[2]+e[2]*e[5]-e[4]*e[3])/n,t[1]=(-i*e[1]+s*e[0]+e[4]*e[1]-e[5]*e[0])/n}static axialAlignedBoundingBox(t,e,i){const s=e[0],n=e[1],a=e[2],r=e[3],o=e[4],l=e[5],h=t[0],d=t[1],c=t[2],u=t[3];let p=s*h+o,g=p,m=s*c+o,f=m,b=r*d+l,v=b,A=r*u+l,w=A;if(0!==n||0!==a){const t=n*h,e=n*c,i=a*d,s=a*u;p+=i,f+=i,m+=s,g+=s,b+=t,w+=t,A+=e,v+=e}i[0]=Math.min(i[0],p,m,g,f),i[1]=Math.min(i[1],b,A,v,w),i[2]=Math.max(i[2],p,m,g,f),i[3]=Math.max(i[3],b,A,v,w)}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t,e){const i=t[0],s=t[1],n=t[2],a=t[3],r=i**2+s**2,o=i*n+s*a,l=n**2+a**2,h=(r+l)/2,d=Math.sqrt(h**2-(r*l-o**2));e[0]=Math.sqrt(h+d||1),e[1]=Math.sqrt(h-d||1)}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const i=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),s=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(i>s)return null;const n=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),a=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return n>a?null:[i,n,s,a]}static pointBoundingBox(t,e,i){i[0]=Math.min(i[0],t),i[1]=Math.min(i[1],e),i[2]=Math.max(i[2],t),i[3]=Math.max(i[3],e)}static rectBoundingBox(t,e,i,s,n){n[0]=Math.min(n[0],t,i),n[1]=Math.min(n[1],e,s),n[2]=Math.max(n[2],t,i),n[3]=Math.max(n[3],e,s)}static#t(t,e,i,s,n,a,r,o,l,h){if(l<=0||l>=1)return;const d=1-l,c=l*l,u=c*l,p=d*(d*(d*t+3*l*e)+3*c*i)+u*s,g=d*(d*(d*n+3*l*a)+3*c*r)+u*o;h[0]=Math.min(h[0],p),h[1]=Math.min(h[1],g),h[2]=Math.max(h[2],p),h[3]=Math.max(h[3],g)}static#e(t,e,i,s,n,a,r,o,l,h,d,c){if(Math.abs(l)<1e-12)return void(Math.abs(h)>=1e-12&&this.#t(t,e,i,s,n,a,r,o,-d/h,c));const u=h**2-4*d*l;if(u<0)return;const p=Math.sqrt(u),g=2*l;this.#t(t,e,i,s,n,a,r,o,(-h+p)/g,c),this.#t(t,e,i,s,n,a,r,o,(-h-p)/g,c)}static bezierBoundingBox(t,e,i,s,n,a,r,o,l){l[0]=Math.min(l[0],t,r),l[1]=Math.min(l[1],e,o),l[2]=Math.max(l[2],t,r),l[3]=Math.max(l[3],e,o),this.#e(t,i,n,r,e,s,a,o,3*(3*(i-n)-t+r),6*(t-2*i+n),3*(i-t),l),this.#e(t,i,n,r,e,s,a,o,3*(3*(s-a)-e+o),6*(e-2*s+a),3*(s-e),l)}}let rt=null,ot=null;function lt(t){return rt||(rt=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,ot=new Map([["ﬅ","ſt"]])),t.replaceAll(rt,((t,e,i)=>e?e.normalize("NFKC"):ot.get(i)))}function ht(){if("function"==typeof crypto.randomUUID)return crypto.randomUUID();const t=new Uint8Array(32);return crypto.getRandomValues(t),et(t)}const dt="pdfjs_internal_id_";function ct(t,e,i){return Math.min(Math.max(t,e),i)}function ut(t){return Uint8Array.prototype.toBase64?t.toBase64():btoa(et(t))}"function"!=typeof Promise.try&&(Promise.try=function(t,...e){return new Promise((i=>{i(t(...e))}))}),"function"!=typeof Math.sumPrecise&&(Math.sumPrecise=function(t){return t.reduce(((t,e)=>t+e),0)});const pt="http://www.w3.org/2000/svg";class gt{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}async function mt(t,e="text"){if(xt(t,document.baseURI)){const i=await fetch(t);if(!i.ok)throw new Error(i.statusText);switch(e){case"arraybuffer":return i.arrayBuffer();case"blob":return i.blob();case"json":return i.json()}return i.text()}return new Promise(((i,s)=>{const n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType=e,n.onreadystatechange=()=>{if(n.readyState===XMLHttpRequest.DONE)if(200!==n.status&&0!==n.status)s(new Error(n.statusText));else{switch(e){case"arraybuffer":case"blob":case"json":return void i(n.response)}i(n.responseText)}},n.send(null)}))}class ft{constructor({viewBox:t,userUnit:e,scale:i,rotation:s,offsetX:n=0,offsetY:a=0,dontFlip:r=!1}){this.viewBox=t,this.userUnit=e,this.scale=i,this.rotation=s,this.offsetX=n,this.offsetY=a,i*=e;const o=(t[2]+t[0])/2,l=(t[3]+t[1])/2;let h,d,c,u,p,g,m,f;switch((s%=360)<0&&(s+=360),s){case 180:h=-1,d=0,c=0,u=1;break;case 90:h=0,d=1,c=1,u=0;break;case 270:h=0,d=-1,c=-1,u=0;break;case 0:h=1,d=0,c=0,u=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}r&&(c=-c,u=-u),0===h?(p=Math.abs(l-t[1])*i+n,g=Math.abs(o-t[0])*i+a,m=(t[3]-t[1])*i,f=(t[2]-t[0])*i):(p=Math.abs(o-t[0])*i+n,g=Math.abs(l-t[1])*i+a,m=(t[2]-t[0])*i,f=(t[3]-t[1])*i),this.transform=[h*i,d*i,c*i,u*i,p-h*i*o-c*i*l,g-d*i*o-u*i*l],this.width=m,this.height=f}get rawDims(){const t=this.viewBox;return q(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:i=this.offsetX,offsetY:s=this.offsetY,dontFlip:n=!1}={}){return new ft({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}convertToViewportPoint(t,e){const i=[t,e];return at.applyTransform(i,this.transform),i}convertToViewportRectangle(t){const e=[t[0],t[1]];at.applyTransform(e,this.transform);const i=[t[2],t[3]];return at.applyTransform(i,this.transform),[e[0],e[1],i[0],i[1]]}convertToPdfPoint(t,e){const i=[t,e];return at.applyInverseTransform(i,this.transform),i}}class bt extends X{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function vt(t){const e=t.length;let i=0;for(;i<e&&""===t[i].trim();)i++;return"data:"===t.substring(i,i+5).toLowerCase()}function At(t){return"string"==typeof t&&/\.pdf$/i.test(t)}function wt(t){return[t]=t.split(/[#?]/,1),t.substring(t.lastIndexOf("/")+1)}function yt(t,e="document.pdf"){if("string"!=typeof t)return e;if(vt(t))return G(),e;const i=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,s=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t);let n=i.exec(s[1])||i.exec(s[2])||i.exec(s[3]);if(n&&(n=n[0],n.includes("%")))try{n=i.exec(decodeURIComponent(n))[0]}catch{}return n||e}class _t{started=Object.create(null);times=[];time(t){t in this.started&&G(),this.started[t]=Date.now()}timeEnd(t){t in this.started||G(),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const{name:i}of this.times)e=Math.max(i.length,e);for(const{name:i,start:s,end:n}of this.times)t.push(`${i.padEnd(e)} ${n-s}ms\n`);return t.join("")}}function xt(t,e){const i=e?URL.parse(t,e):URL.parse(t);return"http:"===i?.protocol||"https:"===i?.protocol}function St(t){t.preventDefault()}function Et(t){t.preventDefault(),t.stopPropagation()}class Ct{static#i;static toDateObject(t){if(!t||"string"!=typeof t)return null;this.#i||=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");const e=this.#i.exec(t);if(!e)return null;const i=parseInt(e[1],10);let s=parseInt(e[2],10);s=s>=1&&s<=12?s-1:0;let n=parseInt(e[3],10);n=n>=1&&n<=31?n:1;let a=parseInt(e[4],10);a=a>=0&&a<=23?a:0;let r=parseInt(e[5],10);r=r>=0&&r<=59?r:0;let o=parseInt(e[6],10);o=o>=0&&o<=59?o:0;const l=e[7]||"Z";let h=parseInt(e[8],10);h=h>=0&&h<=23?h:0;let d=parseInt(e[9],10)||0;return d=d>=0&&d<=59?d:0,"-"===l?(a+=h,r+=d):"+"===l&&(a-=h,r-=d),new Date(Date.UTC(i,s,n,a,r,o))}}function Tt(t,{scale:e=1,rotation:i=0}){const{width:s,height:n}=t.attributes.style,a=[0,0,parseInt(s),parseInt(n)];return new ft({viewBox:a,userUnit:1,scale:e,rotation:i})}function Mt(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}return t.startsWith("rgb(")?t.slice(4,-1).split(",").map((t=>parseInt(t))):t.startsWith("rgba(")?t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3):(G(),[0,0,0])}function Pt(t){const{a:e,b:i,c:s,d:n,e:a,f:r}=t.getTransform();return[e,i,s,n,a,r]}function It(t){const{a:e,b:i,c:s,d:n,e:a,f:r}=t.getTransform().invertSelf();return[e,i,s,n,a,r]}function kt(t,e,i=!1,s=!0){if(e instanceof ft){const{pageWidth:s,pageHeight:n}=e.rawDims,{style:a}=t,r=st.isCSSRoundSupported,o=`var(--total-scale-factor) * ${s}px`,l=`var(--total-scale-factor) * ${n}px`,h=r?`round(down, ${o}, var(--scale-round-x))`:`calc(${o})`,d=r?`round(down, ${l}, var(--scale-round-y))`:`calc(${l})`;i&&e.rotation%180!=0?(a.width=d,a.height=h):(a.width=h,a.height=d)}s&&t.setAttribute("data-main-rotation",e.rotation)}class Dt{constructor(){const{pixelRatio:t}=Dt;this.sx=t,this.sy=t}get scaled(){return 1!==this.sx||1!==this.sy}get symmetric(){return this.sx===this.sy}limitCanvas(t,e,i,s,n=-1){let a=1/0,r=1/0,o=1/0;(i=Dt.capPixels(i,n))>0&&(a=Math.sqrt(i/(t*e))),-1!==s&&(r=s/t,o=s/e);const l=Math.min(a,r,o);return(this.sx>l||this.sy>l)&&(this.sx=l,this.sy=l,!0)}static get pixelRatio(){return globalThis.devicePixelRatio||1}static capPixels(t,e){if(e>=0){const i=Math.ceil(window.screen.availWidth*window.screen.availHeight*this.pixelRatio**2*(1+e/100));return t>0?Math.min(t,i):i}return t}}const Rt=["image/apng","image/avif","image/bmp","image/gif","image/jpeg","image/png","image/svg+xml","image/webp","image/x-icon"];class Lt{#s=null;#n=null;#a;#r=null;#o=null;#l=null;static#h=null;constructor(t){this.#a=t,Lt.#h||=Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button",signature:"pdfjs-editor-remove-signature-button"})}render(){const t=this.#s=document.createElement("div");t.classList.add("editToolbar","hidden"),t.setAttribute("role","toolbar");const e=this.#a._uiManager._signal;t.addEventListener("contextmenu",St,{signal:e}),t.addEventListener("pointerdown",Lt.#d,{signal:e});const i=this.#r=document.createElement("div");i.className="buttons",t.append(i);const s=this.#a.toolbarPosition;if(s){const{style:e}=t,i="ltr"===this.#a._uiManager.direction?1-s[0]:s[0];e.insetInlineEnd=100*i+"%",e.top=`calc(${100*s[1]}% + var(--editor-toolbar-vert-offset))`}return this.#c(),t}get div(){return this.#s}static#d(t){t.stopPropagation()}#u(t){this.#a._focusEventsAllowed=!1,Et(t)}#p(t){this.#a._focusEventsAllowed=!0,Et(t)}#g(t){const e=this.#a._uiManager._signal;t.addEventListener("focusin",this.#u.bind(this),{capture:!0,signal:e}),t.addEventListener("focusout",this.#p.bind(this),{capture:!0,signal:e}),t.addEventListener("contextmenu",St,{signal:e})}hide(){this.#s.classList.add("hidden"),this.#n?.hideDropdown()}show(){this.#s.classList.remove("hidden"),this.#o?.shown()}#c(){const{editorType:t,_uiManager:e}=this.#a,i=document.createElement("button");i.className="delete",i.tabIndex=0,i.setAttribute("data-l10n-id",Lt.#h[t]),this.#g(i),i.addEventListener("click",(t=>{e.delete()}),{signal:e._signal}),this.#r.append(i)}get#m(){const t=document.createElement("div");return t.className="divider",t}async addAltText(t){const e=await t.render();this.#g(e),this.#r.prepend(e,this.#m),this.#o=t}addColorPicker(t){this.#n=t;const e=t.renderButton();this.#g(e),this.#r.prepend(e,this.#m)}async addEditSignatureButton(t){const e=this.#l=await t.renderEditButton(this.#a);this.#g(e),this.#r.prepend(e,this.#m)}updateEditSignatureButton(t){this.#l&&(this.#l.title=t)}remove(){this.#s.remove(),this.#n?.destroy(),this.#n=null}}class Ft{#r=null;#s=null;#f;constructor(t){this.#f=t}#b(){const t=this.#s=document.createElement("div");t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",St,{signal:this.#f._signal});const e=this.#r=document.createElement("div");return e.className="buttons",t.append(e),this.#v(),t}#A(t,e){let i=0,s=0;for(const n of t){const t=n.y+n.height;if(t<i)continue;const a=n.x+(e?n.width:0);t>i?(s=a,i=t):e?a>s&&(s=a):a<s&&(s=a)}return[e?1-s:s,i]}show(t,e,i){const[s,n]=this.#A(e,i),{style:a}=this.#s||=this.#b();t.append(this.#s),a.insetInlineEnd=100*s+"%",a.top=`calc(${100*n}% + var(--editor-toolbar-vert-offset))`}hide(){this.#s.remove()}#v(){const t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const i=this.#f._signal;t.addEventListener("contextmenu",St,{signal:i}),t.addEventListener("click",(()=>{this.#f.highlightSelection("floating_button")}),{signal:i}),this.#r.append(t)}}function Nt(t,e,i){for(const s of i)e.addEventListener(s,t[s].bind(t))}class Ot{#w=0;get id(){return"pdfjs_internal_editor_"+this.#w++}}class Bt{#y=ht();#w=0;#_=null;static get _isSVGFittingCanvas(){const t=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),e=new Image;e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>';return q(this,"_isSVGFittingCanvas",e.decode().then((()=>(t.drawImage(e,0,0,1,1,0,0,1,3),0===new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0]))))}async#x(t,e){this.#_||=new Map;let i=this.#_.get(t);if(null===i)return null;if(i?.bitmap)return i.refCounter+=1,i;try{let t;if(i||={bitmap:null,id:`image_${this.#y}_${this.#w++}`,refCounter:0,isSvg:!1},"string"==typeof e?(i.url=e,t=await mt(e,"blob")):e instanceof File?t=i.file=e:e instanceof Blob&&(t=e),"image/svg+xml"===t.type){const e=Bt._isSVGFittingCanvas,s=new FileReader,n=new Image,a=new Promise(((t,a)=>{n.onload=()=>{i.bitmap=n,i.isSvg=!0,t()},s.onload=async()=>{const t=i.svgUrl=s.result;n.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t},n.onerror=s.onerror=a}));s.readAsDataURL(t),await a}else i.bitmap=await createImageBitmap(t);i.refCounter=1}catch(s){G(),i=null}return this.#_.set(t,i),i&&this.#_.set(i.id,i),i}async getFromFile(t){const{lastModified:e,name:i,size:s,type:n}=t;return this.#x(`${e}_${i}_${s}_${n}`,t)}async getFromUrl(t){return this.#x(t,t)}async getFromBlob(t,e){const i=await e;return this.#x(t,i)}async getFromId(t){this.#_||=new Map;const e=this.#_.get(t);if(!e)return null;if(e.bitmap)return e.refCounter+=1,e;if(e.file)return this.getFromFile(e.file);if(e.blobPromise){const{blobPromise:t}=e;return delete e.blobPromise,this.getFromBlob(e.id,t)}return this.getFromUrl(e.url)}getFromCanvas(t,e){this.#_||=new Map;let i=this.#_.get(t);if(i?.bitmap)return i.refCounter+=1,i;const s=new OffscreenCanvas(e.width,e.height);return s.getContext("2d").drawImage(e,0,0),i={bitmap:s.transferToImageBitmap(),id:`image_${this.#y}_${this.#w++}`,refCounter:1,isSvg:!1},this.#_.set(t,i),this.#_.set(i.id,i),i}getSvgUrl(t){const e=this.#_.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#_||=new Map;const e=this.#_.get(t);if(!e)return;if(e.refCounter-=1,0!==e.refCounter)return;const{bitmap:i}=e;if(!e.url&&!e.file){const t=new OffscreenCanvas(i.width,i.height);t.getContext("bitmaprenderer").transferFromImageBitmap(i),e.blobPromise=t.convertToBlob()}i.close?.(),e.bitmap=null}isValidId(t){return t.startsWith(`image_${this.#y}_`)}}class zt{#S=[];#E=!1;#C;#T=-1;constructor(t=128){this.#C=t}add({cmd:t,undo:e,post:i,mustExec:s,type:n=NaN,overwriteIfSameType:a=!1,keepUndo:r=!1}){if(s&&t(),this.#E)return;const o={cmd:t,undo:e,post:i,type:n};if(-1===this.#T)return this.#S.length>0&&(this.#S.length=0),this.#T=0,void this.#S.push(o);if(a&&this.#S[this.#T].type===n)return r&&(o.undo=this.#S[this.#T].undo),void(this.#S[this.#T]=o);const l=this.#T+1;l===this.#C?this.#S.splice(0,1):(this.#T=l,l<this.#S.length&&this.#S.splice(l)),this.#S.push(o)}undo(){if(-1===this.#T)return;this.#E=!0;const{undo:t,post:e}=this.#S[this.#T];t(),e?.(),this.#E=!1,this.#T-=1}redo(){if(this.#T<this.#S.length-1){this.#T+=1,this.#E=!0;const{cmd:t,post:e}=this.#S[this.#T];t(),e?.(),this.#E=!1}}hasSomethingToUndo(){return-1!==this.#T}hasSomethingToRedo(){return this.#T<this.#S.length-1}cleanType(t){if(-1!==this.#T){for(let e=this.#T;e>=0;e--)if(this.#S[e].type!==t)return this.#S.splice(e+1,this.#T-e),void(this.#T=e);this.#S.length=0,this.#T=-1}}destroy(){this.#S=null}}class Ht{constructor(t){this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:e}=st.platform;for(const[i,s,n={}]of t)for(const t of i){const i=t.startsWith("mac+");e&&i?(this.callbacks.set(t.slice(4),{callback:s,options:n}),this.allKeys.add(t.split("+").at(-1))):e||i||(this.callbacks.set(t,{callback:s,options:n}),this.allKeys.add(t.split("+").at(-1)))}}#M(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e}exec(t,e){if(!this.allKeys.has(e.key))return;const i=this.callbacks.get(this.#M(e));if(!i)return;const{callback:s,options:{bubbles:n=!1,args:a=[],checker:r=null}}=i;r&&!r(t,e)||(s.bind(t,...a,e)(),n||Et(e))}}class Ut{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return function(t){const e=document.createElement("span");e.style.visibility="hidden",e.style.colorScheme="only light",document.body.append(e);for(const i of t.keys()){e.style.color=i;const s=window.getComputedStyle(e).color;t.set(i,Mt(s))}e.remove()}(t),q(this,"_colors",t)}convert(t){const e=Mt(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[i,s]of this._colors)if(s.every(((t,i)=>t===e[i])))return Ut._colorsMapping.get(i);return e}getHexCode(t){const e=this._colors.get(t);return e?at.makeHexColor(...e):t}}class Gt{#P=new AbortController;#I=null;#k=new Map;#D=new Map;#R=null;#L=null;#F=null;#N=new zt;#O=null;#B=null;#z=0;#H=new Set;#U=null;#G=null;#$=new Set;_editorUndoBar=null;#j=!1;#V=!1;#W=!1;#q=null;#X=null;#K=null;#Y=null;#Q=!1;#J=null;#Z=new Ot;#tt=!1;#et=!1;#it=null;#st=null;#nt=null;#at=null;#rt=null;#ot=m.NONE;#lt=new Set;#ht=null;#dt=null;#ct=null;#ut=null;#pt={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1};#gt=[0,0];#mt=null;#ft=null;#bt=null;#vt=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){const t=Gt.prototype,e=t=>t.#ft.contains(document.activeElement)&&"BUTTON"!==document.activeElement.tagName&&t.hasSomethingToControl(),i=(t,{target:e})=>{if(e instanceof HTMLInputElement){const{type:t}=e;return"text"!==t&&"number"!==t}return!0},s=this.TRANSLATE_SMALL,n=this.TRANSLATE_BIG;return q(this,"_keyboardManager",new Ht([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:i}],[["ctrl+z","mac+meta+z"],t.undo,{checker:i}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:i}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:i}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#ft.contains(e)&&!t.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#ft.contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-s,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-n,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[s,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[n,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-s],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-n],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,s],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,n],checker:e}]]))}constructor(t,e,i,s,n,a,r,o,l,h,d,c,u,p){const g=this._signal=this.#P.signal;this.#ft=t,this.#bt=e,this.#R=i,this.#dt=s,this._eventBus=n,n._on("editingaction",this.onEditingAction.bind(this),{signal:g}),n._on("pagechanging",this.onPageChanging.bind(this),{signal:g}),n._on("scalechanging",this.onScaleChanging.bind(this),{signal:g}),n._on("rotationchanging",this.onRotationChanging.bind(this),{signal:g}),n._on("setpreference",this.onSetPreference.bind(this),{signal:g}),n._on("switchannotationeditorparams",(t=>this.updateParams(t.type,t.value)),{signal:g}),this.#At(),this.#wt(),this.#yt(),this.#L=a.annotationStorage,this.#q=a.filterFactory,this.#ct=r,this.#Y=o||null,this.#j=l,this.#V=h,this.#W=d,this.#rt=c||null,this.viewParameters={realScale:gt.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1,this._editorUndoBar=u||null,this._supportsPinchToZoom=!1!==p}destroy(){this.#vt?.resolve(),this.#vt=null,this.#P?.abort(),this.#P=null,this._signal=null;for(const t of this.#D.values())t.destroy();this.#D.clear(),this.#k.clear(),this.#$.clear(),this.#at?.clear(),this.#I=null,this.#lt.clear(),this.#N.destroy(),this.#R?.destroy(),this.#dt?.destroy(),this.#J?.hide(),this.#J=null,this.#nt?.destroy(),this.#nt=null,this.#X&&(clearTimeout(this.#X),this.#X=null),this.#mt&&(clearTimeout(this.#mt),this.#mt=null),this._editorUndoBar?.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return this.#rt}get useNewAltTextFlow(){return this.#V}get useNewAltTextWhenAddingImage(){return this.#W}get hcmFilter(){return q(this,"hcmFilter",this.#ct?this.#q.addHCMFilter(this.#ct.foreground,this.#ct.background):"none")}get direction(){return q(this,"direction",getComputedStyle(this.#ft).direction)}get highlightColors(){return q(this,"highlightColors",this.#Y?new Map(this.#Y.split(",").map((t=>t.split("=").map((t=>t.trim()))))):null)}get highlightColorNames(){return q(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,(t=>t.reverse()))):null)}setCurrentDrawingSession(t){t?(this.unselectAll(),this.disableUserSelect(!0)):this.disableUserSelect(!1),this.#B=t}setMainHighlightColorPicker(t){this.#nt=t}editAltText(t,e=!1){this.#R?.editAltText(this,t,e)}getSignature(t){this.#dt?.getSignature({uiManager:this,editor:t})}get signatureManager(){return this.#dt}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal}),this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){if("enableNewAltTextWhenAddingImage"===t)this.#W=e}onPageChanging({pageNumber:t}){this.#z=t-1}focusMainContainer(){this.#ft.focus()}findParent(t,e){for(const i of this.#D.values()){const{x:s,y:n,width:a,height:r}=i.div.getBoundingClientRect();if(t>=s&&t<=s+a&&e>=n&&e<=n+r)return i}return null}disableUserSelect(t=!1){this.#bt.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#$.add(t)}removeShouldRescale(t){this.#$.delete(t)}onScaleChanging({scale:t}){this.commitOrRemove(),this.viewParameters.realScale=t*gt.PDF_TO_CSS_UNITS;for(const e of this.#$)e.onScaleChanging();this.#B?.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}#_t({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t}#xt(t){const{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(const i of this.#D.values())if(i.hasTextLayer(t))return i;return null}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:i,anchorOffset:s,focusNode:n,focusOffset:a}=e,r=e.toString(),o=this.#_t(e).closest(".textLayer"),l=this.getSelectionBoxes(o);if(!l)return;e.empty();const h=this.#xt(o),d=this.#ot===m.NONE,c=()=>{h?.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:l,anchorNode:i,anchorOffset:s,focusNode:n,focusOffset:a,text:r}),d&&this.showAllEditors("highlight",!0,!0)};d?this.switchToMode(m.HIGHLIGHT,c):c()}#St(){const t=document.getSelection();if(!t||t.isCollapsed)return;const e=this.#_t(t).closest(".textLayer"),i=this.getSelectionBoxes(e);i&&(this.#J||=new Ft(this),this.#J.show(e,i,"ltr"===this.direction))}addToAnnotationStorage(t){t.isEmpty()||!this.#L||this.#L.has(t.id)||this.#L.setValue(t.id,t)}#Et(){const t=document.getSelection();if(!t||t.isCollapsed)return void(this.#ht&&(this.#J?.hide(),this.#ht=null,this.#Ct({hasSelectedText:!1})));const{anchorNode:e}=t;if(e===this.#ht)return;const i=this.#_t(t).closest(".textLayer");if(i){if(this.#J?.hide(),this.#ht=e,this.#Ct({hasSelectedText:!0}),(this.#ot===m.HIGHLIGHT||this.#ot===m.NONE)&&(this.#ot===m.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),this.#Q=this.isShiftKeyDown,!this.isShiftKeyDown)){const t=this.#ot===m.HIGHLIGHT?this.#xt(i):null;t?.toggleDrawing();const e=new AbortController,s=this.combinedSignal(e),n=i=>{"pointerup"===i.type&&0!==i.button||(e.abort(),t?.toggleDrawing(!0),"pointerup"===i.type&&this.#Tt("main_toolbar"))};window.addEventListener("pointerup",n,{signal:s}),window.addEventListener("blur",n,{signal:s})}}else this.#ht&&(this.#J?.hide(),this.#ht=null,this.#Ct({hasSelectedText:!1}))}#Tt(t=""){this.#ot===m.HIGHLIGHT?this.highlightSelection(t):this.#j&&this.#St()}#At(){document.addEventListener("selectionchange",this.#Et.bind(this),{signal:this._signal})}#Mt(){if(this.#K)return;this.#K=new AbortController;const t=this.combinedSignal(this.#K);window.addEventListener("focus",this.focus.bind(this),{signal:t}),window.addEventListener("blur",this.blur.bind(this),{signal:t})}#Pt(){this.#K?.abort(),this.#K=null}blur(){if(this.isShiftKeyDown=!1,this.#Q&&(this.#Q=!1,this.#Tt("main_toolbar")),!this.hasSelection)return;const{activeElement:t}=document;for(const e of this.#lt)if(e.div.contains(t)){this.#st=[e,t],e._focusEventsAllowed=!1;break}}focus(){if(!this.#st)return;const[t,e]=this.#st;this.#st=null,e.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this._signal}),e.focus()}#yt(){if(this.#it)return;this.#it=new AbortController;const t=this.combinedSignal(this.#it);window.addEventListener("keydown",this.keydown.bind(this),{signal:t}),window.addEventListener("keyup",this.keyup.bind(this),{signal:t})}#It(){this.#it?.abort(),this.#it=null}#kt(){if(this.#O)return;this.#O=new AbortController;const t=this.combinedSignal(this.#O);document.addEventListener("copy",this.copy.bind(this),{signal:t}),document.addEventListener("cut",this.cut.bind(this),{signal:t}),document.addEventListener("paste",this.paste.bind(this),{signal:t})}#Dt(){this.#O?.abort(),this.#O=null}#wt(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t}),document.addEventListener("drop",this.drop.bind(this),{signal:t})}addEditListeners(){this.#yt(),this.#kt()}removeEditListeners(){this.#It(),this.#Dt()}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const i of this.#G)if(i.isHandlingMimeForPasting(e))return t.dataTransfer.dropEffect="copy",void t.preventDefault()}drop(t){for(const e of t.dataTransfer.items)for(const i of this.#G)if(i.isHandlingMimeForPasting(e.type))return i.paste(e,this.currentLayer),void t.preventDefault()}copy(t){if(t.preventDefault(),this.#I?.commitOrRemove(),!this.hasSelection)return;const e=[];for(const i of this.#lt){const t=i.serialize(!0);t&&e.push(t)}0!==e.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}async paste(t){t.preventDefault();const{clipboardData:e}=t;for(const a of e.items)for(const t of this.#G)if(t.isHandlingMimeForPasting(a.type))return void t.paste(a,this.currentLayer);let i=e.getData("application/pdfjs");if(!i)return;try{i=JSON.parse(i)}catch(n){return void G(n.message)}if(!Array.isArray(i))return;this.unselectAll();const s=this.currentLayer;try{const t=[];for(const a of i){const e=await s.deserialize(a);if(!e)return;t.push(e)}const e=()=>{for(const e of t)this.#Rt(e);this.#Lt(t)},n=()=>{for(const e of t)e.remove()};this.addCommands({cmd:e,undo:n,mustExec:!0})}catch(n){G(n.message)}}keydown(t){this.isShiftKeyDown||"Shift"!==t.key||(this.isShiftKeyDown=!0),this.#ot===m.NONE||this.isEditorHandlingKeyboard||Gt._keyboardManager.exec(this,t)}keyup(t){this.isShiftKeyDown&&"Shift"===t.key&&(this.isShiftKeyDown=!1,this.#Q&&(this.#Q=!1,this.#Tt("main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu")}}#Ct(t){Object.entries(t).some((([t,e])=>this.#pt[t]!==e))&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#pt,t)}),this.#ot===m.HIGHLIGHT&&!1===t.hasSelectedEditor&&this.#Ft([[f.HIGHLIGHT_FREE,!0]]))}#Ft(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){t?(this.#Mt(),this.#kt(),this.#Ct({isEditing:this.#ot!==m.NONE,isEmpty:this.#Nt(),hasSomethingToUndo:this.#N.hasSomethingToUndo(),hasSomethingToRedo:this.#N.hasSomethingToRedo(),hasSelectedEditor:!1})):(this.#Pt(),this.#Dt(),this.#Ct({isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!this.#G){this.#G=t;for(const t of this.#G)this.#Ft(t.defaultPropertiesToUpdate)}}getId(){return this.#Z.id}get currentLayer(){return this.#D.get(this.#z)}getLayer(t){return this.#D.get(t)}get currentPageIndex(){return this.#z}addLayer(t){this.#D.set(t.pageIndex,t),this.#tt?t.enable():t.disable()}removeLayer(t){this.#D.delete(t.pageIndex)}async updateMode(t,e=null,i=!1){if(this.#ot!==t&&(!this.#vt||(await this.#vt.promise,this.#vt))){if(this.#vt=Promise.withResolvers(),this.#B?.commitOrRemove(),this.#ot=t,t===m.NONE)return this.setEditingState(!1),this.#Ot(),this._editorUndoBar?.hide(),void this.#vt.resolve();t===m.SIGNATURE&&await(this.#dt?.loadSignatures()),this.setEditingState(!0),await this.#Bt(),this.unselectAll();for(const e of this.#D.values())e.updateMode(t);if(!e)return i&&this.addNewEditorFromKeyboard(),void this.#vt.resolve();for(const t of this.#k.values())t.annotationElementId===e||t.id===e?(this.setSelected(t),t.enterInEditMode()):t.unselect();this.#vt.resolve()}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t.mode!==this.#ot&&this._eventBus.dispatch("switchannotationeditormode",{source:this,...t})}updateParams(t,e){if(this.#G){switch(t){case f.CREATE:return void this.currentLayer.addNewEditor(e);case f.HIGHLIGHT_DEFAULT_COLOR:this.#nt?.updateColor(e);break;case f.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(this.#ut||=new Map).set(t,e),this.showAllEditors("highlight",e)}for(const i of this.#lt)i.updateParams(t,e);for(const i of this.#G)i.updateDefaultParams(t,e)}}showAllEditors(t,e,i=!1){for(const s of this.#k.values())s.editorType===t&&s.show(e);(this.#ut?.get(f.HIGHLIGHT_SHOW_ALL)??!0)!==e&&this.#Ft([[f.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(this.#et!==t){this.#et=t;for(const e of this.#D.values())t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}}async#Bt(){if(!this.#tt){this.#tt=!0;const t=[];for(const e of this.#D.values())t.push(e.enable());await Promise.all(t);for(const e of this.#k.values())e.enable()}}#Ot(){if(this.unselectAll(),this.#tt){this.#tt=!1;for(const t of this.#D.values())t.disable();for(const t of this.#k.values())t.disable()}}getEditors(t){const e=[];for(const i of this.#k.values())i.pageIndex===t&&e.push(i);return e}getEditor(t){return this.#k.get(t)}addEditor(t){this.#k.set(t.id,t)}removeEditor(t){t.div.contains(document.activeElement)&&(this.#X&&clearTimeout(this.#X),this.#X=setTimeout((()=>{this.focusMainContainer(),this.#X=null}),0)),this.#k.delete(t.id),t.annotationElementId&&this.#at?.delete(t.annotationElementId),this.unselect(t),t.annotationElementId&&this.#H.has(t.annotationElementId)||this.#L?.remove(t.id)}addDeletedAnnotationElement(t){this.#H.add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return this.#H.has(t)}removeDeletedAnnotationElement(t){this.#H.delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}#Rt(t){const e=this.#D.get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))}setActiveEditor(t){this.#I!==t&&(this.#I=t,t&&this.#Ft(t.propertiesToUpdate))}get#zt(){let t=null;for(t of this.#lt);return t}updateUI(t){this.#zt===t&&this.#Ft(t.propertiesToUpdate)}updateUIForDefaultProperties(t){this.#Ft(t.defaultPropertiesToUpdate)}toggleSelected(t){if(this.#lt.has(t))return this.#lt.delete(t),t.unselect(),void this.#Ct({hasSelectedEditor:this.hasSelection});this.#lt.add(t),t.select(),this.#Ft(t.propertiesToUpdate),this.#Ct({hasSelectedEditor:!0})}setSelected(t){this.#B?.commitOrRemove();for(const e of this.#lt)e!==t&&e.unselect();this.#lt.clear(),this.#lt.add(t),t.select(),this.#Ft(t.propertiesToUpdate),this.#Ct({hasSelectedEditor:!0})}isSelected(t){return this.#lt.has(t)}get firstSelectedEditor(){return this.#lt.values().next().value}unselect(t){t.unselect(),this.#lt.delete(t),this.#Ct({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#lt.size}get isEnterHandled(){return 1===this.#lt.size&&this.firstSelectedEditor.isEnterHandled}undo(){this.#N.undo(),this.#Ct({hasSomethingToUndo:this.#N.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#Nt()}),this._editorUndoBar?.hide()}redo(){this.#N.redo(),this.#Ct({hasSomethingToUndo:!0,hasSomethingToRedo:this.#N.hasSomethingToRedo(),isEmpty:this.#Nt()})}addCommands(t){this.#N.add(t),this.#Ct({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#Nt()})}cleanUndoStack(t){this.#N.cleanType(t)}#Nt(){if(0===this.#k.size)return!0;if(1===this.#k.size)for(const t of this.#k.values())return t.isEmpty();return!1}delete(){this.commitOrRemove();const t=this.currentLayer?.endDrawingSession(!0);if(!this.hasSelection&&!t)return;const e=t?[t]:[...this.#lt],i=()=>{for(const t of e)this.#Rt(t)};this.addCommands({cmd:()=>{this._editorUndoBar?.show(i,1===e.length?e[0].editorType:e.length);for(const t of e)t.remove()},undo:i,mustExec:!0})}commitOrRemove(){this.#I?.commitOrRemove()}hasSomethingToControl(){return this.#I||this.hasSelection}#Lt(t){for(const e of this.#lt)e.unselect();this.#lt.clear();for(const e of t)e.isEmpty()||(this.#lt.add(e),e.select());this.#Ct({hasSelectedEditor:this.hasSelection})}selectAll(){for(const t of this.#lt)t.commit();this.#Lt(this.#k.values())}unselectAll(){if((!this.#I||(this.#I.commitOrRemove(),this.#ot===m.NONE))&&!this.#B?.commitOrRemove()&&this.hasSelection){for(const t of this.#lt)t.unselect();this.#lt.clear(),this.#Ct({hasSelectedEditor:!1})}}translateSelectedEditors(t,e,i=!1){if(i||this.commitOrRemove(),!this.hasSelection)return;this.#gt[0]+=t,this.#gt[1]+=e;const[s,n]=this.#gt,a=[...this.#lt];this.#mt&&clearTimeout(this.#mt),this.#mt=setTimeout((()=>{this.#mt=null,this.#gt[0]=this.#gt[1]=0,this.addCommands({cmd:()=>{for(const t of a)this.#k.has(t.id)&&(t.translateInPage(s,n),t.translationDone())},undo:()=>{for(const t of a)this.#k.has(t.id)&&(t.translateInPage(-s,-n),t.translationDone())},mustExec:!1})}),1e3);for(const r of a)r.translateInPage(t,e),r.translationDone()}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),this.#U=new Map;for(const t of this.#lt)this.#U.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!this.#U)return!1;this.disableUserSelect(!1);const t=this.#U;this.#U=null;let e=!1;for(const[{x:s,y:n,pageIndex:a},r]of t)r.newX=s,r.newY=n,r.newPageIndex=a,e||=s!==r.savedX||n!==r.savedY||a!==r.savedPageIndex;if(!e)return!1;const i=(t,e,i,s)=>{if(this.#k.has(t.id)){const n=this.#D.get(s);n?t._setParentAndPosition(n,e,i):(t.pageIndex=s,t.x=e,t.y=i)}};return this.addCommands({cmd:()=>{for(const[e,{newX:s,newY:n,newPageIndex:a}]of t)i(e,s,n,a)},undo:()=>{for(const[e,{savedX:s,savedY:n,savedPageIndex:a}]of t)i(e,s,n,a)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(this.#U)for(const i of this.#U.keys())i.drag(t,e)}rebuild(t){if(null===t.parent){const e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||1===this.#lt.size&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return this.#I===t}getActive(){return this.#I}getMode(){return this.#ot}get imageManager(){return q(this,"imageManager",new Bt)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let l=0,h=e.rangeCount;l<h;l++)if(!t.contains(e.getRangeAt(l).commonAncestorContainer))return null;const{x:i,y:s,width:n,height:a}=t.getBoundingClientRect();let r;switch(t.getAttribute("data-main-rotation")){case"90":r=(t,e,r,o)=>({x:(e-s)/a,y:1-(t+r-i)/n,width:o/a,height:r/n});break;case"180":r=(t,e,r,o)=>({x:1-(t+r-i)/n,y:1-(e+o-s)/a,width:r/n,height:o/a});break;case"270":r=(t,e,r,o)=>({x:1-(e+o-s)/a,y:(t-i)/n,width:o/a,height:r/n});break;default:r=(t,e,r,o)=>({x:(t-i)/n,y:(e-s)/a,width:r/n,height:o/a})}const o=[];for(let l=0,h=e.rangeCount;l<h;l++){const t=e.getRangeAt(l);if(!t.collapsed)for(const{x:e,y:i,width:s,height:n}of t.getClientRects())0!==s&&0!==n&&o.push(r(e,i,s,n))}return 0===o.length?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(this.#F||=new Map).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){this.#F?.delete(t)}renderAnnotationElement(t){const e=this.#F?.get(t.data.id);if(!e)return;const i=this.#L.getRawValue(e);i&&(this.#ot!==m.NONE||i.hasBeenModified)&&i.renderAnnotationElement(t)}setMissingCanvas(t,e,i){const s=this.#at?.get(t);s&&(s.setCanvas(e,i),this.#at.delete(t))}addMissingCanvas(t,e){(this.#at||=new Map).set(t,e)}}class $t{#o=null;#Ht=!1;#Ut=null;#Gt=null;#$t=null;#jt=null;#Vt=!1;#Wt=null;#a=null;#qt=null;#Xt=null;#Kt=!1;static#Yt=null;static _l10n=null;constructor(t){this.#a=t,this.#Kt=t._uiManager.useNewAltTextFlow,$t.#Yt||=Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"})}static initialize(t){$t._l10n??=t}async render(){const t=this.#Ut=document.createElement("button");t.className="altText",t.tabIndex="0";const e=this.#Gt=document.createElement("span");t.append(e),this.#Kt?(t.classList.add("new"),t.setAttribute("data-l10n-id",$t.#Yt.missing),e.setAttribute("data-l10n-id",$t.#Yt["missing-label"])):(t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button"),e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label"));const i=this.#a._uiManager._signal;t.addEventListener("contextmenu",St,{signal:i}),t.addEventListener("pointerdown",(t=>t.stopPropagation()),{signal:i});const s=t=>{t.preventDefault(),this.#a._uiManager.editAltText(this.#a),this.#Kt&&this.#a._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:this.#Qt}})};return t.addEventListener("click",s,{capture:!0,signal:i}),t.addEventListener("keydown",(e=>{e.target===t&&"Enter"===e.key&&(this.#Vt=!0,s(e))}),{signal:i}),await this.#Jt(),t}get#Qt(){return(this.#o?"added":null===this.#o&&this.guessedText&&"review")||"missing"}finish(){this.#Ut&&(this.#Ut.focus({focusVisible:this.#Vt}),this.#Vt=!1)}isEmpty(){return this.#Kt?null===this.#o:!this.#o&&!this.#Ht}hasData(){return this.#Kt?null!==this.#o||!!this.#qt:this.isEmpty()}get guessedText(){return this.#qt}async setGuessedText(t){null===this.#o&&(this.#qt=t,this.#Xt=await $t._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:t}),this.#Jt())}toggleAltTextBadge(t=!1){if(!this.#Kt||this.#o)return this.#Wt?.remove(),void(this.#Wt=null);if(!this.#Wt){const t=this.#Wt=document.createElement("div");t.className="noAltTextBadge",this.#a.div.append(t)}this.#Wt.classList.toggle("hidden",!t)}serialize(t){let e=this.#o;return t||this.#qt!==e||(e=this.#Xt),{altText:e,decorative:this.#Ht,guessedText:this.#qt,textWithDisclaimer:this.#Xt}}get data(){return{altText:this.#o,decorative:this.#Ht}}set data({altText:t,decorative:e,guessedText:i,textWithDisclaimer:s,cancel:n=!1}){i&&(this.#qt=i,this.#Xt=s),this.#o===t&&this.#Ht===e||(n||(this.#o=t,this.#Ht=e),this.#Jt())}toggle(t=!1){this.#Ut&&(!t&&this.#jt&&(clearTimeout(this.#jt),this.#jt=null),this.#Ut.disabled=!t)}shown(){this.#a._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:this.#Qt}})}destroy(){this.#Ut?.remove(),this.#Ut=null,this.#Gt=null,this.#$t=null,this.#Wt?.remove(),this.#Wt=null}async#Jt(){const t=this.#Ut;if(!t)return;if(this.#Kt){if(t.classList.toggle("done",!!this.#o),t.setAttribute("data-l10n-id",$t.#Yt[this.#Qt]),this.#Gt?.setAttribute("data-l10n-id",$t.#Yt[`${this.#Qt}-label`]),!this.#o)return void this.#$t?.remove()}else{if(!this.#o&&!this.#Ht)return t.classList.remove("done"),void this.#$t?.remove();t.classList.add("done"),t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let e=this.#$t;if(!e){this.#$t=e=document.createElement("span"),e.className="tooltip",e.setAttribute("role","tooltip"),e.id=`alt-text-tooltip-${this.#a.id}`;const i=100,s=this.#a._uiManager._signal;s.addEventListener("abort",(()=>{clearTimeout(this.#jt),this.#jt=null}),{once:!0}),t.addEventListener("mouseenter",(()=>{this.#jt=setTimeout((()=>{this.#jt=null,this.#$t.classList.add("show"),this.#a._reportTelemetry({action:"alt_text_tooltip"})}),i)}),{signal:s}),t.addEventListener("mouseleave",(()=>{this.#jt&&(clearTimeout(this.#jt),this.#jt=null),this.#$t?.classList.remove("show")}),{signal:s})}this.#Ht?e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip"):(e.removeAttribute("data-l10n-id"),e.textContent=this.#o),e.parentNode||t.append(e);const i=this.#a.getElementForAltText();i?.setAttribute("aria-describedby",e.id)}}class jt{#ft;#Zt=!1;#te=null;#ee;#ie;#se;#ne;#ae=null;#re;#oe=null;#le;#he=null;constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:i=null,onPinchStart:s=null,onPinching:n=null,onPinchEnd:a=null,signal:r}){this.#ft=t,this.#te=i,this.#ee=e,this.#ie=s,this.#se=n,this.#ne=a,this.#le=new AbortController,this.#re=AbortSignal.any([r,this.#le.signal]),t.addEventListener("touchstart",this.#de.bind(this),{passive:!1,signal:this.#re})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return 35/Dt.pixelRatio}#de(t){if(this.#ee?.())return;if(1===t.touches.length){if(this.#ae)return;const t=this.#ae=new AbortController,e=AbortSignal.any([this.#re,t.signal]),i=this.#ft,s={capture:!0,signal:e,passive:!1},n=t=>{"touch"===t.pointerType&&(this.#ae?.abort(),this.#ae=null)};return i.addEventListener("pointerdown",(t=>{"touch"===t.pointerType&&(Et(t),n(t))}),s),i.addEventListener("pointerup",n,s),void i.addEventListener("pointercancel",n,s)}if(!this.#he){this.#he=new AbortController;const t=AbortSignal.any([this.#re,this.#he.signal]),e=this.#ft,i={signal:t,capture:!1,passive:!1};e.addEventListener("touchmove",this.#ce.bind(this),i);const s=this.#ue.bind(this);e.addEventListener("touchend",s,i),e.addEventListener("touchcancel",s,i),i.capture=!0,e.addEventListener("pointerdown",Et,i),e.addEventListener("pointermove",Et,i),e.addEventListener("pointercancel",Et,i),e.addEventListener("pointerup",Et,i),this.#ie?.()}if(Et(t),2!==t.touches.length||this.#te?.())return void(this.#oe=null);let[e,i]=t.touches;e.identifier>i.identifier&&([e,i]=[i,e]),this.#oe={touch0X:e.screenX,touch0Y:e.screenY,touch1X:i.screenX,touch1Y:i.screenY}}#ce(t){if(!this.#oe||2!==t.touches.length)return;Et(t);let[e,i]=t.touches;e.identifier>i.identifier&&([e,i]=[i,e]);const{screenX:s,screenY:n}=e,{screenX:a,screenY:r}=i,o=this.#oe,{touch0X:l,touch0Y:h,touch1X:d,touch1Y:c}=o,u=d-l,p=c-h,g=a-s,m=r-n,f=Math.hypot(g,m)||1,b=Math.hypot(u,p)||1;if(!this.#Zt&&Math.abs(b-f)<=jt.MIN_TOUCH_DISTANCE_TO_PINCH)return;if(o.touch0X=s,o.touch0Y=n,o.touch1X=a,o.touch1Y=r,!this.#Zt)return void(this.#Zt=!0);const v=[(s+a)/2,(n+r)/2];this.#se?.(v,b,f)}#ue(t){t.touches.length>=2||(this.#he&&(this.#he.abort(),this.#he=null,this.#ne?.()),this.#oe&&(Et(t),this.#oe=null,this.#Zt=!1))}destroy(){this.#le?.abort(),this.#le=null,this.#ae?.abort(),this.#ae=null}}class Vt{#pe=null;#ge=null;#o=null;#me=!1;#fe=null;#be="";#ve=!1;#Ae=null;#we=null;#ye=null;#_e=null;#xe="";#Se=!1;#Ee=null;#Ce=!1;#Te=!1;#Me=!1;#Pe=null;#Ie=0;#ke=0;#De=null;#Re=null;isSelected=!1;_isCopy=!1;_editToolbar=null;_initialOptions=Object.create(null);_initialData=null;_isVisible=!0;_uiManager=null;_focusEventsAllowed=!0;static _l10n=null;static _l10nResizer=null;#Le=!1;#Fe=Vt._zIndex++;static _borderLineWidth=-1;static _colorManager=new Ut;static _zIndex=1;static _telemetryTimeout=1e3;static get _resizerKeyboardManager(){const t=Vt.prototype._resizeWithKeyboard,e=Gt.TRANSLATE_SMALL,i=Gt.TRANSLATE_BIG;return q(this,"_resizerKeyboardManager",new Ht([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-i,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[i,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-i]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,i]}],[["Escape","mac+Escape"],Vt.prototype._stopResizingWithKeyboard]]))}constructor(t){this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:i,pageHeight:s,pageX:n,pageY:a}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[i,s],this.pageTranslation=[n,a];const[r,o]=this.parentDimensions;this.x=t.x/r,this.y=t.y/o,this.isAttachedToDOM=!1,this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get isDrawer(){return!1}static get _defaultLineColor(){return q(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new Wt({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t,e){if(Vt._l10n??=t,Vt._l10nResizer||=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"}),-1!==Vt._borderLineWidth)return;const i=getComputedStyle(document.documentElement);Vt._borderLineWidth=parseFloat(i.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){$("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#Le}set _isDraggable(t){this.#Le=t,this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t),this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t),this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2,this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#Fe}setParent(t){null!==t?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):this.#Ne(),this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#Se?this.#Se=!1:this.parent.setSelected(this))}focusout(t){if(!this._focusEventsAllowed)return;if(!this.isAttachedToDOM)return;const e=t.relatedTarget;e?.closest(`#${this.id}`)||(t.preventDefault(),this.parent?.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,i,s){const[n,a]=this.parentDimensions;[i,s]=this.screenToPageTranslation(i,s),this.x=(t+i)/n,this.y=(e+s)/a,this.fixAndSetPosition()}_moveAfterPaste(t,e){const[i,s]=this.parentDimensions;this.setAt(t*i,e*s,this.width*i,this.height*s),this._onTranslated()}#Oe([t,e],i,s){[i,s]=this.screenToPageTranslation(i,s),this.x+=i/t,this.y+=s/e,this._onTranslating(this.x,this.y),this.fixAndSetPosition()}translate(t,e){this.#Oe(this.parentDimensions,t,e)}translateInPage(t,e){this.#Ee||=[this.x,this.y,this.width,this.height],this.#Oe(this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}translationDone(){this._onTranslated(this.x,this.y)}drag(t,e){this.#Ee||=[this.x,this.y,this.width,this.height];const{div:i,parentDimensions:[s,n]}=this;if(this.x+=t/s,this.y+=e/n,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:t,y:e}=this.div.getBoundingClientRect();this.parent.findNewParent(this,t,e)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:a,y:r}=this;const[o,l]=this.getBaseTranslation();a+=o,r+=l;const{style:h}=i;h.left=`${(100*a).toFixed(2)}%`,h.top=`${(100*r).toFixed(2)}%`,this._onTranslating(a,r),i.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!this.#Ee&&(this.#Ee[0]!==this.x||this.#Ee[1]!==this.y)}get _hasBeenResized(){return!!this.#Ee&&(this.#Ee[2]!==this.width||this.#Ee[3]!==this.height)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:i}=Vt,s=i/t,n=i/e;switch(this.rotation){case 90:return[-s,n];case 180:return[s,n];case 270:return[s,-n];default:return[-s,-n]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const{div:{style:e},pageDimensions:[i,s]}=this;let{x:n,y:a,width:r,height:o}=this;if(r*=i,o*=s,n*=i,a*=s,this._mustFixPosition)switch(t){case 0:n=ct(n,0,i-r),a=ct(a,0,s-o);break;case 90:n=ct(n,0,i-o),a=ct(a,r,s);break;case 180:n=ct(n,r,i),a=ct(a,o,s);break;case 270:n=ct(n,o,i),a=ct(a,0,s-r)}this.x=n/=i,this.y=a/=s;const[l,h]=this.getBaseTranslation();n+=l,a+=h,e.left=`${(100*n).toFixed(2)}%`,e.top=`${(100*a).toFixed(2)}%`,this.moveInDOM()}static#Be(t,e,i){switch(i){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return Vt.#Be(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return Vt.#Be(t,e,360-this.parentRotation)}#ze(t){switch(t){case 90:{const[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{const[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,i]}=this;return[e*t,i*t]}setDims(t,e){const[i,s]=this.parentDimensions,{style:n}=this.div;n.width=`${(100*t/i).toFixed(2)}%`,this.#ve||(n.height=`${(100*e/s).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:i}=t,s=i.endsWith("%"),n=!this.#ve&&e.endsWith("%");if(s&&n)return;const[a,r]=this.parentDimensions;s||(t.width=`${(100*parseFloat(i)/a).toFixed(2)}%`),this.#ve||n||(t.height=`${(100*parseFloat(e)/r).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#He(){if(this.#Ae)return;this.#Ae=document.createElement("div"),this.#Ae.classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const i of t){const t=document.createElement("div");this.#Ae.append(t),t.classList.add("resizer",i),t.setAttribute("data-resizer-name",i),t.addEventListener("pointerdown",this.#Ue.bind(this,i),{signal:e}),t.addEventListener("contextmenu",St,{signal:e}),t.tabIndex=-1}this.div.prepend(this.#Ae)}#Ue(t,e){e.preventDefault();const{isMac:i}=st.platform;if(0!==e.button||e.ctrlKey&&i)return;this.#o?.toggle(!1);const s=this._isDraggable;this._isDraggable=!1,this.#we=[e.screenX,e.screenY];const n=new AbortController,a=this._uiManager.combinedSignal(n);this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",this.#Ge.bind(this,t),{passive:!0,capture:!0,signal:a}),window.addEventListener("touchmove",Et,{passive:!1,signal:a}),window.addEventListener("contextmenu",St,{signal:a}),this.#ye={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const r=this.parent.div.style.cursor,o=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const l=()=>{n.abort(),this.parent.togglePointerEvents(!0),this.#o?.toggle(!0),this._isDraggable=s,this.parent.div.style.cursor=r,this.div.style.cursor=o,this.#$e()};window.addEventListener("pointerup",l,{signal:a}),window.addEventListener("blur",l,{signal:a})}#je(t,e,i,s){this.width=i,this.height=s,this.x=t,this.y=e;const[n,a]=this.parentDimensions;this.setDims(n*i,a*s),this.fixAndSetPosition(),this._onResized()}_onResized(){}#$e(){if(!this.#ye)return;const{savedX:t,savedY:e,savedWidth:i,savedHeight:s}=this.#ye;this.#ye=null;const n=this.x,a=this.y,r=this.width,o=this.height;n===t&&a===e&&r===i&&o===s||this.addCommands({cmd:this.#je.bind(this,n,a,r,o),undo:this.#je.bind(this,t,e,i,s),mustExec:!0})}static _round(t){return Math.round(1e4*t)/1e4}#Ge(t,e){const[i,s]=this.parentDimensions,n=this.x,a=this.y,r=this.width,o=this.height,l=Vt.MIN_SIZE/i,h=Vt.MIN_SIZE/s,d=this.#ze(this.rotation),c=(t,e)=>[d[0]*t+d[2]*e,d[1]*t+d[3]*e],u=this.#ze(360-this.rotation);let p,g,m=!1,f=!1;switch(t){case"topLeft":m=!0,p=(t,e)=>[0,0],g=(t,e)=>[t,e];break;case"topMiddle":p=(t,e)=>[t/2,0],g=(t,e)=>[t/2,e];break;case"topRight":m=!0,p=(t,e)=>[t,0],g=(t,e)=>[0,e];break;case"middleRight":f=!0,p=(t,e)=>[t,e/2],g=(t,e)=>[0,e/2];break;case"bottomRight":m=!0,p=(t,e)=>[t,e],g=(t,e)=>[0,0];break;case"bottomMiddle":p=(t,e)=>[t/2,e],g=(t,e)=>[t/2,0];break;case"bottomLeft":m=!0,p=(t,e)=>[0,e],g=(t,e)=>[t,0];break;case"middleLeft":f=!0,p=(t,e)=>[0,e/2],g=(t,e)=>[t,e/2]}const b=p(r,o),v=g(r,o);let A=c(...v);const w=Vt._round(n+A[0]),y=Vt._round(a+A[1]);let _,x,S=1,E=1;if(e.fromKeyboard)({deltaX:_,deltaY:x}=e);else{const{screenX:t,screenY:i}=e,[s,n]=this.#we;[_,x]=this.screenToPageTranslation(t-s,i-n),this.#we[0]=t,this.#we[1]=i}var C,T;if([_,x]=(C=_/i,T=x/s,[u[0]*C+u[2]*T,u[1]*C+u[3]*T]),m){const t=Math.hypot(r,o);S=E=Math.max(Math.min(Math.hypot(v[0]-b[0]-_,v[1]-b[1]-x)/t,1/r,1/o),l/r,h/o)}else f?S=ct(Math.abs(v[0]-b[0]-_),l,1)/r:E=ct(Math.abs(v[1]-b[1]-x),h,1)/o;const M=Vt._round(r*S),P=Vt._round(o*E);A=c(...g(M,P));const I=w-A[0],k=y-A[1];this.#Ee||=[this.x,this.y,this.width,this.height],this.width=M,this.height=P,this.x=I,this.y=k,this.setDims(i*M,s*P),this.fixAndSetPosition(),this._onResizing()}_onResizing(){}altTextFinish(){this.#o?.finish()}async addEditToolbar(){return this._editToolbar||this.#Te||(this._editToolbar=new Lt(this),this.div.append(this._editToolbar.render()),this.#o&&await this._editToolbar.addAltText(this.#o)),this._editToolbar}removeEditToolbar(){this._editToolbar&&(this._editToolbar.remove(),this._editToolbar=null,this.#o?.destroy())}addContainer(t){const e=this._editToolbar?.div;e?e.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){this.#o||($t.initialize(Vt._l10n),this.#o=new $t(this),this.#pe&&(this.#o.data=this.#pe,this.#pe=null),await this.addEditToolbar())}get altTextData(){return this.#o?.data}set altTextData(t){this.#o&&(this.#o.data=t)}get guessedAltText(){return this.#o?.guessedText}async setGuessedAltText(t){await(this.#o?.setGuessedText(t))}serializeAltText(t){return this.#o?.serialize(t)}hasAltText(){return!!this.#o&&!this.#o.isEmpty()}hasAltTextData(){return this.#o?.hasData()??!1}render(){const t=this.div=document.createElement("div");t.setAttribute("data-editor-rotation",(360-this.rotation)%360),t.className=this.name,t.setAttribute("id",this.id),t.tabIndex=this.#me?-1:0,t.setAttribute("role","application"),this.defaultL10nId&&t.setAttribute("data-l10n-id",this.defaultL10nId),this._isVisible||t.classList.add("hidden"),this.setInForeground(),this.#Ve();const[e,i]=this.parentDimensions;this.parentRotation%180!=0&&(t.style.maxWidth=`${(100*i/e).toFixed(2)}%`,t.style.maxHeight=`${(100*e/i).toFixed(2)}%`);const[s,n]=this.getInitialTranslation();return this.translate(s,n),Nt(this,t,["keydown","pointerdown","dblclick"]),this.isResizable&&this._uiManager._supportsPinchToZoom&&(this.#Re||=new jt({container:t,isPinchingDisabled:()=>!this.isSelected,onPinchStart:this.#We.bind(this),onPinching:this.#qe.bind(this),onPinchEnd:this.#Xe.bind(this),signal:this._uiManager._signal})),this._uiManager._editorUndoBar?.hide(),t}#We(){this.#ye={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height},this.#o?.toggle(!1),this.parent.togglePointerEvents(!1)}#qe(t,e,i){let s=i/e*.7+1-.7;if(1===s)return;const n=this.#ze(this.rotation),a=(t,e)=>[n[0]*t+n[2]*e,n[1]*t+n[3]*e],[r,o]=this.parentDimensions,l=this.x,h=this.y,d=this.width,c=this.height,u=Vt.MIN_SIZE/r,p=Vt.MIN_SIZE/o;s=Math.max(Math.min(s,1/d,1/c),u/d,p/c);const g=Vt._round(d*s),m=Vt._round(c*s);if(g===d&&m===c)return;this.#Ee||=[l,h,d,c];const f=a(d/2,c/2),b=Vt._round(l+f[0]),v=Vt._round(h+f[1]),A=a(g/2,m/2);this.x=b-A[0],this.y=v-A[1],this.width=g,this.height=m,this.setDims(r*g,o*m),this.fixAndSetPosition(),this._onResizing()}#Xe(){this.#o?.toggle(!0),this.parent.togglePointerEvents(!0),this.#$e()}pointerdown(t){const{isMac:e}=st.platform;0!==t.button||t.ctrlKey&&e?t.preventDefault():(this.#Se=!0,this._isDraggable?this.#Ke(t):this.#Ye(t))}#Ye(t){const{isMac:e}=st.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}#Ke(t){const{isSelected:e}=this;this._uiManager.setUpDragSession();let i=!1;const s=new AbortController,n=this._uiManager.combinedSignal(s),a={capture:!0,passive:!1,signal:n},r=t=>{s.abort(),this.#fe=null,this.#Se=!1,this._uiManager.endDragSession()||this.#Ye(t),i&&this._onStopDragging()};e&&(this.#Ie=t.clientX,this.#ke=t.clientY,this.#fe=t.pointerId,this.#be=t.pointerType,window.addEventListener("pointermove",(t=>{i||(i=!0,this._onStartDragging());const{clientX:e,clientY:s,pointerId:n}=t;if(n!==this.#fe)return void Et(t);const[a,r]=this.screenToPageTranslation(e-this.#Ie,s-this.#ke);this.#Ie=e,this.#ke=s,this._uiManager.dragSelectedEditors(a,r)}),a),window.addEventListener("touchmove",Et,a),window.addEventListener("pointerdown",(t=>{t.pointerType===this.#be&&(this.#Re||t.isPrimary)&&r(t),Et(t)}),a));const o=t=>{this.#fe&&this.#fe!==t.pointerId?Et(t):r(t)};window.addEventListener("pointerup",o,{signal:n}),window.addEventListener("blur",o,{signal:n})}_onStartDragging(){}_onStopDragging(){}moveInDOM(){this.#Pe&&clearTimeout(this.#Pe),this.#Pe=setTimeout((()=>{this.#Pe=null,this.parent?.moveEditorInDOM(this)}),0)}_setParentAndPosition(t,e,i){t.changeParent(this),this.x=e,this.y=i,this.fixAndSetPosition(),this._onTranslated()}getRect(t,e,i=this.rotation){const s=this.parentScale,[n,a]=this.pageDimensions,[r,o]=this.pageTranslation,l=t/s,h=e/s,d=this.x*n,c=this.y*a,u=this.width*n,p=this.height*a;switch(i){case 0:return[d+l+r,a-c-h-p+o,d+l+u+r,a-c-h+o];case 90:return[d+h+r,a-c+l+o,d+h+p+r,a-c+l+u+o];case 180:return[d-l-u+r,a-c+h+o,d-l+r,a-c+h+p+o];case 270:return[d-h-p+r,a-c-l-u+o,d-h+r,a-c-l+o];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[i,s,n,a]=t,r=n-i,o=a-s;switch(this.rotation){case 0:return[i,e-a,r,o];case 90:return[i,e-s,o,r];case 180:return[n,e-s,r,o];case 270:return[n,e-a,o,r];default:throw new Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){return!this.isInEditMode()&&(this.parent.setEditingState(!1),this.#Te=!0,!0)}disableEditMode(){return!!this.isInEditMode()&&(this.parent.setEditingState(!0),this.#Te=!1,!0)}isInEditMode(){return this.#Te}shouldGetKeyboardEvents(){return this.#Me}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){const{top:t,left:e,bottom:i,right:s}=this.getClientDimensions(),{innerHeight:n,innerWidth:a}=window;return e<a&&s>0&&t<n&&i>0}#Ve(){if(this.#_e||!this.div)return;this.#_e=new AbortController;const t=this._uiManager.combinedSignal(this.#_e);this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t}),this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})}rebuild(){this.#Ve()}rotate(t){}resize(){}serializeDeleted(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:this._initialData?.popupRef||""}}serialize(t=!1,e=null){$("An editor must be serializable")}static async deserialize(t,e,i){const s=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:i});s.rotation=t.rotation,s.#pe=t.accessibilityData,s._isCopy=t.isCopy||!1;const[n,a]=s.pageDimensions,[r,o,l,h]=s.getRectInCurrentCoords(t.rect,a);return s.x=r/n,s.y=o/a,s.width=l/n,s.height=h/a,s}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||null!==this.serialize())}remove(){if(this.#_e?.abort(),this.#_e=null,this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),this.#Pe&&(clearTimeout(this.#Pe),this.#Pe=null),this.#Ne(),this.removeEditToolbar(),this.#De){for(const t of this.#De.values())clearTimeout(t);this.#De=null}this.parent=null,this.#Re?.destroy(),this.#Re=null}get isResizable(){return!1}makeResizable(){this.isResizable&&(this.#He(),this.#Ae.classList.remove("hidden"))}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||"Enter"!==t.key)return;this._uiManager.setSelected(this),this.#ye={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const e=this.#Ae.children;if(!this.#ge){this.#ge=Array.from(e);const t=this.#Qe.bind(this),i=this.#Je.bind(this),s=this._uiManager._signal;for(const e of this.#ge){const n=e.getAttribute("data-resizer-name");e.setAttribute("role","spinbutton"),e.addEventListener("keydown",t,{signal:s}),e.addEventListener("blur",i,{signal:s}),e.addEventListener("focus",this.#Ze.bind(this,n),{signal:s}),e.setAttribute("data-l10n-id",Vt._l10nResizer[n])}}const i=this.#ge[0];let s=0;for(const a of e){if(a===i)break;s++}const n=(360-this.rotation+this.parentRotation)%360/90*(this.#ge.length/4);if(n!==s){if(n<s)for(let e=0;e<s-n;e++)this.#Ae.append(this.#Ae.firstChild);else if(n>s)for(let e=0;e<n-s;e++)this.#Ae.firstChild.before(this.#Ae.lastChild);let t=0;for(const i of e){const e=this.#ge[t++].getAttribute("data-resizer-name");i.setAttribute("data-l10n-id",Vt._l10nResizer[e])}}this.#ti(0),this.#Me=!0,this.#Ae.firstChild.focus({focusVisible:!0}),t.preventDefault(),t.stopImmediatePropagation()}#Qe(t){Vt._resizerKeyboardManager.exec(this,t)}#Je(t){this.#Me&&t.relatedTarget?.parentNode!==this.#Ae&&this.#Ne()}#Ze(t){this.#xe=this.#Me?t:""}#ti(t){if(this.#ge)for(const e of this.#ge)e.tabIndex=t}_resizeWithKeyboard(t,e){this.#Me&&this.#Ge(this.#xe,{deltaX:t,deltaY:e,fromKeyboard:!0})}#Ne(){this.#Me=!1,this.#ti(-1),this.#$e()}_stopResizingWithKeyboard(){this.#Ne(),this.div.focus()}select(){this.isSelected&&this._editToolbar||(this.isSelected=!0,this.makeResizable(),this.div?.classList.add("selectedEditor"),this._editToolbar?(this._editToolbar?.show(),this.#o?.toggleAltTextBadge(!1)):this.addEditToolbar().then((()=>{this.div?.classList.contains("selectedEditor")&&this._editToolbar?.show()})))}unselect(){this.isSelected&&(this.isSelected=!1,this.#Ae?.classList.add("hidden"),this.div?.classList.remove("selectedEditor"),this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),this._editToolbar?.hide(),this.#o?.toggleAltTextBadge(!0))}updateParams(t,e){}disableEditing(){}enableEditing(){}get canChangeContent(){return!1}enterInEditMode(){this.canChangeContent&&(this.enableEditMode(),this.div.focus())}dblclick(t){this.enterInEditMode(),this.parent.updateToolbar({mode:this.constructor._editorType,editId:this.id})}getElementForAltText(){return this.div}get contentDiv(){return this.div}get isEditing(){return this.#Ce}set isEditing(t){this.#Ce=t,this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){this.#ve=!0;const i=t/e,{style:s}=this.div;s.aspectRatio=i,s.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){this.#De||=new Map;const{action:e}=t;let i=this.#De.get(e);return i&&clearTimeout(i),i=setTimeout((()=>{this._reportTelemetry(t),this.#De.delete(e),0===this.#De.size&&(this.#De=null)}),Vt._telemetryTimeout),void this.#De.set(e,i)}t.type||=this.editorType,this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),this.#me=!1}disable(){this.div&&(this.div.tabIndex=-1),this.#me=!0}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(e){if("CANVAS"===e.nodeName){const t=e;e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.before(e)}}else e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.container.prepend(e);return e}resetAnnotationElement(t){const{firstChild:e}=t.container;"DIV"===e?.nodeName&&e.classList.contains("annotationContent")&&e.remove()}}class Wt extends Vt{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return this.serializeDeleted()}}const qt=3285377520,Xt=4294901760,Kt=65535;class Yt{constructor(t){this.h1=t?4294967295&t:qt,this.h2=t?4294967295&t:qt}update(t){let e,i;if("string"==typeof t){e=new Uint8Array(2*t.length),i=0;for(let s=0,n=t.length;s<n;s++){const n=t.charCodeAt(s);n<=255?e[i++]=n:(e[i++]=n>>>8,e[i++]=255&n)}}else{if(!ArrayBuffer.isView(t))throw new Error("Invalid data format, must be a string or TypedArray.");e=t.slice(),i=e.byteLength}const s=i>>2,n=i-4*s,a=new Uint32Array(e.buffer,0,s);let r=0,o=0,l=this.h1,h=this.h2;const d=3432918353,c=461845907,u=11601,p=13715;for(let g=0;g<s;g++)1&g?(r=a[g],r=r*d&Xt|r*u&Kt,r=r<<15|r>>>17,r=r*c&Xt|r*p&Kt,l^=r,l=l<<13|l>>>19,l=5*l+3864292196):(o=a[g],o=o*d&Xt|o*u&Kt,o=o<<15|o>>>17,o=o*c&Xt|o*p&Kt,h^=o,h=h<<13|h>>>19,h=5*h+3864292196);switch(r=0,n){case 3:r^=e[4*s+2]<<16;case 2:r^=e[4*s+1]<<8;case 1:r^=e[4*s],r=r*d&Xt|r*u&Kt,r=r<<15|r>>>17,r=r*c&Xt|r*p&Kt,1&s?l^=r:h^=r}this.h1=l,this.h2=h}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,t=3981806797*t&Xt|36045*t&Kt,e=4283543511*e&Xt|(2950163797*(e<<16|t>>>16)&Xt)>>>16,t^=e>>>1,t=444984403*t&Xt|60499*t&Kt,e=3301882366*e&Xt|(3120437893*(e<<16|t>>>16)&Xt)>>>16,t^=e>>>1,(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const Qt=Object.freeze({map:null,hash:"",transfer:void 0});class Jt{#ei=!1;#ii=null;#si=new Map;constructor(){this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){const i=this.#si.get(t);return void 0===i?e:Object.assign(e,i)}getRawValue(t){return this.#si.get(t)}remove(t){if(this.#si.delete(t),0===this.#si.size&&this.resetModified(),"function"==typeof this.onAnnotationEditor){for(const t of this.#si.values())if(t instanceof Vt)return;this.onAnnotationEditor(null)}}setValue(t,e){const i=this.#si.get(t);let s=!1;if(void 0!==i)for(const[n,a]of Object.entries(e))i[n]!==a&&(s=!0,i[n]=a);else s=!0,this.#si.set(t,e);s&&this.#ni(),e instanceof Vt&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#si.has(t)}get size(){return this.#si.size}#ni(){this.#ei||(this.#ei=!0,"function"==typeof this.onSetModified&&this.onSetModified())}resetModified(){this.#ei&&(this.#ei=!1,"function"==typeof this.onResetModified&&this.onResetModified())}get print(){return new Zt(this)}get serializable(){if(0===this.#si.size)return Qt;const t=new Map,e=new Yt,i=[],s=Object.create(null);let n=!1;for(const[a,r]of this.#si){const i=r instanceof Vt?r.serialize(!1,s):r;i&&(t.set(a,i),e.update(`${a}:${JSON.stringify(i)}`),n||=!!i.bitmap)}if(n)for(const a of t.values())a.bitmap&&i.push(a.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:i}:Qt}get editorStats(){let t=null;const e=new Map;for(const i of this.#si.values()){if(!(i instanceof Vt))continue;const s=i.telemetryFinalData;if(!s)continue;const{type:n}=s;e.has(n)||e.set(n,Object.getPrototypeOf(i).constructor),t||=Object.create(null);const a=t[n]||=new Map;for(const[t,e]of Object.entries(s)){if("type"===t)continue;let i=a.get(t);i||(i=new Map,a.set(t,i));const s=i.get(e)??0;i.set(e,s+1)}}for(const[i,s]of e)t[i]=s.computeTelemetryFinalData(t[i]);return t}resetModifiedIds(){this.#ii=null}get modifiedIds(){if(this.#ii)return this.#ii;const t=[];for(const e of this.#si.values())e instanceof Vt&&e.annotationElementId&&e.serialize()&&t.push(e.annotationElementId);return this.#ii={ids:new Set(t),hash:t.join(",")}}[Symbol.iterator](){return this.#si.entries()}}class Zt extends Jt{#ai;constructor(t){super();const{map:e,hash:i,transfer:s}=t.serializable,n=structuredClone(e,s?{transfer:s}:null);this.#ai={map:n,hash:i,transfer:s}}get print(){$("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#ai}get modifiedIds(){return q(this,"modifiedIds",{ids:new Set,hash:""})}}class te{#ri=new Set;constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),this.#ri.clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont({systemFontInfo:t,disableFontFace:e,_inspectFont:i}){if(t&&!this.#ri.has(t.loadedName))if(j(!e,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:e,src:s,style:n}=t,a=new FontFace(e,s,n);this.addNativeFontFace(a);try{await a.load(),this.#ri.add(e),i?.(t)}catch{G(t.baseFontName),this.removeNativeFontFace(a)}}else $("Not implemented: loadSystemFont without the Font Loading API.")}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo)return void await this.loadSystemFont(t);if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(i){throw G(e.family),t.disableFontFace=!0,i}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise((e=>{const i=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,i)}))}}get isFontLoadingAPISupported(){return q(this,"isFontLoadingAPISupported",!!this._document?.fonts)}get isSyncFontLoadingSupported(){return q(this,"isSyncFontLoadingSupported",s||st.platform.isFirefox)}_queueLoadingCallback(t){const{loadingRequests:e}=this,i={done:!1,complete:function(){for(j(!i.done,"completeRequest() cannot be called twice."),i.done=!0;e.length>0&&e[0].done;){const t=e.shift();setTimeout(t.callback,0)}},callback:t};return e.push(i),i}get _loadTestFont(){return q(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e){function i(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function s(t,e,i,s){return t.substring(0,e)+s+t.substring(e+i)}let n,a;const r=this._document.createElement("canvas");r.width=1,r.height=1;const o=r.getContext("2d");let l=0;const h=`lt${Date.now()}${this.loadTestFontId++}`;let d=this._loadTestFont;d=s(d,976,h.length,h);const c=1482184792;let u=i(d,16);for(n=0,a=h.length-3;n<a;n+=4)u=u-c+i(h,n)|0;var p;n<h.length&&(u=u-c+i(h+"XXX",n)|0),d=s(d,16,4,(p=u,String.fromCharCode(p>>24&255,p>>16&255,p>>8&255,255&p)));const g=`@font-face {font-family:"${h}";src:${`url(data:font/opentype;base64,${btoa(d)});`}}`;this.insertRule(g);const m=this._document.createElement("div");m.style.visibility="hidden",m.style.width=m.style.height="10px",m.style.position="absolute",m.style.top=m.style.left="0px";for(const f of[t.loadedName,h]){const t=this._document.createElement("span");t.textContent="Hi",t.style.fontFamily=f,m.append(t)}this._document.body.append(m),function t(e,i){if(++l>30)return G(),void i();o.font="30px "+e,o.fillText(".",0,20),o.getImageData(0,0,1,1).data[3]>0?i():setTimeout(t.bind(null,e,i))}(h,(()=>{m.remove(),e.complete()}))}}class ee{constructor(t,e=null){this.compiledGlyphs=Object.create(null);for(const i in t)this[i]=t[i];this._inspectFont=e}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});return this._inspectFont?.(this),t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=`url(data:${this.mimetype};base64,${ut(this.data)});`;let e;if(this.cssFontInfo){let i=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(i+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),e=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${i}src:${t}}`}else e=`@font-face {font-family:"${this.loadedName}";src:${t}}`;return this._inspectFont?.(this,t),e}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];const i=this.loadedName+"_path_"+e;let s;try{s=t.get(i)}catch(a){G()}const n=new Path2D(s||"");return this.fontExtraProperties||t.delete(i),this.compiledGlyphs[e]=n}}function ie(t){if("string"!=typeof t)return null;if(t.endsWith("/"))return t;throw new Error(`Invalid factory url: "${t}" must include trailing slash.`)}const se=t=>"object"==typeof t&&Number.isInteger(t?.num)&&t.num>=0&&Number.isInteger(t?.gen)&&t.gen>=0,ne=function(t,e,i){if(!Array.isArray(i)||i.length<2)return!1;const[s,n,...a]=i;if(!t(s)&&!Number.isInteger(s))return!1;if(!e(n))return!1;const r=a.length;let o=!0;switch(n.name){case"XYZ":if(r<2||r>3)return!1;break;case"Fit":case"FitB":return 0===r;case"FitH":case"FitBH":case"FitV":case"FitBV":if(r>1)return!1;break;case"FitR":if(4!==r)return!1;o=!1;break;default:return!1}for(const l of a)if(!("number"==typeof l||o&&null===l))return!1;return!0}.bind(null,se,(t=>"object"==typeof t&&"string"==typeof t?.name));class ae{#oi=new Map;#li=Promise.resolve();postMessage(t,e){const i={data:structuredClone(t,e?{transfer:e}:null)};this.#li.then((()=>{for(const[t]of this.#oi)t.call(this,i)}))}addEventListener(t,e,i=null){let s=null;if(i?.signal instanceof AbortSignal){const{signal:n}=i;if(n.aborted)return void G();const a=()=>this.removeEventListener(t,e);s=()=>n.removeEventListener("abort",a),n.addEventListener("abort",a)}this.#oi.set(e,s)}removeEventListener(t,e){const i=this.#oi.get(e);i?.(),this.#oi.delete(e)}terminate(){for(const[,t]of this.#oi)t?.();this.#oi.clear()}}const re=1,oe=2,le=1,he=2,de=3,ce=4,ue=5,pe=6,ge=7,me=8;function fe(){}function be(t){if(t instanceof tt||t instanceof Q||t instanceof K||t instanceof J||t instanceof Y)return t;switch(t instanceof Error||"object"==typeof t&&null!==t||$('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),t.name){case"AbortException":return new tt(t.message);case"InvalidPDFException":return new Q(t.message);case"PasswordException":return new K(t.message,t.code);case"ResponseException":return new J(t.message,t.status,t.missing);case"UnknownErrorException":return new Y(t.message,t.details)}return new Y(t.message,t.toString())}class ve{#hi=new AbortController;constructor(t,e,i){this.sourceName=t,this.targetName=e,this.comObj=i,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),i.addEventListener("message",this.#di.bind(this),{signal:this.#hi.signal})}#di({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream)return void this.#ci(t);if(t.callback){const e=t.callbackId,i=this.callbackCapabilities[e];if(!i)throw new Error(`Cannot resolve callback ${e}`);if(delete this.callbackCapabilities[e],t.callback===re)i.resolve(t.data);else{if(t.callback!==oe)throw new Error("Unexpected callback case");i.reject(be(t.reason))}return}const e=this.actionHandler[t.action];if(!e)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const i=this.sourceName,s=t.sourceName,n=this.comObj;Promise.try(e,t.data).then((function(e){n.postMessage({sourceName:i,targetName:s,callback:re,callbackId:t.callbackId,data:e})}),(function(e){n.postMessage({sourceName:i,targetName:s,callback:oe,callbackId:t.callbackId,reason:be(e)})}))}else t.streamId?this.#ui(t):e(t.data)}on(t,e){const i=this.actionHandler;if(i[t])throw new Error(`There is already an actionName called "${t}"`);i[t]=e}send(t,e,i){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},i)}sendWithPromise(t,e,i){const s=this.callbackId++,n=Promise.withResolvers();this.callbackCapabilities[s]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:s,data:e},i)}catch(a){n.reject(a)}return n.promise}sendWithStream(t,e,i,s){const n=this.streamId++,a=this.sourceName,r=this.targetName,o=this.comObj;return new ReadableStream({start:i=>{const l=Promise.withResolvers();return this.streamControllers[n]={controller:i,startCall:l,pullCall:null,cancelCall:null,isClosed:!1},o.postMessage({sourceName:a,targetName:r,action:t,streamId:n,data:e,desiredSize:i.desiredSize},s),l.promise},pull:t=>{const e=Promise.withResolvers();return this.streamControllers[n].pullCall=e,o.postMessage({sourceName:a,targetName:r,stream:pe,streamId:n,desiredSize:t.desiredSize}),e.promise},cancel:t=>{j(t instanceof Error,"cancel must have a valid reason");const e=Promise.withResolvers();return this.streamControllers[n].cancelCall=e,this.streamControllers[n].isClosed=!0,o.postMessage({sourceName:a,targetName:r,stream:le,streamId:n,reason:be(t)}),e.promise}},i)}#ui(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,n=this.comObj,a=this,r=this.actionHandler[t.action],o={enqueue(t,a=1,r){if(this.isCancelled)return;const o=this.desiredSize;this.desiredSize-=a,o>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),n.postMessage({sourceName:i,targetName:s,stream:ce,streamId:e,chunk:t},r)},close(){this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:i,targetName:s,stream:de,streamId:e}),delete a.streamSinks[e])},error(t){j(t instanceof Error,"error must have a valid reason"),this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:i,targetName:s,stream:ue,streamId:e,reason:be(t)}))},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};o.sinkCapability.resolve(),o.ready=o.sinkCapability.promise,this.streamSinks[e]=o,Promise.try(r,t.data,o).then((function(){n.postMessage({sourceName:i,targetName:s,stream:me,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:me,streamId:e,reason:be(t)})}))}#ci(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,n=this.comObj,a=this.streamControllers[e],r=this.streamSinks[e];switch(t.stream){case me:t.success?a.startCall.resolve():a.startCall.reject(be(t.reason));break;case ge:t.success?a.pullCall.resolve():a.pullCall.reject(be(t.reason));break;case pe:if(!r){n.postMessage({sourceName:i,targetName:s,stream:ge,streamId:e,success:!0});break}r.desiredSize<=0&&t.desiredSize>0&&r.sinkCapability.resolve(),r.desiredSize=t.desiredSize,Promise.try(r.onPull||fe).then((function(){n.postMessage({sourceName:i,targetName:s,stream:ge,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:ge,streamId:e,reason:be(t)})}));break;case ce:if(j(a,"enqueue should have stream controller"),a.isClosed)break;a.controller.enqueue(t.chunk);break;case de:if(j(a,"close should have stream controller"),a.isClosed)break;a.isClosed=!0,a.controller.close(),this.#pi(a,e);break;case ue:j(a,"error should have stream controller"),a.controller.error(be(t.reason)),this.#pi(a,e);break;case he:t.success?a.cancelCall.resolve():a.cancelCall.reject(be(t.reason)),this.#pi(a,e);break;case le:if(!r)break;const o=be(t.reason);Promise.try(r.onCancel||fe,o).then((function(){n.postMessage({sourceName:i,targetName:s,stream:he,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:he,streamId:e,reason:be(t)})})),r.sinkCapability.reject(o),r.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async#pi(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]),delete this.streamControllers[e]}destroy(){this.#hi?.abort(),this.#hi=null}}class Ae{#gi=!1;constructor({enableHWA:t=!1}){this.#gi=t}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const i=this._createCanvas(t,e);return{canvas:i,context:i.getContext("2d",{willReadFrequently:!this.#gi})}}reset(t,e,i){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||i<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=i}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){$("Abstract method `_createCanvas` called.")}}class we extends Ae{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e}),this._document=t}_createCanvas(t,e){const i=this._document.createElement("canvas");return i.width=t,i.height=e,i}}class ye{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t,this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(e).then((t=>({cMapData:t,isCompressed:this.isCompressed}))).catch((t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)}))}async _fetch(t){$("Abstract method `_fetch` called.")}}class _e extends ye{async _fetch(t){const e=await mt(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):it(e)}}class xe{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,i,s,n){return"none"}destroy(t=!1){}}class Se extends xe{#mi;#fi;#bi;#vi;#Ai;#wi;#w=0;constructor({docId:t,ownerDocument:e=globalThis.document}){super(),this.#vi=t,this.#Ai=e}get#_(){return this.#fi||=new Map}get#yi(){return this.#wi||=new Map}get#_i(){if(!this.#bi){const t=this.#Ai.createElement("div"),{style:e}=t;e.visibility="hidden",e.contain="strict",e.width=e.height=0,e.position="absolute",e.top=e.left=0,e.zIndex=-1;const i=this.#Ai.createElementNS(pt,"svg");i.setAttribute("width",0),i.setAttribute("height",0),this.#bi=this.#Ai.createElementNS(pt,"defs"),t.append(i),i.append(this.#bi),this.#Ai.body.append(t)}return this.#bi}#xi(t){if(1===t.length){const e=t[0],i=new Array(256);for(let t=0;t<256;t++)i[t]=e[t]/255;const s=i.join(",");return[s,s,s]}const[e,i,s]=t,n=new Array(256),a=new Array(256),r=new Array(256);for(let o=0;o<256;o++)n[o]=e[o]/255,a[o]=i[o]/255,r[o]=s[o]/255;return[n.join(","),a.join(","),r.join(",")]}#Si(t){if(void 0===this.#mi){this.#mi="";const t=this.#Ai.URL;t!==this.#Ai.baseURI&&(vt(t)?G():this.#mi=W(t,""))}return`url(${this.#mi}#${t})`}addFilter(t){if(!t)return"none";let e=this.#_.get(t);if(e)return e;const[i,s,n]=this.#xi(t),a=1===t.length?i:`${i}${s}${n}`;if(e=this.#_.get(a),e)return this.#_.set(t,e),e;const r=`g_${this.#vi}_transfer_map_${this.#w++}`,o=this.#Si(r);this.#_.set(t,o),this.#_.set(a,o);const l=this.#Ei(r);return this.#Ci(i,s,n,l),o}addHCMFilter(t,e){const i=`${t}-${e}`,s="base";let n=this.#yi.get(s);if(n?.key===i)return n.url;if(n?(n.filter?.remove(),n.key=i,n.url="none",n.filter=null):(n={key:i,url:"none",filter:null},this.#yi.set(s,n)),!t||!e)return n.url;const a=this.#Ti(t);t=at.makeHexColor(...a);const r=this.#Ti(e);if(e=at.makeHexColor(...r),this.#_i.style.color="","#000000"===t&&"#ffffff"===e||t===e)return n.url;const o=new Array(256);for(let u=0;u<=255;u++){const t=u/255;o[u]=t<=.03928?t/12.92:((t+.055)/1.055)**2.4}const l=o.join(","),h=`g_${this.#vi}_hcm_filter`,d=n.filter=this.#Ei(h);this.#Ci(l,l,l,d),this.#Mi(d);const c=(t,e)=>{const i=a[t]/255,s=r[t]/255,n=new Array(e+1);for(let a=0;a<=e;a++)n[a]=i+a/e*(s-i);return n.join(",")};return this.#Ci(c(0,5),c(1,5),c(2,5),d),n.url=this.#Si(h),n.url}addAlphaFilter(t){let e=this.#_.get(t);if(e)return e;const[i]=this.#xi([t]),s=`alpha_${i}`;if(e=this.#_.get(s),e)return this.#_.set(t,e),e;const n=`g_${this.#vi}_alpha_map_${this.#w++}`,a=this.#Si(n);this.#_.set(t,a),this.#_.set(s,a);const r=this.#Ei(n);return this.#Pi(i,r),a}addLuminosityFilter(t){let e,i,s=this.#_.get(t||"luminosity");if(s)return s;if(t?([e]=this.#xi([t]),i=`luminosity_${e}`):i="luminosity",s=this.#_.get(i),s)return this.#_.set(t,s),s;const n=`g_${this.#vi}_luminosity_map_${this.#w++}`,a=this.#Si(n);this.#_.set(t,a),this.#_.set(i,a);const r=this.#Ei(n);return this.#Ii(r),t&&this.#Pi(e,r),a}addHighlightHCMFilter(t,e,i,s,n){const a=`${e}-${i}-${s}-${n}`;let r=this.#yi.get(t);if(r?.key===a)return r.url;if(r?(r.filter?.remove(),r.key=a,r.url="none",r.filter=null):(r={key:a,url:"none",filter:null},this.#yi.set(t,r)),!e||!i)return r.url;const[o,l]=[e,i].map(this.#Ti.bind(this));let h=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),d=Math.round(.2126*l[0]+.7152*l[1]+.0722*l[2]),[c,u]=[s,n].map(this.#Ti.bind(this));d<h&&([h,d,c,u]=[d,h,u,c]),this.#_i.style.color="";const p=(t,e,i)=>{const s=new Array(256),n=(d-h)/i,a=t/255,r=(e-t)/(255*i);let o=0;for(let l=0;l<=i;l++){const t=Math.round(h+l*n),e=a+l*r;for(let i=o;i<=t;i++)s[i]=e;o=t+1}for(let l=o;l<256;l++)s[l]=s[o-1];return s.join(",")},g=`g_${this.#vi}_hcm_${t}_filter`,m=r.filter=this.#Ei(g);return this.#Mi(m),this.#Ci(p(c[0],u[0],5),p(c[1],u[1],5),p(c[2],u[2],5),m),r.url=this.#Si(g),r.url}destroy(t=!1){t&&this.#wi?.size||(this.#bi?.parentNode.parentNode.remove(),this.#bi=null,this.#fi?.clear(),this.#fi=null,this.#wi?.clear(),this.#wi=null,this.#w=0)}#Ii(t){const e=this.#Ai.createElementNS(pt,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),t.append(e)}#Mi(t){const e=this.#Ai.createElementNS(pt,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),t.append(e)}#Ei(t){const e=this.#Ai.createElementNS(pt,"filter");return e.setAttribute("color-interpolation-filters","sRGB"),e.setAttribute("id",t),this.#_i.append(e),e}#ki(t,e,i){const s=this.#Ai.createElementNS(pt,e);s.setAttribute("type","discrete"),s.setAttribute("tableValues",i),t.append(s)}#Ci(t,e,i,s){const n=this.#Ai.createElementNS(pt,"feComponentTransfer");s.append(n),this.#ki(n,"feFuncR",t),this.#ki(n,"feFuncG",e),this.#ki(n,"feFuncB",i)}#Pi(t,e){const i=this.#Ai.createElementNS(pt,"feComponentTransfer");e.append(i),this.#ki(i,"feFuncA",t)}#Ti(t){return this.#_i.style.color=t,Mt(getComputedStyle(this.#_i).getPropertyValue("color"))}}class Ee{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch((t=>{throw new Error(`Unable to load font data at: ${e}`)}))}async _fetch(t){$("Abstract method `_fetch` called.")}}class Ce extends Ee{async _fetch(t){const e=await mt(t,"arraybuffer");return new Uint8Array(e)}}class Te{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `wasmUrl` API parameter is provided.");if(!t)throw new Error("Wasm filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch((t=>{throw new Error(`Unable to load wasm data at: ${e}`)}))}async _fetch(t){$("Abstract method `_fetch` called.")}}class Me extends Te{async _fetch(t){const e=await mt(t,"arraybuffer");return new Uint8Array(e)}}async function Pe(t){const e=process.getBuiltinModule("fs"),i=await e.promises.readFile(t);return new Uint8Array(i)}s&&G();class Ie extends xe{}class ke extends Ae{_createCanvas(t,e){return process.getBuiltinModule("module").createRequire("file:///C:/Users/<USER>/Documents/CODEZ/Cognito-AI_Sidekick-main/node_modules/pdfjs-dist/build/pdf.mjs")("@napi-rs/canvas").createCanvas(t,e)}}class De extends ye{async _fetch(t){return Pe(t)}}class Re extends Ee{async _fetch(t){return Pe(t)}}class Le extends Te{async _fetch(t){return Pe(t)}}const Fe="Fill",Ne="Stroke",Oe="Shading";function Be(t,e){if(!e)return;const i=e[2]-e[0],s=e[3]-e[1],n=new Path2D;n.rect(e[0],e[1],i,s),t.clip(n)}class ze{isModifyingCurrentTransform(){return!1}getPattern(){$("Abstract method `getPattern` called.")}}class He extends ze{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const i of this._colorStops)e.addColorStop(i[0],i[1]);return e}getPattern(t,e,i,s){let n;if(s===Ne||s===Fe){const a=e.current.getClippedPathBoundingBox(s,Pt(t))||[0,0,0,0],r=Math.ceil(a[2]-a[0])||1,o=Math.ceil(a[3]-a[1])||1,l=e.cachedCanvases.getCanvas("pattern",r,o),h=l.context;h.clearRect(0,0,h.canvas.width,h.canvas.height),h.beginPath(),h.rect(0,0,h.canvas.width,h.canvas.height),h.translate(-a[0],-a[1]),i=at.transform(i,[1,0,0,1,a[0],a[1]]),h.transform(...e.baseTransform),this.matrix&&h.transform(...this.matrix),Be(h,this._bbox),h.fillStyle=this._createGradient(h),h.fill(),n=t.createPattern(l.canvas,"no-repeat");const d=new DOMMatrix(i);n.setTransform(d)}else Be(t,this._bbox),n=this._createGradient(t);return n}}function Ue(t,e,i,s,n,a,r,o){const l=e.coords,h=e.colors,d=t.data,c=4*t.width;let u;l[i+1]>l[s+1]&&(u=i,i=s,s=u,u=a,a=r,r=u),l[s+1]>l[n+1]&&(u=s,s=n,n=u,u=r,r=o,o=u),l[i+1]>l[s+1]&&(u=i,i=s,s=u,u=a,a=r,r=u);const p=(l[i]+e.offsetX)*e.scaleX,g=(l[i+1]+e.offsetY)*e.scaleY,m=(l[s]+e.offsetX)*e.scaleX,f=(l[s+1]+e.offsetY)*e.scaleY,b=(l[n]+e.offsetX)*e.scaleX,v=(l[n+1]+e.offsetY)*e.scaleY;if(g>=v)return;const A=h[a],w=h[a+1],y=h[a+2],_=h[r],x=h[r+1],S=h[r+2],E=h[o],C=h[o+1],T=h[o+2],M=Math.round(g),P=Math.round(v);let I,k,D,R,L,F,N,O;for(let B=M;B<=P;B++){if(B<f){const t=B<g?0:(g-B)/(g-f);I=p-(p-m)*t,k=A-(A-_)*t,D=w-(w-x)*t,R=y-(y-S)*t}else{let t;t=B>v?1:f===v?0:(f-B)/(f-v),I=m-(m-b)*t,k=_-(_-E)*t,D=x-(x-C)*t,R=S-(S-T)*t}let t;t=B<g?0:B>v?1:(g-B)/(g-v),L=p-(p-b)*t,F=A-(A-E)*t,N=w-(w-C)*t,O=y-(y-T)*t;const e=Math.round(Math.min(I,L)),i=Math.round(Math.max(I,L));let s=c*B+4*e;for(let n=e;n<=i;n++)t=(I-n)/(I-L),t<0?t=0:t>1&&(t=1),d[s++]=k-(k-F)*t|0,d[s++]=D-(D-N)*t|0,d[s++]=R-(R-O)*t|0,d[s++]=255}}function Ge(t,e,i){const s=e.coords,n=e.colors;let a,r;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(s.length/o)-1,h=o-1;for(a=0;a<l;a++){let e=a*o;for(let a=0;a<h;a++,e++)Ue(t,i,s[e],s[e+1],s[e+o],n[e],n[e+1],n[e+o]),Ue(t,i,s[e+o+1],s[e+1],s[e+o],n[e+o+1],n[e+1],n[e+o])}break;case"triangles":for(a=0,r=s.length;a<r;a+=3)Ue(t,i,s[a],s[a+1],s[a+2],n[a],n[a+1],n[a+2]);break;default:throw new Error("illegal figure")}}class $e extends ze{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[6],this._background=t[7],this.matrix=null}_createMeshCanvas(t,e,i){const s=Math.floor(this._bounds[0]),n=Math.floor(this._bounds[1]),a=Math.ceil(this._bounds[2])-s,r=Math.ceil(this._bounds[3])-n,o=Math.min(Math.ceil(Math.abs(a*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(r*t[1]*1.1)),3e3),h=a/o,d=r/l,c={coords:this._coords,colors:this._colors,offsetX:-s,offsetY:-n,scaleX:1/h,scaleY:1/d},u=o+4,p=l+4,g=i.getCanvas("mesh",u,p),m=g.context,f=m.createImageData(o,l);if(e){const t=f.data;for(let i=0,s=t.length;i<s;i+=4)t[i]=e[0],t[i+1]=e[1],t[i+2]=e[2],t[i+3]=255}for(const b of this._figures)Ge(f,b,c);m.putImageData(f,2,2);return{canvas:g.canvas,offsetX:s-2*h,offsetY:n-2*d,scaleX:h,scaleY:d}}isModifyingCurrentTransform(){return!0}getPattern(t,e,i,s){Be(t,this._bbox);const n=new Float32Array(2);if(s===Oe)at.singularValueDecompose2dScale(Pt(t),n);else if(this.matrix){at.singularValueDecompose2dScale(this.matrix,n);const[t,i]=n;at.singularValueDecompose2dScale(e.baseTransform,n),n[0]*=t,n[1]*=i}else at.singularValueDecompose2dScale(e.baseTransform,n);const a=this._createMeshCanvas(n,s===Oe?null:this._background,e.cachedCanvases);return s!==Oe&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(a.offsetX,a.offsetY),t.scale(a.scaleX,a.scaleY),t.createPattern(a.canvas,"no-repeat")}}class je extends ze{getPattern(){return"hotpink"}}const Ve=1,We=2;class qe{static MAX_PATTERN_SIZE=3e3;constructor(t,e,i,s){this.color=t[1],this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.ctx=e,this.canvasGraphicsFactory=i,this.baseTransform=s}createPatternCanvas(t){const{bbox:e,operatorList:i,paintType:s,tilingType:n,color:a,canvasGraphicsFactory:r}=this;let{xstep:o,ystep:l}=this;o=Math.abs(o),l=Math.abs(l),U();const h=e[0],d=e[1],c=e[2],u=e[3],p=c-h,g=u-d,m=new Float32Array(2);at.singularValueDecompose2dScale(this.matrix,m);const[f,b]=m;at.singularValueDecompose2dScale(this.baseTransform,m);const v=f*m[0],A=b*m[1];let w=p,y=g,_=!1,x=!1;const S=Math.ceil(o*v),E=Math.ceil(l*A);S>=Math.ceil(p*v)?w=o:_=!0,E>=Math.ceil(g*A)?y=l:x=!0;const C=this.getSizeAndScale(w,this.ctx.canvas.width,v),T=this.getSizeAndScale(y,this.ctx.canvas.height,A),M=t.cachedCanvases.getCanvas("pattern",C.size,T.size),P=M.context,I=r.createCanvasGraphics(P);if(I.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(I,s,a),P.translate(-C.scale*h,-T.scale*d),I.transform(C.scale,0,0,T.scale,0,0),P.save(),this.clipBbox(I,h,d,c,u),I.baseTransform=Pt(I.ctx),I.executeOperatorList(i),I.endDrawing(),P.restore(),_||x){const e=M.canvas;_&&(w=o),x&&(y=l);const i=this.getSizeAndScale(w,this.ctx.canvas.width,v),s=this.getSizeAndScale(y,this.ctx.canvas.height,A),n=i.size,a=s.size,r=t.cachedCanvases.getCanvas("pattern-workaround",n,a),c=r.context,u=_?Math.floor(p/o):0,m=x?Math.floor(g/l):0;for(let t=0;t<=u;t++)for(let i=0;i<=m;i++)c.drawImage(e,n*t,a*i,n,a,0,0,n,a);return{canvas:r.canvas,scaleX:i.scale,scaleY:s.scale,offsetX:h,offsetY:d}}return{canvas:M.canvas,scaleX:C.scale,scaleY:T.scale,offsetX:h,offsetY:d}}getSizeAndScale(t,e,i){const s=Math.max(qe.MAX_PATTERN_SIZE,e);let n=Math.ceil(t*i);return n>=s?n=s:i=n/t,{scale:i,size:n}}clipBbox(t,e,i,s,n){const a=s-e,r=n-i;t.ctx.rect(e,i,a,r),at.axialAlignedBoundingBox([e,i,s,n],Pt(t.ctx),t.current.minMax),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,i){const s=t.ctx,n=t.current;switch(e){case Ve:const{fillStyle:t,strokeStyle:a}=this.ctx;s.fillStyle=n.fillColor=t,s.strokeStyle=n.strokeColor=a;break;case We:s.fillStyle=s.strokeStyle=i,n.fillColor=n.strokeColor=i;break;default:throw new Z(`Unsupported paint type: ${e}`)}}isModifyingCurrentTransform(){return!1}getPattern(t,e,i,s){let n=i;s!==Oe&&(n=at.transform(n,e.baseTransform),this.matrix&&(n=at.transform(n,this.matrix)));const a=this.createPatternCanvas(e);let r=new DOMMatrix(n);r=r.translate(a.offsetX,a.offsetY),r=r.scale(1/a.scaleX,1/a.scaleY);const o=t.createPattern(a.canvas,"repeat");return o.setTransform(r),o}}function Xe({src:t,srcPos:e=0,dest:i,width:s,height:n,nonBlackColor:a=4294967295,inverseDecode:r=!1}){const o=st.isLittleEndian?4278190080:255,[l,h]=r?[a,o]:[o,a],d=s>>3,c=7&s,u=t.length;i=new Uint32Array(i.buffer);let p=0;for(let g=0;g<n;g++){for(const n=e+d;e<n;e++){const s=e<u?t[e]:255;i[p++]=128&s?h:l,i[p++]=64&s?h:l,i[p++]=32&s?h:l,i[p++]=16&s?h:l,i[p++]=8&s?h:l,i[p++]=4&s?h:l,i[p++]=2&s?h:l,i[p++]=1&s?h:l}if(0===c)continue;const s=e<u?t[e++]:255;for(let t=0;t<c;t++)i[p++]=s&1<<7-t?h:l}return{srcPos:e,destPos:p}}const Ke=16,Ye=new DOMMatrix,Qe=new Float32Array(2),Je=new Float32Array([1/0,1/0,-1/0,-1/0]);class Ze{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,i){let s;return void 0!==this.cache[t]?(s=this.cache[t],this.canvasFactory.reset(s,e,i)):(s=this.canvasFactory.create(e,i),this.cache[t]=s),s}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function ti(t,e,i,s,n,a,r,o,l,h){const[d,c,u,p,g,m]=Pt(t);if(0===c&&0===u){const f=r*d+g,b=Math.round(f),v=o*p+m,A=Math.round(v),w=(r+l)*d+g,y=Math.abs(Math.round(w)-b)||1,_=(o+h)*p+m,x=Math.abs(Math.round(_)-A)||1;return t.setTransform(Math.sign(d),0,0,Math.sign(p),b,A),t.drawImage(e,i,s,n,a,0,0,y,x),t.setTransform(d,c,u,p,g,m),[y,x]}if(0===d&&0===p){const f=o*u+g,b=Math.round(f),v=r*c+m,A=Math.round(v),w=(o+h)*u+g,y=Math.abs(Math.round(w)-b)||1,_=(r+l)*c+m,x=Math.abs(Math.round(_)-A)||1;return t.setTransform(0,Math.sign(c),Math.sign(u),0,b,A),t.drawImage(e,i,s,n,a,0,0,x,y),t.setTransform(d,c,u,p,g,m),[x,y]}t.drawImage(e,i,s,n,a,r,o,l,h);return[Math.hypot(d,c)*l,Math.hypot(u,p)*h]}class ei{alphaIsShape=!1;fontSize=0;fontSizeScale=1;textMatrix=null;textMatrixScale=1;fontMatrix=n;leading=0;x=0;y=0;lineX=0;lineY=0;charSpacing=0;wordSpacing=0;textHScale=1;textRenderingMode=v;textRise=0;fillColor="#000000";strokeColor="#000000";patternFill=!1;patternStroke=!1;fillAlpha=1;strokeAlpha=1;lineWidth=1;activeSMask=null;transferMaps="none";constructor(t,e){this.clipBox=new Float32Array([0,0,t,e]),this.minMax=Je.slice()}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t.minMax=this.minMax.slice(),t}getPathBoundingBox(t=Fe,e=null){const i=this.minMax.slice();if(t===Ne){e||$("Stroke bounding box must include transform."),at.singularValueDecompose2dScale(e,Qe);const t=Qe[0]*this.lineWidth/2,s=Qe[1]*this.lineWidth/2;i[0]-=t,i[1]-=s,i[2]+=t,i[3]+=s}return i}updateClipFromPath(){const t=at.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minMax[0]===1/0}startNewPathAndClipBox(t){this.clipBox.set(t,0),this.minMax.set(Je,0)}getClippedPathBoundingBox(t=Fe,e=null){return at.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function ii(t,e){if(e instanceof ImageData)return void t.putImageData(e,0,0);const i=e.height,s=e.width,n=i%Ke,a=(i-n)/Ke,r=0===n?a:a+1,o=t.createImageData(s,Ke);let l,h=0;const d=e.data,c=o.data;let u,p,g,m;if(e.kind===S.GRAYSCALE_1BPP){const e=d.byteLength,i=new Uint32Array(c.buffer,0,c.byteLength>>2),m=i.length,f=s+7>>3,b=4294967295,v=st.isLittleEndian?4278190080:255;for(u=0;u<r;u++){for(g=u<a?Ke:n,l=0,p=0;p<g;p++){const t=e-h;let n=0;const a=t>f?s:8*t-7,r=-8&a;let o=0,c=0;for(;n<r;n+=8)c=d[h++],i[l++]=128&c?b:v,i[l++]=64&c?b:v,i[l++]=32&c?b:v,i[l++]=16&c?b:v,i[l++]=8&c?b:v,i[l++]=4&c?b:v,i[l++]=2&c?b:v,i[l++]=1&c?b:v;for(;n<a;n++)0===o&&(c=d[h++],o=128),i[l++]=c&o?b:v,o>>=1}for(;l<m;)i[l++]=0;t.putImageData(o,0,u*Ke)}}else if(e.kind===S.RGBA_32BPP){for(p=0,m=s*Ke*4,u=0;u<a;u++)c.set(d.subarray(h,h+m)),h+=m,t.putImageData(o,0,p),p+=Ke;u<r&&(m=s*n*4,c.set(d.subarray(h,h+m)),t.putImageData(o,0,p))}else{if(e.kind!==S.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);for(g=Ke,m=s*g,u=0;u<r;u++){for(u>=a&&(g=n,m=s*g),l=0,p=m;p--;)c[l++]=d[h++],c[l++]=d[h++],c[l++]=d[h++],c[l++]=255;t.putImageData(o,0,u*Ke)}}}function si(t,e){if(e.bitmap)return void t.drawImage(e.bitmap,0,0);const i=e.height,s=e.width,n=i%Ke,a=(i-n)/Ke,r=0===n?a:a+1,o=t.createImageData(s,Ke);let l=0;const h=e.data,d=o.data;for(let c=0;c<r;c++){const e=c<a?Ke:n;({srcPos:l}=Xe({src:h,srcPos:l,dest:d,width:s,height:e,nonBlackColor:0})),t.putImageData(o,0,c*Ke)}}function ni(t,e){const i=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of i)void 0!==t[s]&&(e[s]=t[s]);void 0!==t.setLineDash&&(e.setLineDash(t.getLineDash()),e.lineDashOffset=t.lineDashOffset)}function ai(t){t.strokeStyle=t.fillStyle="#000000",t.fillRule="nonzero",t.globalAlpha=1,t.lineWidth=1,t.lineCap="butt",t.lineJoin="miter",t.miterLimit=10,t.globalCompositeOperation="source-over",t.font="10px sans-serif",void 0!==t.setLineDash&&(t.setLineDash([]),t.lineDashOffset=0);const{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}function ri(t,e){if(e)return!0;at.singularValueDecompose2dScale(t,Qe);const i=Math.fround(Dt.pixelRatio*gt.PDF_TO_CSS_UNITS);return Qe[0]<=i&&Qe[1]<=i}const oi=["butt","round","square"],li=["miter","round","bevel"],hi={},di={};class ci{constructor(t,e,i,s,n,{optionalContentConfig:a,markedContentStack:r=null},o,l){this.ctx=t,this.current=new ei(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=i,this.canvasFactory=s,this.filterFactory=n,this.groupStack=[],this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=r||[],this.optionalContentConfig=a,this.cachedCanvases=new Ze(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=o,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=l,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:i=!1,background:s=null}){const n=this.ctx.canvas.width,a=this.ctx.canvas.height,r=this.ctx.fillStyle;if(this.ctx.fillStyle=s||"#ffffff",this.ctx.fillRect(0,0,n,a),this.ctx.fillStyle=r,i){const t=this.cachedCanvases.getCanvas("transparent",n,a);this.compositeCtx=this.ctx,this.transparentCanvas=t.canvas,this.ctx=t.context,this.ctx.save(),this.ctx.transform(...Pt(this.compositeCtx))}this.ctx.save(),ai(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=Pt(this.ctx)}executeOperatorList(t,e,i,s){const n=t.argsArray,a=t.fnArray;let r=e||0;const o=n.length;if(o===r)return r;const l=o-r>10&&"function"==typeof i,h=l?Date.now()+15:0;let d=0;const c=this.commonObjs,u=this.objs;let p;for(;;){if(void 0!==s&&r===s.nextBreakPoint)return s.breakIt(r,i),r;if(p=a[r],p!==D.dependency)this[p].apply(this,n[r]);else for(const t of n[r]){const e=t.startsWith("g_")?c:u;if(!e.has(t))return e.get(t,i),r}if(r++,r===o)return r;if(l&&++d>10){if(Date.now()>h)return i(),r;d=0}}}#Di(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null,this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)}endDrawing(){this.#Di(),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),this.#Ri()}#Ri(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){const e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}}_scaleImage(t,e){const i=t.width??t.displayWidth,s=t.height??t.displayHeight;let n,a,r=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=i,h=s,d="prescale1";for(;r>2&&l>1||o>2&&h>1;){let e=l,i=h;r>2&&l>1&&(e=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2),r/=l/e),o>2&&h>1&&(i=h>=16384?Math.floor(h/2)-1||1:Math.ceil(h)/2,o/=h/i),n=this.cachedCanvases.getCanvas(d,e,i),a=n.context,a.clearRect(0,0,e,i),a.drawImage(t,0,0,l,h,0,0,e,i),t=n.canvas,l=e,h=i,d="prescale1"===d?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:h}}_createMaskCanvas(t){const e=this.ctx,{width:i,height:s}=t,n=this.current.fillColor,a=this.current.patternFill,r=Pt(e);let o,l,h,d;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer;l=JSON.stringify(a?r:[r.slice(0,4),n]),o=this._cachedBitmapsMap.get(e),o||(o=new Map,this._cachedBitmapsMap.set(e,o));const i=o.get(l);if(i&&!a){return{canvas:i,offsetX:Math.round(Math.min(r[0],r[2])+r[4]),offsetY:Math.round(Math.min(r[1],r[3])+r[5])}}h=i}h||(d=this.cachedCanvases.getCanvas("maskCanvas",i,s),si(d.context,t));let c=at.transform(r,[1/i,0,0,-1/s,0,0]);c=at.transform(c,[1,0,0,1,0,-s]);const u=Je.slice();at.axialAlignedBoundingBox([0,0,i,s],c,u);const[p,g,m,f]=u,b=Math.round(m-p)||1,v=Math.round(f-g)||1,A=this.cachedCanvases.getCanvas("fillCanvas",b,v),w=A.context,y=p,_=g;w.translate(-y,-_),w.transform(...c),h||(h=this._scaleImage(d.canvas,It(w)),h=h.img,o&&a&&o.set(l,h)),w.imageSmoothingEnabled=ri(Pt(w),t.interpolate),ti(w,h,0,0,h.width,h.height,0,0,i,s),w.globalCompositeOperation="source-in";const x=at.transform(It(w),[1,0,0,1,-y,-_]);return w.fillStyle=a?n.getPattern(e,this,x,Fe):n,w.fillRect(0,0,i,s),o&&!a&&(this.cachedCanvases.delete("fillCanvas"),o.set(l,A.canvas)),{canvas:A.canvas,offsetX:Math.round(y),offsetY:Math.round(_)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=oi[t]}setLineJoin(t){this.ctx.lineJoin=li[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const i=this.ctx;void 0!==i.setLineDash&&(i.setLineDash(t),i.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=i;break;case"ca":this.ctx.globalAlpha=this.current.fillAlpha=i;break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(i)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,i="smaskGroupAt"+this.groupLevel,s=this.cachedCanvases.getCanvas(i,t,e);this.suspendedCtx=this.ctx;const n=this.ctx=s.context;n.setTransform(this.suspendedCtx.getTransform()),ni(this.suspendedCtx,n),function(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save,t.__originalRestore=t.restore,t.__originalRotate=t.rotate,t.__originalScale=t.scale,t.__originalTranslate=t.translate,t.__originalTransform=t.transform,t.__originalSetTransform=t.setTransform,t.__originalResetTransform=t.resetTransform,t.__originalClip=t.clip,t.__originalMoveTo=t.moveTo,t.__originalLineTo=t.lineTo,t.__originalBezierCurveTo=t.bezierCurveTo,t.__originalRect=t.rect,t.__originalClosePath=t.closePath,t.__originalBeginPath=t.beginPath,t._removeMirroring=()=>{t.save=t.__originalSave,t.restore=t.__originalRestore,t.rotate=t.__originalRotate,t.scale=t.__originalScale,t.translate=t.__originalTranslate,t.transform=t.__originalTransform,t.setTransform=t.__originalSetTransform,t.resetTransform=t.__originalResetTransform,t.clip=t.__originalClip,t.moveTo=t.__originalMoveTo,t.lineTo=t.__originalLineTo,t.bezierCurveTo=t.__originalBezierCurveTo,t.rect=t.__originalRect,t.closePath=t.__originalClosePath,t.beginPath=t.__originalBeginPath,delete t._removeMirroring},t.save=function(){e.save(),this.__originalSave()},t.restore=function(){e.restore(),this.__originalRestore()},t.translate=function(t,i){e.translate(t,i),this.__originalTranslate(t,i)},t.scale=function(t,i){e.scale(t,i),this.__originalScale(t,i)},t.transform=function(t,i,s,n,a,r){e.transform(t,i,s,n,a,r),this.__originalTransform(t,i,s,n,a,r)},t.setTransform=function(t,i,s,n,a,r){e.setTransform(t,i,s,n,a,r),this.__originalSetTransform(t,i,s,n,a,r)},t.resetTransform=function(){e.resetTransform(),this.__originalResetTransform()},t.rotate=function(t){e.rotate(t),this.__originalRotate(t)},t.clip=function(t){e.clip(t),this.__originalClip(t)},t.moveTo=function(t,i){e.moveTo(t,i),this.__originalMoveTo(t,i)},t.lineTo=function(t,i){e.lineTo(t,i),this.__originalLineTo(t,i)},t.bezierCurveTo=function(t,i,s,n,a,r){e.bezierCurveTo(t,i,s,n,a,r),this.__originalBezierCurveTo(t,i,s,n,a,r)},t.rect=function(t,i,s,n){e.rect(t,i,s,n),this.__originalRect(t,i,s,n)},t.closePath=function(){e.closePath(),this.__originalClosePath()},t.beginPath=function(){e.beginPath(),this.__originalBeginPath()}}(n,this.suspendedCtx),this.setGState([["BM","source-over"]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),ni(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,i=this.suspendedCtx;this.composeSMask(i,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,i,s){const n=s[0],a=s[1],r=s[2]-n,o=s[3]-a;0!==r&&0!==o&&(this.genericComposeSMask(e.context,i,r,o,e.subtype,e.backdrop,e.transferMap,n,a,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(i.canvas,0,0),t.restore())}genericComposeSMask(t,e,i,s,n,a,r,o,l,h,d){let c=t.canvas,u=o-h,p=l-d;if(a)if(u<0||p<0||u+i>c.width||p+s>c.height){const t=this.cachedCanvases.getCanvas("maskExtension",i,s),e=t.context;e.drawImage(c,-u,-p),e.globalCompositeOperation="destination-atop",e.fillStyle=a,e.fillRect(0,0,i,s),e.globalCompositeOperation="source-over",c=t.canvas,u=p=0}else{t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);const e=new Path2D;e.rect(u,p,i,s),t.clip(e),t.globalCompositeOperation="destination-atop",t.fillStyle=a,t.fillRect(u,p,i,s),t.restore()}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),"Alpha"===n&&r?e.filter=this.filterFactory.addAlphaFilter(r):"Luminosity"===n&&(e.filter=this.filterFactory.addLuminosityFilter(r));const g=new Path2D;g.rect(o,l,i,s),e.clip(g),e.globalCompositeOperation="destination-in",e.drawImage(c,u,p,i,s,o,l,i,s),e.restore()}save(){this.inSMaskMode&&ni(this.ctx,this.suspendedCtx),this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){0!==this.stateStack.length?(this.current=this.stateStack.pop(),this.ctx.restore(),this.inSMaskMode&&ni(this.suspendedCtx,this.ctx),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null):this.inSMaskMode&&this.endSMaskMode()}transform(t,e,i,s,n,a){this.ctx.transform(t,e,i,s,n,a),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,i){let[s]=e;if(!i)return s||=e[0]=new Path2D,void this[t](s);if(!(s instanceof Path2D)){const t=e[0]=new Path2D;for(let e=0,i=s.length;e<i;)switch(s[e++]){case R:t.moveTo(s[e++],s[e++]);break;case L:t.lineTo(s[e++],s[e++]);break;case F:t.bezierCurveTo(s[e++],s[e++],s[e++],s[e++],s[e++],s[e++]);break;case N:t.closePath();break;default:G(s[e-1])}s=t}at.axialAlignedBoundingBox(i,Pt(this.ctx),this.current.minMax),this[t](s)}closePath(){this.ctx.closePath()}stroke(t,e=!0){const i=this.ctx,s=this.current.strokeColor;if(i.globalAlpha=this.current.strokeAlpha,this.contentVisible)if("object"==typeof s&&s?.getPattern){const e=s.isModifyingCurrentTransform()?i.getTransform():null;if(i.save(),i.strokeStyle=s.getPattern(i,this,It(i),Ne),e){const s=new Path2D;s.addPath(t,i.getTransform().invertSelf().multiplySelf(e)),t=s}this.rescaleAndStroke(t,!1),i.restore()}else this.rescaleAndStroke(t,!0);e&&this.consumePath(t,this.current.getClippedPathBoundingBox(Ne,Pt(this.ctx))),i.globalAlpha=this.current.fillAlpha}closeStroke(t){this.stroke(t)}fill(t,e=!0){const i=this.ctx,s=this.current.fillColor;let n=!1;if(this.current.patternFill){const e=s.isModifyingCurrentTransform()?i.getTransform():null;if(i.save(),i.fillStyle=s.getPattern(i,this,It(i),Fe),e){const s=new Path2D;s.addPath(t,i.getTransform().invertSelf().multiplySelf(e)),t=s}n=!0}const a=this.current.getClippedPathBoundingBox();this.contentVisible&&null!==a&&(this.pendingEOFill?(i.fill(t,"evenodd"),this.pendingEOFill=!1):i.fill(t)),n&&i.restore(),e&&this.consumePath(t,a)}eoFill(t){this.pendingEOFill=!0,this.fill(t)}fillStroke(t){this.fill(t,!1),this.stroke(t,!1),this.consumePath(t)}eoFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}closeFillStroke(t){this.fillStroke(t)}closeEOFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}endPath(t){this.consumePath(t)}rawFillPath(t){this.ctx.fill(t)}clip(){this.pendingClip=hi}eoClip(){this.pendingClip=di}beginText(){this.current.textMatrix=null,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0===t)return;const i=new Path2D,s=e.getTransform().invertSelf();for(const{transform:n,x:a,y:r,fontSize:o,path:l}of t)i.addPath(l,new DOMMatrix(n).preMultiplySelf(s).translate(a,r).scale(o,-o));e.clip(i),delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const i=this.commonObjs.get(t),s=this.current;if(!i)throw new Error(`Can't find font for ${t}`);if(s.fontMatrix=i.fontMatrix||n,0!==s.fontMatrix[0]&&0!==s.fontMatrix[3]||G(),e<0?(e=-e,s.fontDirection=-1):s.fontDirection=1,this.current.font=i,this.current.fontSize=e,i.isType3Font)return;const a=i.loadedName||"sans-serif",r=i.systemFontInfo?.css||`"${a}", ${i.fallbackName}`;let o="normal";i.black?o="900":i.bold&&(o="bold");const l=i.italic?"italic":"normal";let h=e;e<16?h=16:e>100&&(h=100),this.current.fontSizeScale=e/h,this.ctx.font=`${l} ${o} ${h}px ${r}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t){const{current:e}=this;e.textMatrix=t,e.textMatrixScale=Math.hypot(t[0],t[1]),e.x=e.lineX=0,e.y=e.lineY=0}nextLine(){this.moveText(0,this.current.leading)}#Li(t,e,i){const s=new Path2D;return s.addPath(t,new DOMMatrix(i).invertSelf().multiplySelf(e)),s}paintChar(t,e,i,s,n){const a=this.ctx,r=this.current,o=r.font,l=r.textRenderingMode,h=r.fontSize/r.fontSizeScale,d=l&_,c=!!(l&x),u=r.patternFill&&!o.missingFile,p=r.patternStroke&&!o.missingFile;let g;if((o.disableFontFace||c||u||p)&&(g=o.getPathGenerator(this.commonObjs,t)),o.disableFontFace||u||p){let t;if(a.save(),a.translate(e,i),a.scale(h,-h),d!==v&&d!==w||(s?(t=a.getTransform(),a.setTransform(...s),a.fill(this.#Li(g,t,s))):a.fill(g)),d===A||d===w)if(n){t||=a.getTransform(),a.setTransform(...n);const{a:e,b:i,c:s,d:r}=t,o=at.inverseTransform(n),l=at.transform([e,i,s,r,0,0],o);at.singularValueDecompose2dScale(l,Qe),a.lineWidth*=Math.max(Qe[0],Qe[1])/h,a.stroke(this.#Li(g,t,n))}else a.lineWidth/=h,a.stroke(g);a.restore()}else d!==v&&d!==w||a.fillText(t,e,i),d!==A&&d!==w||a.strokeText(t,e,i);if(c){(this.pendingTextPaths||=[]).push({transform:Pt(a),x:e,y:i,fontSize:h,path:g})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let i=!1;for(let s=3;s<e.length;s+=4)if(e[s]>0&&e[s]<255){i=!0;break}return q(this,"isFontSubpixelAAEnabled",i)}showText(t){const e=this.current,i=e.font;if(i.isType3Font)return this.showType3Text(t);const s=e.fontSize;if(0===s)return;const n=this.ctx,a=e.fontSizeScale,r=e.charSpacing,o=e.wordSpacing,l=e.fontDirection,h=e.textHScale*l,d=t.length,c=i.vertical,u=c?1:-1,p=i.defaultVMetrics,g=s*e.fontMatrix[0],m=e.textRenderingMode===v&&!i.disableFontFace&&!e.patternFill;let f,b;if(n.save(),e.textMatrix&&n.transform(...e.textMatrix),n.translate(e.x,e.y+e.textRise),l>0?n.scale(h,-1):n.scale(h,1),e.patternFill){n.save();const t=e.fillColor.getPattern(n,this,It(n),Fe);f=Pt(n),n.restore(),n.fillStyle=t}if(e.patternStroke){n.save();const t=e.strokeColor.getPattern(n,this,It(n),Ne);b=Pt(n),n.restore(),n.strokeStyle=t}let y=e.lineWidth;const x=e.textMatrixScale;if(0===x||0===y){const t=e.textRenderingMode&_;t!==A&&t!==w||(y=this.getSinglePixelWidth())}else y/=x;if(1!==a&&(n.scale(a,a),y/=a),n.lineWidth=y,i.isInvalidPDFjsFont){const i=[];let s=0;for(const e of t)i.push(e.unicode),s+=e.width;return n.fillText(i.join(""),0,0),e.x+=s*g*h,n.restore(),void this.compose()}let S,E=0;for(S=0;S<d;++S){const e=t[S];if("number"==typeof e){E+=u*e*s/1e3;continue}let h=!1;const d=(e.isSpace?o:0)+r,v=e.fontChar,A=e.accent;let w,y,_=e.width;if(c){const t=e.vmetric||p,i=-(e.vmetric?t[1]:.5*_)*g,s=t[2]*g;_=t?-t[0]:_,w=i/a,y=(E+s)/a}else w=E/a,y=0;if(i.remeasure&&_>0){const t=1e3*n.measureText(v).width/s*a;if(_<t&&this.isFontSubpixelAAEnabled){const e=_/t;h=!0,n.save(),n.scale(e,1),w/=e}else _!==t&&(w+=(_-t)/2e3*s/a)}if(this.contentVisible&&(e.isInFont||i.missingFile))if(m&&!A)n.fillText(v,w,y);else if(this.paintChar(v,w,y,f,b),A){const t=w+s*A.offset.x/a,e=y-s*A.offset.y/a;this.paintChar(A.fontChar,t,e,f,b)}E+=c?_*g-d*l:_*g+d*l,h&&n.restore()}c?e.y-=E:e.x+=E*h,n.restore(),this.compose()}showType3Text(t){const e=this.ctx,i=this.current,s=i.font,a=i.fontSize,r=i.fontDirection,o=s.vertical?1:-1,l=i.charSpacing,h=i.wordSpacing,d=i.textHScale*r,c=i.fontMatrix||n,u=t.length;let p,g,m,f;if(!(i.textRenderingMode===y)&&0!==a){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,e.save(),i.textMatrix&&e.transform(...i.textMatrix),e.translate(i.x,i.y+i.textRise),e.scale(d,r),p=0;p<u;++p){if(g=t[p],"number"==typeof g){f=o*g*a/1e3,this.ctx.translate(f,0),i.x+=f*d;continue}const n=(g.isSpace?h:0)+l,r=s.charProcOperatorList[g.operatorListId];r?this.contentVisible&&(this.save(),e.scale(a,a),e.transform(...c),this.executeOperatorList(r),this.restore()):G(g.operatorListId);const u=[g.width,0];at.applyTransform(u,c),m=u[0]*a+n,e.translate(m,0),i.x+=m*d}e.restore()}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,i,s,n,a){const r=new Path2D;r.rect(i,s,n-i,a-s),this.ctx.clip(r),this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const i=this.baseTransform||Pt(this.ctx),s={createCanvasGraphics:t=>new ci(t,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new qe(t,this.ctx,s,i)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments),this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t){this.ctx.strokeStyle=this.current.strokeColor=t,this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent",this.current.patternStroke=!1}setFillRGBColor(t){this.ctx.fillStyle=this.current.fillColor=t,this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent",this.current.patternFill=!1}_getPattern(t,e=null){let i;return this.cachedPatterns.has(t)?i=this.cachedPatterns.get(t):(i=function(t){switch(t[0]){case"RadialAxial":return new He(t);case"Mesh":return new $e(t);case"Dummy":return new je}throw new Error(`Unknown IR type: ${t[0]}`)}(this.getObject(t)),this.cachedPatterns.set(t,i)),e&&(i.matrix=e),i}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const i=this._getPattern(t);e.fillStyle=i.getPattern(e,this,It(e),Oe);const s=It(e);if(s){const{width:t,height:i}=e.canvas,n=Je.slice();at.axialAlignedBoundingBox([0,0,t,i],s,n);const[a,r,o,l]=n;this.ctx.fillRect(a,r,o-a,l-r)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){$("Should not call beginInlineImage")}beginImageData(){$("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=Pt(this.ctx),e)){at.axialAlignedBoundingBox(e,this.baseTransform,this.current.minMax);const[t,i,s,n]=e,a=new Path2D;a.rect(t,i,s-t,n-i),this.ctx.clip(a),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||U(),t.knockout&&G();const i=Pt(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw new Error("Bounding box is required.");let s=Je.slice();at.axialAlignedBoundingBox(t.bbox,Pt(e),s);const n=[0,0,e.canvas.width,e.canvas.height];s=at.intersect(s,n)||[0,0,0,0];const a=Math.floor(s[0]),r=Math.floor(s[1]),o=Math.max(Math.ceil(s[2])-a,1),l=Math.max(Math.ceil(s[3])-r,1);this.current.startNewPathAndClipBox([0,0,o,l]);let h="groupAt"+this.groupLevel;t.smask&&(h+="_smask_"+this.smaskCounter++%2);const d=this.cachedCanvases.getCanvas(h,o,l),c=d.context;c.translate(-a,-r),c.transform(...i);let u=new Path2D;const[p,g,m,f]=t.bbox;if(u.rect(p,g,m-p,f-g),t.matrix){const e=new Path2D;e.addPath(u,new DOMMatrix(t.matrix)),u=e}c.clip(u),t.smask?this.smaskStack.push({canvas:d.canvas,context:c,offsetX:a,offsetY:r,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(a,r),e.save()),ni(e,c),this.ctx=c,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,i=this.groupStack.pop();if(this.ctx=i,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const t=Pt(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...t);const i=Je.slice();at.axialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t,i),this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(i)}}beginAnnotation(t,e,i,s,n){if(this.#Di(),ai(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){const s=e[2]-e[0],a=e[3]-e[1];if(n&&this.annotationCanvasMap){(i=i.slice())[4]-=e[0],i[5]-=e[1],(e=e.slice())[0]=e[1]=0,e[2]=s,e[3]=a,at.singularValueDecompose2dScale(Pt(this.ctx),Qe);const{viewportScale:n}=this,r=Math.ceil(s*this.outputScaleX*n),o=Math.ceil(a*this.outputScaleY*n);this.annotationCanvas=this.canvasFactory.create(r,o);const{canvas:l,context:h}=this.annotationCanvas;this.annotationCanvasMap.set(t,l),this.annotationCanvas.savedCtx=this.ctx,this.ctx=h,this.ctx.save(),this.ctx.setTransform(Qe[0],0,0,-Qe[1],0,a*Qe[1]),ai(this.ctx)}else{ai(this.ctx),this.endPath();const t=new Path2D;t.rect(e[0],e[1],s,a),this.ctx.clip(t)}}this.current=new ei(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...i),this.transform(...s)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),this.#Ri(),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const i=this.ctx,s=this._createMaskCanvas(t),n=s.canvas;i.save(),i.setTransform(1,0,0,1,0,0),i.drawImage(n,s.offsetX,s.offsetY),i.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,i=0,s=0,n,a){if(!this.contentVisible)return;t=this.getObject(t.data,t);const r=this.ctx;r.save();const o=Pt(r);r.transform(e,i,s,n,0,0);const l=this._createMaskCanvas(t);r.setTransform(1,0,0,1,l.offsetX-o[4],l.offsetY-o[5]);for(let h=0,d=a.length;h<d;h+=2){const t=at.transform(o,[e,i,s,n,a[h],a[h+1]]);r.drawImage(l.canvas,t[4],t[5])}r.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,i=this.current.fillColor,s=this.current.patternFill;for(const n of t){const{data:t,width:a,height:r,transform:o}=n,l=this.cachedCanvases.getCanvas("maskCanvas",a,r),h=l.context;h.save();si(h,this.getObject(t,n)),h.globalCompositeOperation="source-in",h.fillStyle=s?i.getPattern(h,this,It(e),Fe):i,h.fillRect(0,0,a,r),h.restore(),e.save(),e.transform(...o),e.scale(1,-1),ti(e,l.canvas,0,0,a,r,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):G()}paintImageXObjectRepeat(t,e,i,s){if(!this.contentVisible)return;const n=this.getObject(t);if(!n)return void G();const a=n.width,r=n.height,o=[];for(let l=0,h=s.length;l<h;l+=2)o.push({transform:[e,0,0,i,s[l],s[l+1]],x:0,y:0,w:a,h:r});this.paintInlineImageXObjectGroup(n,o)}applyTransferMapsToCanvas(t){return"none"!==this.current.transferMaps&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;const{bitmap:e,width:i,height:s}=t,n=this.cachedCanvases.getCanvas("inlineImage",i,s),a=n.context;return a.filter=this.current.transferMaps,a.drawImage(e,0,0),a.filter="none",n.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,i=t.height,s=this.ctx;this.save();const{filter:n}=s;let a;if("none"!==n&&""!==n&&(s.filter="none"),s.scale(1/e,-1/i),t.bitmap)a=this.applyTransferMapsToBitmap(t);else if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)a=t;else{const s=this.cachedCanvases.getCanvas("inlineImage",e,i).context;ii(s,t),a=this.applyTransferMapsToCanvas(s)}const r=this._scaleImage(a,It(s));s.imageSmoothingEnabled=ri(Pt(s),t.interpolate),ti(s,r.img,0,0,r.paintWidth,r.paintHeight,0,-i,e,i),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const i=this.ctx;let s;if(t.bitmap)s=t.bitmap;else{const e=t.width,i=t.height,n=this.cachedCanvases.getCanvas("inlineImage",e,i).context;ii(n,t),s=this.applyTransferMapsToCanvas(n)}for(const n of e)i.save(),i.transform(...n.transform),i.scale(1,-1),ti(i,s,n.x,n.y,n.w,n.h,0,-1,1,1),i.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t,e){const i=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(e);const s=this.ctx;this.pendingClip&&(i||(this.pendingClip===di?s.clip(t,"evenodd"):s.clip(t)),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox)}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=Pt(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),i=Math.hypot(t[0],t[2]),s=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(i,s)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){const{lineWidth:t}=this.current,{a:e,b:i,c:s,d:n}=this.ctx.getTransform();let a,r;if(0===i&&0===s){const i=Math.abs(e),s=Math.abs(n);if(i===s)if(0===t)a=r=1/i;else{const e=i*t;a=r=e<1?1/e:1}else if(0===t)a=1/i,r=1/s;else{const e=i*t,n=s*t;a=e<1?1/e:1,r=n<1?1/n:1}}else{const o=Math.abs(e*n-i*s),l=Math.hypot(e,i),h=Math.hypot(s,n);if(0===t)a=h/o,r=l/o;else{const e=t*o;a=h>e?h/e:1,r=l>e?l/e:1}}this._cachedScaleForStroking[0]=a,this._cachedScaleForStroking[1]=r}return this._cachedScaleForStroking}rescaleAndStroke(t,e){const{ctx:i,current:{lineWidth:s}}=this,[n,a]=this.getScaleForStroking();if(n===a)return i.lineWidth=(s||1)*n,void i.stroke(t);const r=i.getLineDash();e&&i.save(),i.scale(n,a),Ye.a=1/n,Ye.d=1/a;const o=new Path2D;if(o.addPath(t,Ye),r.length>0){const t=Math.max(n,a);i.setLineDash(r.map((e=>e/t))),i.lineDashOffset/=t}i.lineWidth=s||1,i.stroke(o),e&&i.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}for(const ln in D)void 0!==ci.prototype[ln]&&(ci.prototype[D[ln]]=ci.prototype[ln]);class ui{static#Fi=null;static#Ni="";static get workerPort(){return this.#Fi}static set workerPort(t){if(!("undefined"!=typeof Worker&&t instanceof Worker)&&null!==t)throw new Error("Invalid `workerPort` type.");this.#Fi=t}static get workerSrc(){return this.#Ni}static set workerSrc(t){if("string"!=typeof t)throw new Error("Invalid `workerSrc` type.");this.#Ni=t}}class pi{#Oi;#Bi;constructor({parsedData:t,rawData:e}){this.#Oi=t,this.#Bi=e}getRaw(){return this.#Bi}get(t){return this.#Oi.get(t)??null}[Symbol.iterator](){return this.#Oi.entries()}}const gi=Symbol("INTERNAL");class mi{#zi=!1;#Hi=!1;#Ui=!1;#Gi=!0;constructor(t,{name:e,intent:i,usage:s,rbGroups:n}){this.#zi=!!(t&o),this.#Hi=!!(t&l),this.name=e,this.intent=i,this.usage=s,this.rbGroups=n}get visible(){if(this.#Ui)return this.#Gi;if(!this.#Gi)return!1;const{print:t,view:e}=this.usage;return this.#zi?"OFF"!==e?.viewState:!this.#Hi||"OFF"!==t?.printState}_setVisible(t,e,i=!1){t!==gi&&$("Internal method `_setVisible` called."),this.#Ui=i,this.#Gi=e}}class fi{#$i=null;#ji=new Map;#Vi=null;#Wi=null;constructor(t,e=o){if(this.renderingIntent=e,this.name=null,this.creator=null,null!==t){this.name=t.name,this.creator=t.creator,this.#Wi=t.order;for(const i of t.groups)this.#ji.set(i.id,new mi(e,i));if("OFF"===t.baseState)for(const t of this.#ji.values())t._setVisible(gi,!1);for(const e of t.on)this.#ji.get(e)._setVisible(gi,!0);for(const e of t.off)this.#ji.get(e)._setVisible(gi,!1);this.#Vi=this.getHash()}}#qi(t){const e=t.length;if(e<2)return!0;const i=t[0];for(let s=1;s<e;s++){const e=t[s];let n;if(Array.isArray(e))n=this.#qi(e);else{if(!this.#ji.has(e))return G(),!0;n=this.#ji.get(e).visible}switch(i){case"And":if(!n)return!1;break;case"Or":if(n)return!0;break;case"Not":return!n;default:return!0}}return"And"===i}isVisible(t){if(0===this.#ji.size)return!0;if(!t)return U(),!0;if("OCG"===t.type)return this.#ji.has(t.id)?this.#ji.get(t.id).visible:(G(t.id),!0);if("OCMD"===t.type){if(t.expression)return this.#qi(t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!this.#ji.has(e))return G(),!0;if(this.#ji.get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!this.#ji.has(e))return G(),!0;if(!this.#ji.get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!this.#ji.has(e))return G(),!0;if(!this.#ji.get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!this.#ji.has(e))return G(),!0;if(this.#ji.get(e).visible)return!1}return!0}return G(t.policy),!0}return G(t.type),!0}setVisibility(t,e=!0,i=!0){const s=this.#ji.get(t);if(s){if(i&&e&&s.rbGroups.length)for(const e of s.rbGroups)for(const i of e)i!==t&&this.#ji.get(i)?._setVisible(gi,!1,!0);s._setVisible(gi,!!e,!0),this.#$i=null}else G()}setOCGState({state:t,preserveRB:e}){let i;for(const s of t){switch(s){case"ON":case"OFF":case"Toggle":i=s;continue}const t=this.#ji.get(s);if(t)switch(i){case"ON":this.setVisibility(s,!0,e);break;case"OFF":this.setVisibility(s,!1,e);break;case"Toggle":this.setVisibility(s,!t.visible,e)}}this.#$i=null}get hasInitialVisibility(){return null===this.#Vi||this.getHash()===this.#Vi}getOrder(){return this.#ji.size?this.#Wi?this.#Wi.slice():[...this.#ji.keys()]:null}getGroup(t){return this.#ji.get(t)||null}getHash(){if(null!==this.#$i)return this.#$i;const t=new Yt;for(const[e,i]of this.#ji)t.update(`${e}:${i.visible}`);return this.#$i=t.hexdigest()}[Symbol.iterator](){return this.#ji.entries()}}class bi{constructor(t,{disableRange:e=!1,disableStream:i=!1}){j(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:s,initialData:n,progressiveDone:a,contentDispositionFilename:r}=t;if(this._queuedChunks=[],this._progressiveDone=a,this._contentDispositionFilename=r,n?.length>0){const t=n instanceof Uint8Array&&n.byteLength===n.buffer.byteLength?n.buffer:new Uint8Array(n).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!i,this._isRangeSupported=!e,this._contentLength=s,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})})),t.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})})),t.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})})),t.addProgressiveDoneListener((()=>{this._onProgressiveDone()})),t.transportReady()}_onReceiveData({begin:t,chunk:e}){const i=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(void 0===t)this._fullRequestReader?this._fullRequestReader._enqueue(i):this._queuedChunks.push(i);else{j(this._rangeReaders.some((function(e){return e._begin===t&&(e._enqueue(i),!0)})),"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){void 0===t.total?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){j(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new vi(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new Ai(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}class vi{constructor(t,e,i=!1,s=null){this._stream=t,this._done=i||!1,this._filename=At(s)?s:null,this._queuedChunks=e||[],this._loaded=0;for(const n of this._queuedChunks)this._loaded+=n.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class Ai{constructor(t,e,i){this._stream=t,this._begin=e,this._end=i,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function wi(t,e){const i=new Headers;if(!t||!e||"object"!=typeof e)return i;for(const s in e){const t=e[s];void 0!==t&&i.append(s,t)}return i}function yi(t){return URL.parse(t)?.origin??null}function _i({responseHeaders:t,isHttp:e,rangeChunkSize:i,disableRange:s}){const n={allowRangeRequests:!1,suggestedLength:void 0},a=parseInt(t.get("Content-Length"),10);if(!Number.isInteger(a))return n;if(n.suggestedLength=a,a<=2*i)return n;if(s||!e)return n;if("bytes"!==t.get("Accept-Ranges"))return n;return"identity"!==(t.get("Content-Encoding")||"identity")||(n.allowRangeRequests=!0),n}function xi(t){const e=t.get("Content-Disposition");if(e){let t=function(t){let e=!0,i=s("filename\\*","i").exec(t);if(i){i=i[1];let t=r(i);return t=unescape(t),t=o(t),t=l(t),a(t)}if(i=function(t){const e=[];let i;const n=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(i=n.exec(t));){let[,t,s,n]=i;if(t=parseInt(t,10),t in e){if(0===t)break}else e[t]=[s,n]}const a=[];for(let s=0;s<e.length&&s in e;++s){let[t,i]=e[s];i=r(i),t&&(i=unescape(i),0===s&&(i=o(i))),a.push(i)}return a.join("")}(t),i)return a(l(i));if(i=s("filename","i").exec(t),i){i=i[1];let t=r(i);return t=l(t),a(t)}function s(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function n(t,i){if(t){if(!/^[\x00-\xFF]+$/.test(i))return i;try{const s=new TextDecoder(t,{fatal:!0}),n=it(i);i=s.decode(n),e=!1}catch{}}return i}function a(t){return e&&/[\x80-\xff]/.test(t)&&(t=n("utf-8",t),e&&(t=n("iso-8859-1",t))),t}function r(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const i=e[t].indexOf('"');-1!==i&&(e[t]=e[t].slice(0,i),e.length=t+1),e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function o(t){const e=t.indexOf("'");return-1===e?t:n(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function l(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,i,s){if("q"===i||"Q"===i)return n(e,s=(s=s.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})));try{s=atob(s)}catch{}return n(e,s)}))}return""}(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if(At(t))return t}return null}function Si(t,e){return new J(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t,404===t||0===t&&e.startsWith("file:"))}function Ei(t){return 200===t||206===t}function Ci(t,e,i){return{method:"GET",headers:t,signal:i.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function Ti(t){return t instanceof Uint8Array?t.buffer:t instanceof ArrayBuffer?t:(G(),new Uint8Array(t).buffer)}class Mi{_responseOrigin=null;constructor(t){this.source=t,this.isHttp=/^https?:/i.test(t.url),this.headers=wi(this.isHttp,t.httpHeaders),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return j(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new Pi(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new Ii(this,t,e);return this._rangeRequestReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class Pi{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange;const i=new Headers(t.headers),s=e.url;fetch(s,Ci(i,this._withCredentials,this._abortController)).then((e=>{if(t._responseOrigin=yi(e.url),!Ei(e.status))throw Si(e.status,s);this._reader=e.body.getReader(),this._headersCapability.resolve();const i=e.headers,{allowRangeRequests:n,suggestedLength:a}=_i({responseHeaders:i,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=n,this._contentLength=a||this._contentLength,this._filename=xi(i),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new tt("Streaming is disabled."))})).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:Ti(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class Ii{constructor(t,e,i){this._stream=t,this._reader=null,this._loaded=0;const s=t.source;this._withCredentials=s.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!s.disableStream,this._abortController=new AbortController;const n=new Headers(t.headers);n.append("Range",`bytes=${e}-${i-1}`);const a=s.url;fetch(a,Ci(n,this._withCredentials,this._abortController)).then((e=>{const i=yi(e.url);if(i!==t._responseOrigin)throw new Error(`Expected range response-origin "${i}" to match "${t._responseOrigin}".`);if(!Ei(e.status))throw Si(e.status,a);this._readCapability.resolve(),this._reader=e.body.getReader()})).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded}),{value:Ti(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class ki{_responseOrigin=null;constructor({url:t,httpHeaders:e,withCredentials:i}){this.url=t,this.isHttp=/^https?:/i.test(t),this.headers=wi(this.isHttp,e),this.withCredentials=i||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}request(t){const e=new XMLHttpRequest,i=this.currXhrId++,s=this.pendingRequests[i]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const[n,a]of this.headers)e.setRequestHeader(n,a);return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),s.expectedStatus=206):s.expectedStatus=200,e.responseType="arraybuffer",j(t.onError,"Expected `onError` callback to be provided."),e.onerror=()=>{t.onError(e.status)},e.onreadystatechange=this.onStateChange.bind(this,i),e.onprogress=this.onProgress.bind(this,i),s.onHeadersReceived=t.onHeadersReceived,s.onDone=t.onDone,s.onError=t.onError,s.onProgress=t.onProgress,e.send(null),i}onProgress(t,e){const i=this.pendingRequests[t];i&&i.onProgress?.(e)}onStateChange(t,e){const i=this.pendingRequests[t];if(!i)return;const s=i.xhr;if(s.readyState>=2&&i.onHeadersReceived&&(i.onHeadersReceived(),delete i.onHeadersReceived),4!==s.readyState)return;if(!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],0===s.status&&this.isHttp)return void i.onError(s.status);const n=s.status||200;if(!(200===n&&206===i.expectedStatus)&&n!==i.expectedStatus)return void i.onError(s.status);const a=function(t){const e=t.response;return"string"!=typeof e?e:it(e).buffer}(s);if(206===n){const t=s.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);e?i.onDone({begin:parseInt(e[1],10),chunk:a}):(G(),i.onError(0))}else a?i.onDone({begin:0,chunk:a}):i.onError(s.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class Di{constructor(t){this._source=t,this._manager=new ki(t),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return j(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new Ri(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const i=new Li(this._manager,t,e);return i.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class Ri{constructor(t,e){this._manager=t,this._url=e.url,this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=yi(e.responseURL);const i=e.getAllResponseHeaders(),s=new Headers(i?i.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map((t=>{const[e,...i]=t.split(": ");return[e,i.join(": ")]})):[]),{allowRangeRequests:n,suggestedLength:a}=_i({responseHeaders:s,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});n&&(this._isRangeSupported=!0),this._contentLength=a||this._contentLength,this._filename=xi(s),this._isRangeSupported&&this._manager.abortRequest(t),this._headersCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){this._requests.shift().resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);if(this._done=!0,!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=Si(t,this._url),this._headersCapability.reject(this._storedError);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}async read(){if(await this._headersCapability.promise,this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0,this._headersCapability.reject(t);for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class Li{constructor(t,e,i){this._manager=t,this._url=t.url,this._requestId=t.request({begin:e,end:i,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_onHeadersReceived(){const t=yi(this._manager.getRequestXhr(this._requestId)?.responseURL);t!==this._manager._responseOrigin&&(this._storedError=new Error(`Expected range response-origin "${t}" to match "${this._manager._responseOrigin}".`),this._onError(0))}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const i of this._requests)i.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){this._storedError??=Si(t,this._url);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}const Fi=/^[a-z][a-z0-9\-+.]+:/i;class Ni{constructor(t){this.source=t,this.url=function(t){if(Fi.test(t))return new URL(t);const e=process.getBuiltinModule("url");return new URL(e.pathToFileURL(t))}(t.url),j("file:"===this.url.protocol,"PDFNodeStream only supports file:// URLs."),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return j(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=new Oi(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new Bi(this,t,e);return this._rangeRequestReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class Oi{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers();const i=process.getBuiltinModule("fs");i.promises.lstat(this._url).then((t=>{this._contentLength=t.size,this._setReadableStream(i.createReadStream(this._url)),this._headersCapability.resolve()}),(t=>{"ENOENT"===t.code&&(t=Si(0,this._url.href)),this._storedError=t,this._headersCapability.reject(t)}))}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t)return this._readCapability=Promise.withResolvers(),this.read();this._loaded+=t.length,this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",(()=>{this._readCapability.resolve()})),t.on("end",(()=>{t.destroy(),this._done=!0,this._readCapability.resolve()})),t.on("error",(t=>{this._error(t)})),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new tt("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class Bi{constructor(t,e,i){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();const s=t.source;this._isStreamingSupported=!s.disableStream;const n=process.getBuiltinModule("fs");this._setReadableStream(n.createReadStream(this._url,{start:e,end:i-1}))}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t)return this._readCapability=Promise.withResolvers(),this.read();this._loaded+=t.length,this.onProgress?.({loaded:this._loaded});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",(()=>{this._readCapability.resolve()})),t.on("end",(()=>{t.destroy(),this._done=!0,this._readCapability.resolve()})),t.on("error",(t=>{this._error(t)})),this._storedError&&this._readableStream.destroy(this._storedError)}}const zi=Symbol("INITIAL_DATA");class Hi{#Xi=Object.create(null);#Ki(t){return this.#Xi[t]||={...Promise.withResolvers(),data:zi}}get(t,e=null){if(e){const i=this.#Ki(t);return i.promise.then((()=>e(i.data))),null}const i=this.#Xi[t];if(!i||i.data===zi)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return i.data}has(t){const e=this.#Xi[t];return!!e&&e.data!==zi}delete(t){const e=this.#Xi[t];return!(!e||e.data===zi)&&(delete this.#Xi[t],!0)}resolve(t,e=null){const i=this.#Ki(t);i.data=e,i.resolve()}clear(){for(const t in this.#Xi){const{data:e}=this.#Xi[t];e?.bitmap?.close()}this.#Xi=Object.create(null)}*[Symbol.iterator](){for(const t in this.#Xi){const{data:e}=this.#Xi[t];e!==zi&&(yield[t,e])}}}class Ui{#Yi=Promise.withResolvers();#ft=null;#Qi=!1;#Ji=!!globalThis.FontInspector?.enabled;#Zi=null;#ts=null;#es=0;#is=0;#ss=null;#ns=null;#as=0;#rs=0;#os=Object.create(null);#ls=[];#hs=null;#ds=[];#cs=new WeakMap;#us=null;static#ps=new Map;static#gs=new Map;static#ms=new WeakMap;static#fs=null;static#bs=new Set;constructor({textContentSource:t,container:e,viewport:i}){if(t instanceof ReadableStream)this.#hs=t;else{if("object"!=typeof t)throw new Error('No "textContentSource" parameter specified.');this.#hs=new ReadableStream({start(e){e.enqueue(t),e.close()}})}this.#ft=this.#ns=e,this.#rs=i.scale*Dt.pixelRatio,this.#as=i.rotation,this.#ts={div:null,properties:null,ctx:null};const{pageWidth:s,pageHeight:n,pageX:a,pageY:r}=i.rawDims;this.#us=[1,0,0,-1,-a,r+n],this.#is=s,this.#es=n,Ui.#vs(),kt(e,i),this.#Yi.promise.finally((()=>{Ui.#bs.delete(this),this.#ts=null,this.#os=null})).catch((()=>{}))}static get fontFamilyMap(){const{isWindows:t,isFirefox:e}=st.platform;return q(this,"fontFamilyMap",new Map([["sans-serif",(t&&e?"Calibri, ":"")+"sans-serif"],["monospace",(t&&e?"Lucida Console, ":"")+"monospace"]]))}render(){const t=()=>{this.#ss.read().then((({value:e,done:i})=>{i?this.#Yi.resolve():(this.#Zi??=e.lang,Object.assign(this.#os,e.styles),this.#As(e.items),t())}),this.#Yi.reject)};return this.#ss=this.#hs.getReader(),Ui.#bs.add(this),t(),this.#Yi.promise}update({viewport:t,onBefore:e=null}){const i=t.scale*Dt.pixelRatio,s=t.rotation;if(s!==this.#as&&(e?.(),this.#as=s,kt(this.#ns,{rotation:s})),i!==this.#rs){e?.(),this.#rs=i;const t={div:null,properties:null,ctx:Ui.#ws(this.#Zi)};for(const e of this.#ds)t.properties=this.#cs.get(e),t.div=e,this.#ys(t)}}cancel(){const t=new tt("TextLayer task cancelled.");this.#ss?.cancel(t).catch((()=>{})),this.#ss=null,this.#Yi.reject(t)}get textDivs(){return this.#ds}get textContentItemsStr(){return this.#ls}#As(t){if(this.#Qi)return;this.#ts.ctx??=Ui.#ws(this.#Zi);const e=this.#ds,i=this.#ls;for(const s of t){if(e.length>1e5)return G(),void(this.#Qi=!0);if(void 0!==s.str)i.push(s.str),this.#_s(s);else if("beginMarkedContentProps"===s.type||"beginMarkedContent"===s.type){const t=this.#ft;this.#ft=document.createElement("span"),this.#ft.classList.add("markedContent"),null!==s.id&&this.#ft.setAttribute("id",`${s.id}`),t.append(this.#ft)}else"endMarkedContent"===s.type&&(this.#ft=this.#ft.parentNode)}}#_s(t){const e=document.createElement("span"),i={angle:0,canvasWidth:0,hasText:""!==t.str,hasEOL:t.hasEOL,fontSize:0};this.#ds.push(e);const s=at.transform(this.#us,t.transform);let n=Math.atan2(s[1],s[0]);const a=this.#os[t.fontName];a.vertical&&(n+=Math.PI/2);let r=this.#Ji&&a.fontSubstitution||a.fontFamily;r=Ui.fontFamilyMap.get(r)||r;const o=Math.hypot(s[2],s[3]),l=o*Ui.#xs(r,a,this.#Zi);let h,d;0===n?(h=s[4],d=s[5]-l):(h=s[4]+l*Math.sin(n),d=s[5]-l*Math.cos(n));const c="calc(var(--total-scale-factor) *",u=e.style;this.#ft===this.#ns?(u.left=`${(100*h/this.#is).toFixed(2)}%`,u.top=`${(100*d/this.#es).toFixed(2)}%`):(u.left=`${c}${h.toFixed(2)}px)`,u.top=`${c}${d.toFixed(2)}px)`),u.fontSize=`${c}${(Ui.#fs*o).toFixed(2)}px)`,u.fontFamily=r,i.fontSize=o,e.setAttribute("role","presentation"),e.textContent=t.str,e.dir=t.dir,this.#Ji&&(e.dataset.fontName=a.fontSubstitutionLoadedName||t.fontName),0!==n&&(i.angle=n*(180/Math.PI));let p=!1;if(t.str.length>1)p=!0;else if(" "!==t.str&&t.transform[0]!==t.transform[3]){const e=Math.abs(t.transform[0]),i=Math.abs(t.transform[3]);e!==i&&Math.max(e,i)/Math.min(e,i)>1.5&&(p=!0)}if(p&&(i.canvasWidth=a.vertical?t.height:t.width),this.#cs.set(e,i),this.#ts.div=e,this.#ts.properties=i,this.#ys(this.#ts),i.hasText&&this.#ft.append(e),i.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation"),this.#ft.append(t)}}#ys(t){const{div:e,properties:i,ctx:s}=t,{style:n}=e;let a="";if(Ui.#fs>1&&(a=`scale(${1/Ui.#fs})`),0!==i.canvasWidth&&i.hasText){const{fontFamily:t}=n,{canvasWidth:r,fontSize:o}=i;Ui.#Ss(s,o*this.#rs,t);const{width:l}=s.measureText(e.textContent);l>0&&(a=`scaleX(${r*this.#rs/l}) ${a}`)}0!==i.angle&&(a=`rotate(${i.angle}deg) ${a}`),a.length>0&&(n.transform=a)}static cleanup(){if(!(this.#bs.size>0)){this.#ps.clear();for(const{canvas:t}of this.#gs.values())t.remove();this.#gs.clear()}}static#ws(t=null){let e=this.#gs.get(t||="");if(!e){const i=document.createElement("canvas");i.className="hiddenCanvasElement",i.lang=t,document.body.append(i),e=i.getContext("2d",{alpha:!1,willReadFrequently:!0}),this.#gs.set(t,e),this.#ms.set(e,{size:0,family:""})}return e}static#Ss(t,e,i){const s=this.#ms.get(t);e===s.size&&i===s.family||(t.font=`${e}px ${i}`,s.size=e,s.family=i)}static#vs(){if(null!==this.#fs)return;const t=document.createElement("div");t.style.opacity=0,t.style.lineHeight=1,t.style.fontSize="1px",t.style.position="absolute",t.textContent="X",document.body.append(t),this.#fs=t.getBoundingClientRect().height,t.remove()}static#xs(t,e,i){const s=this.#ps.get(t);if(s)return s;const n=this.#ws(i);n.canvas.width=n.canvas.height=30,this.#Ss(n,30,t);const a=n.measureText(""),r=a.fontBoundingBoxAscent,o=Math.abs(a.fontBoundingBoxDescent);n.canvas.width=n.canvas.height=0;let l=.8;return r?l=r/(r+o):(st.platform.isFirefox&&G(),e.ascent?l=e.ascent:e.descent&&(l=1+e.descent)),this.#ps.set(t,l),l}}class Gi{static textContent(t){const e=[],i={items:e,styles:Object.create(null)};return function t(i){if(!i)return;let s=null;const n=i.name;if("#text"===n)s=i.value;else{if(!Gi.shouldBuildText(n))return;i?.attributes?.textContent?s=i.attributes.textContent:i.value&&(s=i.value)}if(null!==s&&e.push({str:s}),i.children)for(const e of i.children)t(e)}(t),i}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}function $i(t={}){"string"==typeof t||t instanceof URL?t={url:t}:(t instanceof ArrayBuffer||ArrayBuffer.isView(t))&&(t={data:t});const e=new ji,{docId:i}=e,n=t.url?function(t){if(t instanceof URL)return t.href;if("string"==typeof t){if(s)return t;const e=URL.parse(t,window.location);if(e)return e.href}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}(t.url):null,a=t.data?function(t){if(s&&"undefined"!=typeof Buffer&&t instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return it(t);if(t instanceof ArrayBuffer||ArrayBuffer.isView(t)||"object"==typeof t&&!isNaN(t?.length))return new Uint8Array(t);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}(t.data):null,r=t.httpHeaders||null,o=!0===t.withCredentials,l=t.password??null,h=t.range instanceof Vi?t.range:null,d=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:65536;let c=t.worker instanceof Xi?t.worker:null;const u=t.verbosity,p="string"!=typeof t.docBaseUrl||vt(t.docBaseUrl)?null:t.docBaseUrl,g=ie(t.cMapUrl),m=!1!==t.cMapPacked,f=t.CMapReaderFactory||(s?De:_e),b=ie(t.iccUrl),v=ie(t.standardFontDataUrl),A=t.StandardFontDataFactory||(s?Re:Ce),w=ie(t.wasmUrl),y=t.WasmFactory||(s?Le:Me),_=!0!==t.stopAtErrors,x=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,S=!1!==t.isEvalSupported,E="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!s,C="boolean"==typeof t.isImageDecoderSupported?t.isImageDecoderSupported:!s&&(st.platform.isFirefox||!globalThis.chrome),T=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,M="boolean"==typeof t.disableFontFace?t.disableFontFace:s,P=!0===t.fontExtraProperties,I=!0===t.enableXfa,k=t.ownerDocument||globalThis.document,D=!0===t.disableRange,R=!0===t.disableStream,L=!0===t.disableAutoFetch,F=!0===t.pdfBug,N=t.CanvasFactory||(s?ke:we),O=t.FilterFactory||(s?Ie:Se),B=!0===t.enableHWA,H=!1!==t.useWasm,U=h?h.length:t.length??NaN,G="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!s&&!M,$="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:!!(f===_e&&A===Ce&&y===Me&&g&&v&&w&&xt(g,document.baseURI)&&xt(v,document.baseURI)&&xt(w,document.baseURI));z(u);const j={canvasFactory:new N({ownerDocument:k,enableHWA:B}),filterFactory:new O({docId:i,ownerDocument:k}),cMapReaderFactory:$?null:new f({baseUrl:g,isCompressed:m}),standardFontDataFactory:$?null:new A({baseUrl:v}),wasmFactory:$?null:new y({baseUrl:w})};c||(c=Xi.create({verbosity:u,port:ui.workerPort}),e._worker=c);const V={docId:i,apiVersion:"5.3.31",data:a,password:l,disableAutoFetch:L,rangeChunkSize:d,length:U,docBaseUrl:p,enableXfa:I,evaluatorOptions:{maxImageSize:x,disableFontFace:M,ignoreErrors:_,isEvalSupported:S,isOffscreenCanvasSupported:E,isImageDecoderSupported:C,canvasMaxAreaInBytes:T,fontExtraProperties:P,useSystemFonts:G,useWasm:H,useWorkerFetch:$,cMapUrl:g,iccUrl:b,standardFontDataUrl:v,wasmUrl:w}},W={ownerDocument:k,pdfBug:F,styleElement:null,loadingParams:{disableAutoFetch:L,enableXfa:I}};return c.promise.then((function(){if(e.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const t=c.messageHandler.sendWithPromise("GetDocRequest",V,a?[a.buffer]:null);let l;if(h)l=new bi(h,{disableRange:D,disableStream:R});else if(!a){if(!n)throw new Error("getDocument - no `url` parameter provided.");const t=xt(n)?Mi:s?Ni:Di;l=new t({url:n,length:U,httpHeaders:r,withCredentials:o,rangeChunkSize:d,disableRange:D,disableStream:R})}return t.then((t=>{if(e.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const s=new ve(i,t,c.port),n=new Ki(s,e,l,W,j);e._transport=n,s.send("Ready",null)}))})).catch(e._capability.reject),e}class ji{static#vi=0;_capability=Promise.withResolvers();_transport=null;_worker=null;docId="d"+ji.#vi++;destroyed=!1;onPassword=null;onProgress=null;get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0),await(this._transport?.destroy())}catch(t){throw this._worker?.port&&delete this._worker._pendingDestroy,t}this._transport=null,this._worker?.destroy(),this._worker=null}async getData(){return this._transport.getData()}}class Vi{#Yi=Promise.withResolvers();#Es=[];#Cs=[];#Ts=[];#Ms=[];constructor(t,e,i=!1,s=null){this.length=t,this.initialData=e,this.progressiveDone=i,this.contentDispositionFilename=s}addRangeListener(t){this.#Ms.push(t)}addProgressListener(t){this.#Ts.push(t)}addProgressiveReadListener(t){this.#Cs.push(t)}addProgressiveDoneListener(t){this.#Es.push(t)}onDataRange(t,e){for(const i of this.#Ms)i(t,e)}onDataProgress(t,e){this.#Yi.promise.then((()=>{for(const i of this.#Ts)i(t,e)}))}onDataProgressiveRead(t){this.#Yi.promise.then((()=>{for(const e of this.#Cs)e(t)}))}onDataProgressiveDone(){this.#Yi.promise.then((()=>{for(const t of this.#Es)t()}))}transportReady(){this.#Yi.resolve()}requestDataRange(t,e){$("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class Wi{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return q(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class qi{#Ps=!1;constructor(t,e,i,s=!1){this._pageIndex=t,this._pageInfo=e,this._transport=i,this._stats=s?new _t:null,this._pdfBug=s,this.commonObjs=i.commonObjs,this.objs=new Hi,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:i=0,offsetY:s=0,dontFlip:n=!1}={}){return new ft({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return q(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:i="display",annotationMode:s=g.ENABLE,transform:n=null,background:a=null,optionalContentConfigPromise:r=null,annotationCanvasMap:o=null,pageColors:h=null,printAnnotationStorage:d=null,isEditing:c=!1}){this._stats?.time("Overall");const u=this._transport.getRenderingIntent(i,s,d,c),{renderingIntent:p,cacheKey:m}=u;this.#Ps=!1,r||=this._transport.getOptionalContentConfig(p);let f=this._intentStates.get(m);f||(f=Object.create(null),this._intentStates.set(m,f)),f.streamReaderCancelTimeout&&(clearTimeout(f.streamReaderCancelTimeout),f.streamReaderCancelTimeout=null);const b=!!(p&l);f.displayReadyCapability||(f.displayReadyCapability=Promise.withResolvers(),f.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(u));const v=t=>{f.renderTasks.delete(A),b&&(this.#Ps=!0),this.#Is(),t?(A.capability.reject(t),this._abortOperatorList({intentState:f,reason:t instanceof Error?t:new Error(t)})):A.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats))},A=new Qi({callback:v,params:{canvasContext:t,viewport:e,transform:n,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:o,operatorList:f.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!b,pdfBug:this._pdfBug,pageColors:h});(f.renderTasks||=new Set).add(A);const w=A.task;return Promise.all([f.displayReadyCapability.promise,r]).then((([t,e])=>{if(this.destroyed)v();else{if(this._stats?.time("Rendering"),!(e.renderingIntent&p))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");A.initializeGraphics({transparency:t,optionalContentConfig:e}),A.operatorListChanged()}})).catch(v),w}getOperatorList({intent:t="display",annotationMode:e=g.ENABLE,printAnnotationStorage:i=null,isEditing:s=!1}={}){const n=this._transport.getRenderingIntent(t,e,i,s,!0);let a,r=this._intentStates.get(n.cacheKey);return r||(r=Object.create(null),this._intentStates.set(n.cacheKey,r)),r.opListReadCapability||(a=Object.create(null),a.operatorListChanged=function(){r.operatorList.lastChunk&&(r.opListReadCapability.resolve(r.operatorList),r.renderTasks.delete(a))},r.opListReadCapability=Promise.withResolvers(),(r.renderTasks||=new Set).add(a),r.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(n)),r.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then((t=>Gi.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,i){const s=e.getReader(),n={items:[],styles:Object.create(null),lang:null};!function e(){s.read().then((function({value:i,done:s}){s?t(n):(n.lang??=i.lang,Object.assign(n.styles,i.styles),n.items.push(...i.items),e())}),i)}()}))}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const i of e.renderTasks)t.push(i.completed),i.cancel();return this.objs.clear(),this.#Ps=!1,Promise.all(t)}cleanup(t=!1){this.#Ps=!0;const e=this.#Is();return t&&e&&(this._stats&&=new _t),e}#Is(){if(!this.#Ps||this.destroyed)return!1;for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),this.#Ps=!1,!0}_startRenderPage(t,e){const i=this._intentStates.get(e);i&&(this._stats?.timeEnd("Page Request"),i.displayReadyCapability?.resolve(t))}_renderPageChunk(t,e){for(let i=0,s=t.length;i<s;i++)e.operatorList.fnArray.push(t.fnArray[i]),e.operatorList.argsArray.push(t.argsArray[i]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const i of e.renderTasks)i.operatorListChanged();t.lastChunk&&this.#Is()}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:i,modifiedIds:s}){const{map:n,transfer:a}=i,r=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:n,modifiedIds:s},a).getReader(),o=this._intentStates.get(e);o.streamReader=r;const l=()=>{r.read().then((({value:t,done:e})=>{e?o.streamReader=null:this._transport.destroyed||(this._renderPageChunk(t,o),l())}),(t=>{if(o.streamReader=null,!this._transport.destroyed){if(o.operatorList){o.operatorList.lastChunk=!0;for(const t of o.renderTasks)t.operatorListChanged();this.#Is()}if(o.displayReadyCapability)o.displayReadyCapability.reject(t);else{if(!o.opListReadCapability)throw t;o.opListReadCapability.reject(t)}}}))};l()}_abortOperatorList({intentState:t,reason:e,force:i=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!i){if(t.renderTasks.size>0)return;if(e instanceof bt){let i=100;return e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay),void(t.streamReaderCancelTimeout=setTimeout((()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})}),i))}}if(t.streamReader.cancel(new tt(e.message)).catch((()=>{})),t.streamReader=null,!this._transport.destroyed){for(const[e,i]of this._intentStates)if(i===t){this._intentStates.delete(e);break}this.cleanup()}}}get stats(){return this._stats}}class Xi{#Yi=Promise.withResolvers();#ks=null;#Fi=null;#Ds=null;static#Rs=0;static#Ls=!1;static#Fs=new WeakMap;static{s&&(this.#Ls=!0,ui.workerSrc||="./pdf.worker.mjs"),this._isSameOrigin=(t,e)=>{const i=URL.parse(t);if(!i?.origin||"null"===i.origin)return!1;const s=new URL(e,i);return i.origin===s.origin},this._createCDNWrapper=t=>{const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))},this.fromPort=t=>{if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");return this.create(t)}}constructor({name:t=null,port:e=null,verbosity:i=H()}={}){if(this.name=t,this.destroyed=!1,this.verbosity=i,e){if(Xi.#Fs.has(e))throw new Error("Cannot use more than one PDFWorker per port.");Xi.#Fs.set(e,this),this.#Ns(e)}else this.#Os()}get promise(){return this.#Yi.promise}#Bs(){this.#Yi.resolve(),this.#ks.send("configure",{verbosity:this.verbosity})}get port(){return this.#Fi}get messageHandler(){return this.#ks}#Ns(t){this.#Fi=t,this.#ks=new ve("main","worker",t),this.#ks.on("ready",(()=>{})),this.#Bs()}#Os(){if(Xi.#Ls||Xi.#zs)return void this.#Hs();let{workerSrc:t}=Xi;try{Xi._isSameOrigin(window.location,t)||(t=Xi._createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),i=new ve("main","worker",e),s=()=>{n.abort(),i.destroy(),e.terminate(),this.destroyed?this.#Yi.reject(new Error("Worker was destroyed")):this.#Hs()},n=new AbortController;e.addEventListener("error",(()=>{this.#Ds||s()}),{signal:n.signal}),i.on("test",(t=>{n.abort(),!this.destroyed&&t?(this.#ks=i,this.#Fi=e,this.#Ds=e,this.#Bs()):s()})),i.on("ready",(t=>{if(n.abort(),this.destroyed)s();else try{a()}catch{this.#Hs()}}));const a=()=>{const t=new Uint8Array;i.send("test",t,[t.buffer])};return void a()}catch{U()}this.#Hs()}#Hs(){Xi.#Ls||(G(),Xi.#Ls=!0),Xi._setupFakeWorkerGlobal.then((t=>{if(this.destroyed)return void this.#Yi.reject(new Error("Worker was destroyed"));const e=new ae;this.#Fi=e;const i="fake"+Xi.#Rs++,s=new ve(i+"_worker",i,e);t.setup(s,e),this.#ks=new ve(i,i+"_worker",e),this.#Bs()})).catch((t=>{this.#Yi.reject(new Error(`Setting up fake worker failed: "${t.message}".`))}))}destroy(){this.destroyed=!0,this.#Ds?.terminate(),this.#Ds=null,Xi.#Fs.delete(this.#Fi),this.#Fi=null,this.#ks?.destroy(),this.#ks=null}static create(t){const e=this.#Fs.get(t?.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.create - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new Xi(t)}static get workerSrc(){if(ui.workerSrc)return ui.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get#zs(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}}static get _setupFakeWorkerGlobal(){return q(this,"_setupFakeWorkerGlobal",(async()=>{if(this.#zs)return this.#zs;return(await import(this.workerSrc)).WorkerMessageHandler})())}}class Ki{#Us=new Map;#Gs=new Map;#$s=new Map;#js=new Map;#Vs=null;constructor(t,e,i,s,n){this.messageHandler=t,this.loadingTask=e,this.commonObjs=new Hi,this.fontLoader=new te({ownerDocument:s.ownerDocument,styleElement:s.styleElement}),this.loadingParams=s.loadingParams,this._params=s,this.canvasFactory=n.canvasFactory,this.filterFactory=n.filterFactory,this.cMapReaderFactory=n.cMapReaderFactory,this.standardFontDataFactory=n.standardFontDataFactory,this.wasmFactory=n.wasmFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=i,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.setupMessageHandler()}#Ws(t,e=null){const i=this.#Us.get(t);if(i)return i;const s=this.messageHandler.sendWithPromise(t,e);return this.#Us.set(t,s),s}get annotationStorage(){return q(this,"annotationStorage",new Jt)}getRenderingIntent(t,e=g.ENABLE,i=null,s=!1,n=!1){let a=o,m=Qt;switch(t){case"any":a=r;break;case"display":break;case"print":a=l;break;default:G()}const f=a&l&&i instanceof Zt?i:this.annotationStorage;switch(e){case g.DISABLE:a+=c;break;case g.ENABLE:break;case g.ENABLE_FORMS:a+=h;break;case g.ENABLE_STORAGE:a+=d,m=f.serializable;break;default:G()}s&&(a+=u),n&&(a+=p);const{ids:b,hash:v}=f.modifiedIds;return{renderingIntent:a,cacheKey:[a,m.hash,v].join("_"),annotationStorageSerializable:m,modifiedIds:b}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),this.#Vs?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const i of this.#Gs.values())t.push(i._destroy());this.#Gs.clear(),this.#$s.clear(),this.#js.clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then((()=>{this.commonObjs.clear(),this.fontLoader.clear(),this.#Us.clear(),this.filterFactory.destroy(),Ui.cleanup(),this._networkStream?.cancelAllRequests(new tt("Worker was terminated.")),this.messageHandler?.destroy(),this.messageHandler=null,this.destroyCapability.resolve()}),this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{j(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}},e.onPull=()=>{this._fullReader.read().then((function({value:t,done:i}){i?e.close():(j(t instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(t),1,[t]))})).catch((t=>{e.error(t)}))},e.onCancel=t=>{this._fullReader.cancel(t),e.ready.catch((t=>{if(!this.destroyed)throw t}))}})),t.on("ReaderHeadersReady",(async t=>{await this._fullReader.headersReady;const{isStreamingSupported:i,isRangeSupported:s,contentLength:n}=this._fullReader;return i&&s||(this._lastProgress&&e.onProgress?.(this._lastProgress),this._fullReader.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}),{isStreamingSupported:i,isRangeSupported:s,contentLength:n}})),t.on("GetRangeReader",((t,e)=>{j(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const i=this._networkStream.getRangeReader(t.begin,t.end);i?(e.onPull=()=>{i.read().then((function({value:t,done:i}){i?e.close():(j(t instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(t),1,[t]))})).catch((t=>{e.error(t)}))},e.onCancel=t=>{i.cancel(t),e.ready.catch((t=>{if(!this.destroyed)throw t}))}):e.close()})),t.on("GetDoc",(({pdfInfo:t})=>{this._numPages=t.numPages,this._htmlForXfa=t.htmlForXfa,delete t.htmlForXfa,e._capability.resolve(new Wi(t,this))})),t.on("DocException",(t=>{e._capability.reject(be(t))})),t.on("PasswordRequest",(t=>{this.#Vs=Promise.withResolvers();try{if(!e.onPassword)throw be(t);const i=t=>{t instanceof Error?this.#Vs.reject(t):this.#Vs.resolve({password:t})};e.onPassword(i,t.code)}catch(i){this.#Vs.reject(i)}return this.#Vs.promise})),t.on("DataLoaded",(t=>{e.onProgress?.({loaded:t.length,total:t.length}),this.downloadInfoCapability.resolve(t)})),t.on("StartRenderPage",(t=>{if(this.destroyed)return;this.#Gs.get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)})),t.on("commonobj",(([e,i,s])=>{if(this.destroyed)return null;if(this.commonObjs.has(e))return null;switch(i){case"Font":if("error"in s){const t=s.error;G(),this.commonObjs.resolve(e,t);break}const n=this._params.pdfBug&&globalThis.FontInspector?.enabled?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null,a=new ee(s,n);this.fontLoader.bind(a).catch((()=>t.sendWithPromise("FontFallback",{id:e}))).finally((()=>{!a.fontExtraProperties&&a.data&&(a.data=null),this.commonObjs.resolve(e,a)}));break;case"CopyLocalImage":const{imageRef:r}=s;j(r,"The imageRef must be defined.");for(const t of this.#Gs.values())for(const[,i]of t.objs)if(i?.ref===r)return i.dataLen?(this.commonObjs.resolve(e,structuredClone(i)),i.dataLen):null;break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(e,s);break;default:throw new Error(`Got unknown common object type ${i}`)}return null})),t.on("obj",(([t,e,i,s])=>{if(this.destroyed)return;const n=this.#Gs.get(e);if(!n.objs.has(t))if(0!==n._intentStates.size)switch(i){case"Image":case"Pattern":n.objs.resolve(t,s);break;default:throw new Error(`Got unknown object type ${i}`)}else s?.bitmap?.close()})),t.on("DocProgress",(t=>{this.destroyed||e.onProgress?.({loaded:t.loaded,total:t.total})})),t.on("FetchBinaryData",(async t=>{if(this.destroyed)throw new Error("Worker was destroyed.");const e=this[t.type];if(!e)throw new Error(`${t.type} not initialized, see the \`useWorkerFetch\` parameter.`);return e.fetch(t)}))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&G();const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally((()=>{this.annotationStorage.resetModified()}))}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,i=this.#$s.get(e);if(i)return i;const s=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((i=>{if(this.destroyed)throw new Error("Transport destroyed");i.refStr&&this.#js.set(i.refStr,t);const s=new qi(e,i,this,this._params.pdfBug);return this.#Gs.set(e,s),s}));return this.#$s.set(e,s),s}getPageIndex(t){return se(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#Ws("GetFieldObjects")}hasJSActions(){return this.#Ws("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#Ws("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return this.#Ws("GetOptionalContentConfig").then((e=>new fi(e,t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=this.#Us.get(t);if(e)return e;const i=this.messageHandler.sendWithPromise(t,null).then((t=>({info:t[0],metadata:t[1]?new pi(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null})));return this.#Us.set(t,i),i}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const t of this.#Gs.values()){if(!t.cleanup())throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear(),t||this.fontLoader.clear(),this.#Us.clear(),this.filterFactory.destroy(!0),Ui.cleanup()}}cachedPageNumber(t){if(!se(t))return null;const e=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`;return this.#js.get(e)??null}}class Yi{#qs=null;onContinue=null;onError=null;constructor(t){this.#qs=t}get promise(){return this.#qs.capability.promise}cancel(t=0){this.#qs.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#qs.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#qs;return t.form||t.canvas&&e?.size>0}}class Qi{#Xs=null;static#Ks=new WeakSet;constructor({callback:t,params:e,objs:i,commonObjs:s,annotationCanvasMap:n,operatorList:a,pageIndex:r,canvasFactory:o,filterFactory:l,useRequestAnimationFrame:h=!1,pdfBug:d=!1,pageColors:c=null}){this.callback=t,this.params=e,this.objs=i,this.commonObjs=s,this.annotationCanvasMap=n,this.operatorListIdx=null,this.operatorList=a,this._pageIndex=r,this.canvasFactory=o,this.filterFactory=l,this._pdfBug=d,this.pageColors=c,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window,this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new Yi(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(Qi.#Ks.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");Qi.#Ks.add(this._canvas)}this._pdfBug&&globalThis.StepperManager?.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:i,viewport:s,transform:n,background:a}=this.params;this.gfx=new ci(i,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:n,viewport:s,transparency:t,background:a}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1,this.cancelled=!0,this.gfx?.endDrawing(),this.#Xs&&(window.cancelAnimationFrame(this.#Xs),this.#Xs=null),Qi.#Ks.delete(this._canvas),t||=new bt(`Rendering cancelled, page ${this._pageIndex+1}`,e),this.callback(t),this.task.onError?.(t)}operatorListChanged(){this.graphicsReady?(this.stepper?.updateOperatorList(this.operatorList),this.running||this._continue()):this.graphicsReadyCallback||=this._continueBound}_continue(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?this.#Xs=window.requestAnimationFrame((()=>{this.#Xs=null,this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),Qi.#Ks.delete(this._canvas),this.callback())))}}const Ji="5.3.31",Zi="47ad820d9";function ts(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function es(t){return Math.max(0,Math.min(255,255*t))}class is{static CMYK_G([t,e,i,s]){return["G",1-Math.min(1,.3*t+.59*i+.11*e+s)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return[t=es(t),t,t]}static G_HTML([t]){const e=ts(t);return`#${e}${e}${e}`}static RGB_G([t,e,i]){return["G",.3*t+.59*e+.11*i]}static RGB_rgb(t){return t.map(es)}static RGB_HTML(t){return`#${t.map(ts).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,i,s]){return["RGB",1-Math.min(1,t+s),1-Math.min(1,i+s),1-Math.min(1,e+s)]}static CMYK_rgb([t,e,i,s]){return[es(1-Math.min(1,t+s)),es(1-Math.min(1,i+s)),es(1-Math.min(1,e+s))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,i]){const s=1-t,n=1-e,a=1-i;return["CMYK",s,n,a,Math.min(s,n,a)]}}class ss{create(t,e,i=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const s=this._createSVG("svg:svg");return s.setAttribute("version","1.1"),i||(s.setAttribute("width",`${t}px`),s.setAttribute("height",`${e}px`)),s.setAttribute("preserveAspectRatio","none"),s.setAttribute("viewBox",`0 0 ${t} ${e}`),s}createElement(t){if("string"!=typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){$("Abstract method `_createSVG` called.")}}class ns extends ss{_createSVG(t){return document.createElementNS(pt,t)}}class as{static setupStorage(t,e,i,s,n){const a=s.getValue(e,{value:null});switch(i.name){case"textarea":if(null!==a.value&&(t.textContent=a.value),"print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===i.attributes.type||"checkbox"===i.attributes.type){if(a.value===i.attributes.xfaOn?t.setAttribute("checked",!0):a.value===i.attributes.xfaOff&&t.removeAttribute("checked"),"print"===n)break;t.addEventListener("change",(t=>{s.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{if(null!==a.value&&t.setAttribute("value",a.value),"print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==a.value){t.setAttribute("value",a.value);for(const t of i.children)t.attributes.value===a.value?t.attributes.selected=!0:t.attributes.hasOwnProperty("selected")&&delete t.attributes.selected}t.addEventListener("input",(t=>{const i=t.target.options,n=-1===i.selectedIndex?"":i[i.selectedIndex].value;s.setValue(e,{value:n})}))}}static setAttributes({html:t,element:e,storage:i=null,intent:s,linkService:n}){const{attributes:a}=e,r=t instanceof HTMLAnchorElement;"radio"===a.type&&(a.name=`${a.name}-${s}`);for(const[o,l]of Object.entries(a))if(null!=l)switch(o){case"class":l.length&&t.setAttribute(o,l.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",l);break;case"style":Object.assign(t.style,l);break;case"textContent":t.textContent=l;break;default:(!r||"href"!==o&&"newWindow"!==o)&&t.setAttribute(o,l)}r&&n.addLinkAttributes(t,a.href,a.newWindow),i&&a.dataId&&this.setupStorage(t,a.dataId,e,i)}static render(t){const e=t.annotationStorage,i=t.linkService,s=t.xfaHtml,n=t.intent||"display",a=document.createElement(s.name);s.attributes&&this.setAttributes({html:a,element:s,intent:n,linkService:i});const r="richText"!==n,o=t.div;if(o.append(a),t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;o.style.transform=e}r&&o.setAttribute("class","xfaLayer xfaFont");const l=[];if(0===s.children.length){if(s.value){const t=document.createTextNode(s.value);a.append(t),r&&Gi.shouldBuildText(s.name)&&l.push(t)}return{textDivs:l}}const h=[[s,-1,a]];for(;h.length>0;){const[t,s,a]=h.at(-1);if(s+1===t.children.length){h.pop();continue}const o=t.children[++h.at(-1)[1]];if(null===o)continue;const{name:d}=o;if("#text"===d){const t=document.createTextNode(o.value);l.push(t),a.append(t);continue}const c=o?.attributes?.xmlns?document.createElementNS(o.attributes.xmlns,d):document.createElement(d);if(a.append(c),o.attributes&&this.setAttributes({html:c,element:o,storage:e,intent:n,linkService:i}),o.children?.length>0)h.push([o,-1,c]);else if(o.value){const t=document.createTextNode(o.value);r&&Gi.shouldBuildText(d)&&l.push(t),c.append(t)}}for(const d of o.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))d.setAttribute("readOnly",!0);return{textDivs:l}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}const rs=1e3,os=new WeakSet;class ls{static create(t){switch(t.data.annotationType){case E.LINK:return new ds(t);case E.TEXT:return new cs(t);case E.WIDGET:switch(t.data.fieldType){case"Tx":return new ps(t);case"Btn":return t.data.radioButton?new fs(t):t.data.checkBox?new ms(t):new bs(t);case"Ch":return new vs(t);case"Sig":return new gs(t)}return new us(t);case E.POPUP:return new As(t);case E.FREETEXT:return new ys(t);case E.LINE:return new _s(t);case E.SQUARE:return new xs(t);case E.CIRCLE:return new Ss(t);case E.POLYLINE:return new Es(t);case E.CARET:return new Ts(t);case E.INK:return new Ms(t);case E.POLYGON:return new Cs(t);case E.HIGHLIGHT:return new Ps(t);case E.UNDERLINE:return new Is(t);case E.SQUIGGLY:return new ks(t);case E.STRIKEOUT:return new Ds(t);case E.STAMP:return new Rs(t);case E.FILEATTACHMENT:return new Ls(t);default:return new hs(t)}}}class hs{#Ys=null;#Qs=!1;#Js=null;constructor(t,{isRenderable:e=!1,ignoreBorder:i=!1,createQuadrilaterals:s=!1}={}){this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(i)),s&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:i}){return!!(t?.str||e?.str||i?.str)}get _isEditable(){return this.data.isEditable}get hasPopupData(){return hs._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;this.#Ys||={rect:this.data.rect.slice(0)};const{rect:e}=t;e&&this.#Zs(e),this.#Js?.popup.updateEdited(t)}resetEdited(){this.#Ys&&(this.#Zs(this.#Ys.rect),this.#Js?.popup.resetEdited(),this.#Ys=null)}#Zs(t){const{container:{style:e},data:{rect:i,rotation:s},parent:{viewport:{rawDims:{pageWidth:n,pageHeight:a,pageX:r,pageY:o}}}}=this;i?.splice(0,4,...t),e.left=100*(t[0]-r)/n+"%",e.top=100*(a-t[3]+o)/a+"%",0===s?(e.width=100*(t[2]-t[0])/n+"%",e.height=100*(t[3]-t[1])/a+"%"):this.setRotation(s)}_createContainer(t){const{data:e,parent:{page:i,viewport:s}}=this,n=document.createElement("section");n.setAttribute("data-annotation-id",e.id),this instanceof us||(n.tabIndex=rs);const{style:a}=n;if(a.zIndex=this.parent.zIndex++,e.alternativeText&&(n.title=e.alternativeText),e.noRotate&&n.classList.add("norotate"),!e.rect||this instanceof As){const{rotation:t}=e;return e.hasOwnCanvas||0===t||this.setRotation(t,n),n}const{width:r,height:o}=this;if(!t&&e.borderStyle.width>0){a.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,i=e.borderStyle.verticalCornerRadius;if(t>0||i>0){const e=`calc(${t}px * var(--total-scale-factor)) / calc(${i}px * var(--total-scale-factor))`;a.borderRadius=e}else if(this instanceof fs){const t=`calc(${r}px * var(--total-scale-factor)) / calc(${o}px * var(--total-scale-factor))`;a.borderRadius=t}switch(e.borderStyle.style){case C:a.borderStyle="solid";break;case T:a.borderStyle="dashed";break;case M:case P:G();break;case I:a.borderBottomStyle="solid"}const s=e.borderColor||null;s?(this.#Qs=!0,a.borderColor=at.makeHexColor(0|s[0],0|s[1],0|s[2])):a.borderWidth=0}const l=at.normalizeRect([e.rect[0],i.view[3]-e.rect[1]+i.view[1],e.rect[2],i.view[3]-e.rect[3]+i.view[1]]),{pageWidth:h,pageHeight:d,pageX:c,pageY:u}=s.rawDims;a.left=100*(l[0]-c)/h+"%",a.top=100*(l[1]-u)/d+"%";const{rotation:p}=e;return e.hasOwnCanvas||0===p?(a.width=100*r/h+"%",a.height=100*o/d+"%"):this.setRotation(p,n),n}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:i,pageHeight:s}=this.parent.viewport.rawDims;let{width:n,height:a}=this;t%180!=0&&([n,a]=[a,n]),e.style.width=100*n/i+"%",e.style.height=100*a/s+"%",e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(t,e,i)=>{const s=i.detail[t],n=s[0],a=s.slice(1);i.target.style[e]=is[`${n}_HTML`](a),this.annotationStorage.setValue(this.data.id,{[e]:is[`${n}_rgb`](a)})};return q(this,"_commonActions",{display:t=>{const{display:e}=t.detail,i=e%2==1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{const{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e),this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const i=this._commonActions;for(const s of Object.keys(e.detail)){const n=t[s]||i[s];n?.(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const i=this._commonActions;for(const[s,n]of Object.entries(e)){const a=i[s];if(a){a({detail:{[s]:n},target:t}),delete e[s]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,i,s,n]=this.data.rect.map((t=>Math.fround(t)));if(8===t.length){const[a,r,o,l]=t.subarray(2,6);if(s===a&&n===r&&e===o&&i===l)return}const{style:a}=this.container;let r;if(this.#Qs){const{borderColor:t,borderWidth:e}=a;a.borderWidth=0,r=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${t}" stroke-width="${e}">`],this.container.classList.add("hasBorder")}const o=s-e,l=n-i,{svgFactory:h}=this,d=h.createElement("svg");d.classList.add("quadrilateralsContainer"),d.setAttribute("width",0),d.setAttribute("height",0);const c=h.createElement("defs");d.append(c);const u=h.createElement("clipPath"),p=`clippath_${this.data.id}`;u.setAttribute("id",p),u.setAttribute("clipPathUnits","objectBoundingBox"),c.append(u);for(let g=2,m=t.length;g<m;g+=8){const i=t[g],s=t[g+1],a=t[g+2],d=t[g+3],c=h.createElement("rect"),p=(a-e)/o,m=(n-s)/l,f=(i-a)/o,b=(s-d)/l;c.setAttribute("x",p),c.setAttribute("y",m),c.setAttribute("width",f),c.setAttribute("height",b),u.append(c),r?.push(`<rect vector-effect="non-scaling-stroke" x="${p}" y="${m}" width="${f}" height="${b}"/>`)}this.#Qs&&(r.push("</g></svg>')"),a.backgroundImage=r.join("")),this.container.append(d),this.container.style.clipPath=`url(#${p})`}_createPopup(){const{data:t}=this,e=this.#Js=new As({data:{color:t.color,titleObj:t.titleObj,modificationDate:t.modificationDate,contentsObj:t.contentsObj,richText:t.richText,parentRect:t.rect,borderStyle:0,id:`popup_${t.id}`,rotation:t.rotation},parent:this.parent,elements:[this]});this.parent.div.append(e.render())}render(){$("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const i=[];if(this._fieldObjects){const s=this._fieldObjects[t];if(s)for(const{page:t,id:n,exportValues:a}of s){if(-1===t)continue;if(n===e)continue;const s="string"==typeof a?a:null,r=document.querySelector(`[data-element-id="${n}"]`);!r||os.has(r)?i.push({id:n,exportValue:s,domElement:r}):G()}return i}for(const s of document.getElementsByName(t)){const{exportValue:t}=s,n=s.getAttribute("data-element-id");n!==e&&(os.has(s)&&i.push({id:n,exportValue:t,domElement:s}))}return i}show(){this.container&&(this.container.hidden=!1),this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0),this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",(()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})}))}get width(){return this.data.rect[2]-this.data.rect[0]}get height(){return this.data.rect[3]-this.data.rect[1]}}class ds extends hs{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0}),this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,i=document.createElement("a");i.setAttribute("data-element-id",t.id);let s=!1;return t.url?(e.addLinkAttributes(i,t.url,t.newWindow),s=!0):t.action?(this._bindNamedAction(i,t.action),s=!0):t.attachment?(this.#tn(i,t.attachment,t.attachmentDest),s=!0):t.setOCGState?(this.#en(i,t.setOCGState),s=!0):t.dest?(this._bindLink(i,t.dest),s=!0):(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(i,t),s=!0),t.resetForm?(this._bindResetFormAction(i,t.resetForm),s=!0):this.isTooltipOnly&&!s&&(this._bindLink(i,""),s=!0)),this.container.classList.add("linkAnnotation"),s&&this.container.append(i),this.container}#in(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e),t.onclick=()=>(e&&this.linkService.goToDestination(e),!1),(e||""===e)&&this.#in()}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeNamedAction(e),!1),this.#in()}#tn(t,e,i=null){t.href=this.linkService.getAnchorUrl(""),e.description&&(t.title=e.description),t.onclick=()=>(this.downloadManager?.openOrDownloadData(e.content,e.filename,i),!1),this.#in()}#en(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeSetOCGState(e),!1),this.#in()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const s of Object.keys(e.actions)){const n=i.get(s);n&&(t[n]=()=>(this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:s}}),!1))}t.onclick||(t.onclick=()=>!1),this.#in()}_bindResetFormAction(t,e){const i=t.onclick;if(i||(t.href=this.linkService.getAnchorUrl("")),this.#in(),!this._fieldObjects)return G(),void(i||(t.onclick=()=>!1));t.onclick=()=>{i?.();const{fields:t,refs:s,include:n}=e,a=[];if(0!==t.length||0!==s.length){const e=new Set(s);for(const i of t){const t=this._fieldObjects[i]||[];for(const{id:i}of t)e.add(i)}for(const t of Object.values(this._fieldObjects))for(const i of t)e.has(i.id)===n&&a.push(i)}else for(const e of Object.values(this._fieldObjects))a.push(...e);const r=this.annotationStorage,o=[];for(const e of a){const{id:t}=e;switch(o.push(t),e.type){case"text":{const i=e.defaultValue||"";r.setValue(t,{value:i});break}case"checkbox":case"radiobutton":{const i=e.defaultValue===e.exportValues;r.setValue(t,{value:i});break}case"combobox":case"listbox":{const i=e.defaultValue||"";r.setValue(t,{value:i});break}default:continue}const i=document.querySelector(`[data-element-id="${t}"]`);i&&(os.has(i)?i.dispatchEvent(new Event("resetform")):G())}return this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:o,name:"ResetForm"}}),!1}}}class cs extends hs{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class us extends hs{render(){return this.container}showElementAndHideCanvas(t){this.data.hasOwnCanvas&&("CANVAS"===t.previousSibling?.nodeName&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return st.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,i,s,n){i.includes("mouse")?t.addEventListener(i,(t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(i,(t=>{if("blur"===i){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===i){if(e.focused)return;e.focused=!0}n&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t)}})}))}_setEventListeners(t,e,i,s){for(const[n,a]of i)("Action"===a||this.data.actions?.[a])&&("Focus"!==a&&"Blur"!==a||(e||={focused:!1}),this._setEventListener(t,e,n,a,s),"Focus"!==a||this.data.actions?.Blur?"Blur"!==a||this.data.actions?.Focus||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null))}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":at.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:i}=this.data.defaultAppearanceData,s=this.data.defaultAppearanceData.fontSize||9,n=t.style;let r;const o=t=>Math.round(10*t)/10;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),e=t/(Math.round(t/(a*s))||1);r=Math.min(s,o(e/a))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2);r=Math.min(s,o(t/a))}n.fontSize=`calc(${r}px * var(--total-scale-factor))`,n.color=at.makeHexColor(i[0],i[1],i[2]),null!==this.data.textAlignment&&(n.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class ps extends us{constructor(t){super(t,{isRenderable:t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,i,s){const n=this.annotationStorage;for(const a of this._getElementsByName(t.name,t.id))a.domElement&&(a.domElement[e]=i),n.setValue(a.id,{[s]:i})}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let i=null;if(this.renderForms){const s=t.getValue(e,{value:this.data.fieldValue});let n=s.value||"";const a=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;a&&n.length>a&&(n=n.slice(0,a));let r=s.formattedValue||this.data.textContent?.join("\n")||null;r&&this.data.comb&&(r=r.replaceAll(/\s+/g,""));const o={userValue:n,formattedValue:r,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(i=document.createElement("textarea"),i.textContent=r??n,this.data.doNotScroll&&(i.style.overflowY="hidden")):(i=document.createElement("input"),i.type=this.data.password?"password":"text",i.setAttribute("value",r??n),this.data.doNotScroll&&(i.style.overflowX="hidden")),this.data.hasOwnCanvas&&(i.hidden=!0),os.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,i.name=this.data.fieldName,i.tabIndex=rs,this._setRequired(i,this.data.required),a&&(i.maxLength=a),i.addEventListener("input",(s=>{t.setValue(e,{value:s.target.value}),this.setPropertyOnSiblings(i,"value",s.target.value,"value"),o.formattedValue=null})),i.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue??"";i.value=o.userValue=e,o.formattedValue=null}));let l=t=>{const{formattedValue:e}=o;null!=e&&(t.target.value=e),t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){i.addEventListener("focus",(t=>{if(o.focused)return;const{target:e}=t;o.userValue&&(e.value=o.userValue),o.lastCommittedValue=e.value,o.commitKey=1,this.data.actions?.Focus||(o.focused=!0)})),i.addEventListener("updatefromsandbox",(i=>{this.showElementAndHideCanvas(i.target);const s={value(i){o.userValue=i.detail.value??"",t.setValue(e,{value:o.userValue.toString()}),i.target.value=o.userValue},formattedValue(i){const{formattedValue:s}=i.detail;o.formattedValue=s,null!=s&&i.target!==document.activeElement&&(i.target.value=s),t.setValue(e,{formattedValue:s})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:i=>{const{charLimit:s}=i.detail,{target:n}=i;if(0===s)return void n.removeAttribute("maxLength");n.setAttribute("maxLength",s);let a=o.userValue;!a||a.length<=s||(a=a.slice(0,s),n.value=o.userValue=a,t.setValue(e,{value:a}),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:a,willCommit:!0,commitKey:1,selStart:n.selectionStart,selEnd:n.selectionEnd}}))}};this._dispatchEventFromSandbox(s,i)})),i.addEventListener("keydown",(t=>{o.commitKey=1;let i=-1;if("Escape"===t.key?i=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(o.commitKey=3):i=2,-1===i)return;const{value:s}=t.target;o.lastCommittedValue!==s&&(o.lastCommittedValue=s,o.userValue=s,this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}))}));const s=l;l=null,i.addEventListener("blur",(t=>{if(!o.focused||!t.relatedTarget)return;this.data.actions?.Blur||(o.focused=!1);const{value:i}=t.target;o.userValue=i,o.lastCommittedValue!==i&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,willCommit:!0,commitKey:o.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}),s(t)})),this.data.actions?.Keystroke&&i.addEventListener("beforeinput",(t=>{o.lastCommittedValue=null;const{data:i,target:s}=t,{value:n,selectionStart:a,selectionEnd:r}=s;let l=a,h=r;switch(t.inputType){case"deleteWordBackward":{const t=n.substring(0,a).match(/\w*[^\w]*$/);t&&(l-=t[0].length);break}case"deleteWordForward":{const t=n.substring(a).match(/^[^\w]*\w*/);t&&(h+=t[0].length);break}case"deleteContentBackward":a===r&&(l-=1);break;case"deleteContentForward":a===r&&(h+=1)}t.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,change:i||"",willCommit:!1,selStart:l,selEnd:h}})})),this._setEventListeners(i,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}if(l&&i.addEventListener("blur",l),this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/a;i.classList.add("comb"),i.style.letterSpacing=`calc(${t}px * var(--total-scale-factor) - 1ch)`}}else i=document.createElement("div"),i.textContent=this.data.fieldValue,i.style.verticalAlign="middle",i.style.display="table-cell",this.data.hasOwnCanvas&&(i.hidden=!0);return this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class gs extends us{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class ms extends us{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.exportValue===e.fieldValue}).value;"string"==typeof s&&(s="Off"!==s,t.setValue(i,{value:s})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const n=document.createElement("input");return os.add(n),n.setAttribute("data-element-id",i),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="checkbox",n.name=e.fieldName,s&&n.setAttribute("checked",!0),n.setAttribute("exportValue",e.exportValue),n.tabIndex=rs,n.addEventListener("change",(s=>{const{name:n,checked:a}=s.target;for(const r of this._getElementsByName(n,i)){const i=a&&r.exportValue===e.exportValue;r.domElement&&(r.domElement.checked=i),t.setValue(r.id,{value:i})}t.setValue(i,{value:a})})),n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue||"Off";t.target.checked=i===e.exportValue})),this.enableScripting&&this.hasJSActions&&(n.addEventListener("updatefromsandbox",(e=>{const s={value(e){e.target.checked="Off"!==e.detail.value,t.setValue(i,{value:e.target.checked})}};this._dispatchEventFromSandbox(s,e)})),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))),this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class fs extends us{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof s&&(s=s!==e.buttonValue,t.setValue(i,{value:s})),s)for(const a of this._getElementsByName(e.fieldName,i))t.setValue(a.id,{value:!1});const n=document.createElement("input");if(os.add(n),n.setAttribute("data-element-id",i),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="radio",n.name=e.fieldName,s&&n.setAttribute("checked",!0),n.tabIndex=rs,n.addEventListener("change",(e=>{const{name:s,checked:n}=e.target;for(const a of this._getElementsByName(s,i))t.setValue(a.id,{value:!1});t.setValue(i,{value:n})})),n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue;t.target.checked=null!=i&&i===e.buttonValue})),this.enableScripting&&this.hasJSActions){const s=e.buttonValue;n.addEventListener("updatefromsandbox",(e=>{const n={value:e=>{const n=s===e.detail.value;for(const s of this._getElementsByName(e.target.name)){const e=n&&s.id===i;s.domElement&&(s.domElement.checked=e),t.setValue(s.id,{value:e})}}};this._dispatchEventFromSandbox(n,e)})),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}return this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class bs extends ds{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))),t}}class vs extends us{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,i=t.getValue(e,{value:this.data.fieldValue}),s=document.createElement("select");os.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,this._setRequired(s,this.data.required),s.name=this.data.fieldName,s.tabIndex=rs;let n=this.data.combo&&this.data.options.length>0;this.data.combo||(s.size=this.data.options.length,this.data.multiSelect&&(s.multiple=!0)),s.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const i of s.options)i.selected=i.value===e}));for(const h of this.data.options){const t=document.createElement("option");t.textContent=h.displayValue,t.value=h.exportValue,i.value.includes(h.exportValue)&&(t.setAttribute("selected",!0),n=!1),s.append(t)}let a=null;if(n){const t=document.createElement("option");t.value=" ",t.setAttribute("hidden",!0),t.setAttribute("selected",!0),s.prepend(t),a=()=>{t.remove(),s.removeEventListener("input",a),a=null},s.addEventListener("input",a)}const r=t=>{const e=t?"value":"textContent",{options:i,multiple:n}=s;return n?Array.prototype.filter.call(i,(t=>t.selected)).map((t=>t[e])):-1===i.selectedIndex?null:i[i.selectedIndex][e]};let o=r(!1);const l=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};return this.enableScripting&&this.hasJSActions?(s.addEventListener("updatefromsandbox",(i=>{const n={value(i){a?.();const n=i.detail.value,l=new Set(Array.isArray(n)?n:[n]);for(const t of s.options)t.selected=l.has(t.value);t.setValue(e,{value:r(!0)}),o=r(!1)},multipleSelection(t){s.multiple=!0},remove(i){const n=s.options,a=i.detail.remove;if(n[a].selected=!1,s.remove(a),n.length>0){-1===Array.prototype.findIndex.call(n,(t=>t.selected))&&(n[0].selected=!0)}t.setValue(e,{value:r(!0),items:l(i)}),o=r(!1)},clear(i){for(;0!==s.length;)s.remove(0);t.setValue(e,{value:null,items:[]}),o=r(!1)},insert(i){const{index:n,displayValue:a,exportValue:h}=i.detail.insert,d=s.children[n],c=document.createElement("option");c.textContent=a,c.value=h,d?d.before(c):s.append(c),t.setValue(e,{value:r(!0),items:l(i)}),o=r(!1)},items(i){const{items:n}=i.detail;for(;0!==s.length;)s.remove(0);for(const t of n){const{displayValue:e,exportValue:i}=t,n=document.createElement("option");n.textContent=e,n.value=i,s.append(n)}s.options.length>0&&(s.options[0].selected=!0),t.setValue(e,{value:r(!0),items:l(i)}),o=r(!1)},indices(i){const s=new Set(i.detail.indices);for(const t of i.target.options)t.selected=s.has(t.index);t.setValue(e,{value:r(!0)}),o=r(!1)},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(n,i)})),s.addEventListener("input",(i=>{const s=r(!0),n=r(!1);t.setValue(e,{value:s}),i.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:o,change:n,changeEx:s,willCommit:!1,commitKey:1,keyDown:!1}})})),this._setEventListeners(s,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],(t=>t.target.value))):s.addEventListener("input",(function(i){t.setValue(e,{value:r(!0)})})),this.data.combo&&this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class As extends hs{constructor(t){const{data:e,elements:i}=t;super(t,{isRenderable:hs._hasPopupData(e)}),this.elements=i,this.popup=null}render(){this.container.classList.add("popupAnnotation");const t=this.popup=new ws({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const i of this.elements)i.popup=t,i.container.ariaHasPopup="dialog",e.push(i.data.id),i.addHighlightArea();return this.container.setAttribute("aria-controls",e.map((t=>`${dt}${t}`)).join(",")),this.container}}class ws{#sn=this.#nn.bind(this);#an=this.#rn.bind(this);#on=this.#ln.bind(this);#hn=this.#dn.bind(this);#cn=null;#ft=null;#un=null;#pn=null;#gn=null;#mn=null;#fn=null;#bn=!1;#vn=null;#T=null;#An=null;#wn=null;#yn=null;#Ys=null;#_n=!1;constructor({container:t,color:e,elements:i,titleObj:s,modificationDate:n,contentsObj:a,richText:r,parent:o,rect:l,parentRect:h,open:d}){this.#ft=t,this.#yn=s,this.#un=a,this.#wn=r,this.#mn=o,this.#cn=e,this.#An=l,this.#fn=h,this.#gn=i,this.#pn=Ct.toDateObject(n),this.trigger=i.flatMap((t=>t.getElementsToTriggerPopup()));for(const c of this.trigger)c.addEventListener("click",this.#hn),c.addEventListener("mouseenter",this.#on),c.addEventListener("mouseleave",this.#an),c.classList.add("popupTriggerArea");for(const c of i)c.container?.addEventListener("keydown",this.#sn);this.#ft.hidden=!0,d&&this.#dn()}render(){if(this.#vn)return;const t=this.#vn=document.createElement("div");if(t.className="popup",this.#cn){const e=t.style.outlineColor=at.makeHexColor(...this.#cn);t.style.backgroundColor=`color-mix(in srgb, ${e} 30%, white)`}const e=document.createElement("span");e.className="header";const i=document.createElement("h1");if(e.append(i),({dir:i.dir,str:i.textContent}=this.#yn),t.append(e),this.#pn){const t=document.createElement("span");t.classList.add("popupDate"),t.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string"),t.setAttribute("data-l10n-args",JSON.stringify({dateObj:this.#pn.valueOf()})),e.append(t)}const s=this.#xn;if(s)as.render({xfaHtml:s,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{const e=this._formatContents(this.#un);t.append(e)}this.#ft.append(t)}get#xn(){const t=this.#wn,e=this.#un;return!t?.str||e?.str&&e.str!==t.str?null:this.#wn.html||null}get#Sn(){return this.#xn?.attributes?.style?.fontSize||0}get#En(){return this.#xn?.attributes?.style?.color||null}#Cn(t){const e=[],i={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},s={style:{color:this.#En,fontSize:this.#Sn?`calc(${this.#Sn}px * var(--total-scale-factor))`:""}};for(const n of t.split("\n"))e.push({name:"span",value:n,attributes:s});return i}_formatContents({str:t,dir:e}){const i=document.createElement("p");i.classList.add("popupContent"),i.dir=e;const s=t.split(/(?:\r\n?|\n)/);for(let n=0,a=s.length;n<a;++n){const t=s[n];i.append(document.createTextNode(t)),n<a-1&&i.append(document.createElement("br"))}return i}#nn(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||("Enter"===t.key||"Escape"===t.key&&this.#bn)&&this.#dn()}updateEdited({rect:t,popupContent:e}){this.#Ys||={contentsObj:this.#un,richText:this.#wn},t&&(this.#T=null),e&&(this.#wn=this.#Cn(e),this.#un=null),this.#vn?.remove(),this.#vn=null}resetEdited(){this.#Ys&&(({contentsObj:this.#un,richText:this.#wn}=this.#Ys),this.#Ys=null,this.#vn?.remove(),this.#vn=null,this.#T=null)}#Tn(){if(null!==this.#T)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:i,pageX:s,pageY:n}}}=this.#mn;let a=!!this.#fn,r=a?this.#fn:this.#An;for(const u of this.#gn)if(!r||null!==at.intersect(u.data.rect,r)){r=u.data.rect,a=!0;break}const o=at.normalizeRect([r[0],t[3]-r[1]+t[1],r[2],t[3]-r[3]+t[1]]),l=a?r[2]-r[0]+5:0,h=o[0]+l,d=o[1];this.#T=[100*(h-s)/e,100*(d-n)/i];const{style:c}=this.#ft;c.left=`${this.#T[0]}%`,c.top=`${this.#T[1]}%`}#dn(){this.#bn=!this.#bn,this.#bn?(this.#ln(),this.#ft.addEventListener("click",this.#hn),this.#ft.addEventListener("keydown",this.#sn)):(this.#rn(),this.#ft.removeEventListener("click",this.#hn),this.#ft.removeEventListener("keydown",this.#sn))}#ln(){this.#vn||this.render(),this.isVisible?this.#bn&&this.#ft.classList.add("focused"):(this.#Tn(),this.#ft.hidden=!1,this.#ft.style.zIndex=parseInt(this.#ft.style.zIndex)+1e3)}#rn(){this.#ft.classList.remove("focused"),!this.#bn&&this.isVisible&&(this.#ft.hidden=!0,this.#ft.style.zIndex=parseInt(this.#ft.style.zIndex)-1e3)}forceHide(){this.#_n=this.isVisible,this.#_n&&(this.#ft.hidden=!0)}maybeShow(){this.#_n&&(this.#vn||this.#ln(),this.#_n=!1,this.#ft.hidden=!1)}get isVisible(){return!1===this.#ft.hidden}}class ys extends hs{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=m.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent"),t.setAttribute("role","comment");for(const e of this.textContent){const i=document.createElement("span");i.textContent=e,t.append(i)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}class _s extends hs{#Mn=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");const{data:t,width:e,height:i}=this,s=this.svgFactory.create(e,i,!0),n=this.#Mn=this.svgFactory.createElement("svg:line");return n.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]),n.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]),n.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]),n.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]),n.setAttribute("stroke-width",t.borderStyle.width||1),n.setAttribute("stroke","transparent"),n.setAttribute("fill","transparent"),s.append(n),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#Mn}addHighlightArea(){this.container.classList.add("highlightArea")}}class xs extends hs{#Pn=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");const{data:t,width:e,height:i}=this,s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,a=this.#Pn=this.svgFactory.createElement("svg:rect");return a.setAttribute("x",n/2),a.setAttribute("y",n/2),a.setAttribute("width",e-n),a.setAttribute("height",i-n),a.setAttribute("stroke-width",n||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),s.append(a),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#Pn}addHighlightArea(){this.container.classList.add("highlightArea")}}class Ss extends hs{#In=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");const{data:t,width:e,height:i}=this,s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,a=this.#In=this.svgFactory.createElement("svg:ellipse");return a.setAttribute("cx",e/2),a.setAttribute("cy",i/2),a.setAttribute("rx",e/2-n/2),a.setAttribute("ry",i/2-n/2),a.setAttribute("stroke-width",n||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),s.append(a),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#In}addHighlightArea(){this.container.classList.add("highlightArea")}}class Es extends hs{#kn=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,vertices:e,borderStyle:i,popupRef:s},width:n,height:a}=this;if(!e)return this.container;const r=this.svgFactory.create(n,a,!0);let o=[];for(let h=0,d=e.length;h<d;h+=2){const i=e[h]-t[0],s=t[3]-e[h+1];o.push(`${i},${s}`)}o=o.join(" ");const l=this.#kn=this.svgFactory.createElement(this.svgElementName);return l.setAttribute("points",o),l.setAttribute("stroke-width",i.width||1),l.setAttribute("stroke","transparent"),l.setAttribute("fill","transparent"),r.append(l),this.container.append(r),!s&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#kn}addHighlightArea(){this.container.classList.add("highlightArea")}}class Cs extends Es{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class Ts extends hs{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class Ms extends hs{#Dn=null;#Rn=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType="InkHighlight"===this.data.it?m.HIGHLIGHT:m.INK}#Ln(t,e){switch(t){case 90:return{transform:`rotate(90) translate(${-e[0]},${e[1]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};case 180:return{transform:`rotate(180) translate(${-e[2]},${e[1]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]};case 270:return{transform:`rotate(270) translate(${-e[2]},${e[3]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};default:return{transform:`translate(${-e[0]},${e[3]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]}}}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,rotation:e,inkLists:i,borderStyle:s,popupRef:n}}=this,{transform:a,width:r,height:o}=this.#Ln(e,t),l=this.svgFactory.create(r,o,!0),h=this.#Dn=this.svgFactory.createElement("svg:g");l.append(h),h.setAttribute("stroke-width",s.width||1),h.setAttribute("stroke-linecap","round"),h.setAttribute("stroke-linejoin","round"),h.setAttribute("stroke-miterlimit",10),h.setAttribute("stroke","transparent"),h.setAttribute("fill","transparent"),h.setAttribute("transform",a);for(let d=0,c=i.length;d<c;d++){const t=this.svgFactory.createElement(this.svgElementName);this.#Rn.push(t),t.setAttribute("points",i[d].join(",")),h.append(t)}return!n&&this.hasPopupData&&this._createPopup(),this.container.append(l),this._editOnDoubleClick(),this.container}updateEdited(t){super.updateEdited(t);const{thickness:e,points:i,rect:s}=t,n=this.#Dn;if(e>=0&&n.setAttribute("stroke-width",e||1),i)for(let a=0,r=this.#Rn.length;a<r;a++)this.#Rn[a].setAttribute("points",i[a].join(","));if(s){const{transform:t,width:e,height:i}=this.#Ln(this.data.rotation,s);n.parentElement.setAttribute("viewBox",`0 0 ${e} ${i}`),n.setAttribute("transform",t)}}getElementsToTriggerPopup(){return this.#Rn}addHighlightArea(){this.container.classList.add("highlightArea")}}class Ps extends hs{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}),this.annotationEditorType=m.HIGHLIGHT}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this._editOnDoubleClick(),this.container}}class Is extends hs{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class ks extends hs{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class Ds extends hs{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class Rs extends hs{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.annotationEditorType=m.STAMP}render(){return this.container.classList.add("stampAnnotation"),this.container.setAttribute("role","img"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}class Ls extends hs{#Fn=null;constructor(t){super(t,{isRenderable:!0});const{file:e}=this.data;this.filename=e.filename,this.content=e.content,this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...e})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let i;e.hasAppearance||0===e.fillAlpha?i=document.createElement("div"):(i=document.createElement("img"),i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`,e.fillAlpha&&e.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(100*e.fillAlpha)}%);`)),i.addEventListener("dblclick",this.#Nn.bind(this)),this.#Fn=i;const{isMac:s}=st.platform;return t.addEventListener("keydown",(t=>{"Enter"===t.key&&(s?t.metaKey:t.ctrlKey)&&this.#Nn()})),!e.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea"),t.append(i),t}getElementsToTriggerPopup(){return this.#Fn}addHighlightArea(){this.container.classList.add("highlightArea")}#Nn(){this.downloadManager?.openOrDownloadData(this.content,this.filename)}}class Fs{#On=null;#Bn=null;#zn=new Map;#Hn=null;constructor({div:t,accessibilityManager:e,annotationCanvasMap:i,annotationEditorUIManager:s,page:n,viewport:a,structTreeLayer:r}){this.div=t,this.#On=e,this.#Bn=i,this.#Hn=r||null,this.page=n,this.viewport=a,this.zIndex=0,this._annotationEditorUIManager=s}hasEditableAnnotations(){return this.#zn.size>0}async#Un(t,e){const i=t.firstChild||t,s=i.id=`${dt}${e}`,n=await(this.#Hn?.getAriaAttributes(s));if(n)for(const[a,r]of n)i.setAttribute(a,r);this.div.append(t),this.#On?.moveElementInDOM(this.div,t,i,!1)}async render(t){const{annotations:e}=t,i=this.div;kt(i,this.viewport);const s=new Map,n={data:null,layer:i,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new ns,annotationStorage:t.annotationStorage||new Jt,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const a of e){if(a.noHTML)continue;const t=a.annotationType===E.POPUP;if(t){const t=s.get(a.id);if(!t)continue;n.elements=t}else if(a.rect[2]===a.rect[0]||a.rect[3]===a.rect[1])continue;n.data=a;const e=ls.create(n);if(!e.isRenderable)continue;if(!t&&a.popupRef){const t=s.get(a.popupRef);t?t.push(e):s.set(a.popupRef,[e])}const i=e.render();a.hidden&&(i.style.visibility="hidden"),await this.#Un(i,a.id),e._isEditable&&(this.#zn.set(e.data.id,e),this._annotationEditorUIManager?.renderAnnotationElement(e))}this.#Gn()}async addLinkAnnotations(t,e){const i={data:null,layer:this.div,linkService:e,svgFactory:new ns,parent:this};for(const s of t){s.borderStyle||=Fs._defaultBorderStyle,i.data=s;const t=ls.create(i);if(!t.isRenderable)continue;const e=t.render();await this.#Un(e,s.id)}}update({viewport:t}){const e=this.div;this.viewport=t,kt(e,{rotation:t.rotation}),this.#Gn(),e.hidden=!1}#Gn(){if(!this.#Bn)return;const t=this.div;for(const[e,i]of this.#Bn){const s=t.querySelector(`[data-annotation-id="${e}"]`);if(!s)continue;i.className="annotationContent";const{firstChild:n}=s;n?"CANVAS"===n.nodeName?n.replaceWith(i):n.classList.contains("annotationContent")?n.after(i):n.before(i):s.append(i);const a=this.#zn.get(e);a&&(a._hasNoCanvas?(this._annotationEditorUIManager?.setMissingCanvas(e,s.id,i),a._hasNoCanvas=!1):a.canvas=i)}this.#Bn.clear()}getEditableAnnotations(){return Array.from(this.#zn.values())}getEditableAnnotation(t){return this.#zn.get(t)}static get _defaultBorderStyle(){return q(this,"_defaultBorderStyle",Object.freeze({width:1,rawWidth:1,style:C,dashArray:[3],horizontalCornerRadius:0,verticalCornerRadius:0}))}}const Ns=/\r\n?|\n/g;class Os extends Vt{#cn;#$n="";#jn=`${this.id}-editor`;#Vn=null;#Sn;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){const t=Os.prototype,e=t=>t.isEmpty(),i=Gt.TRANSLATE_SMALL,s=Gt.TRANSLATE_BIG;return q(this,"_keyboardManager",new Ht([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-s,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[s,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-s],checker:e}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,s],checker:e}]]))}static _type="freetext";static _editorType=m.FREETEXT;constructor(t){super({...t,name:"freeTextEditor"}),this.#cn=t.color||Os._defaultColor||Vt._defaultLineColor,this.#Sn=t.fontSize||Os._defaultFontSize}static initialize(t,e){Vt.initialize(t,e);const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case f.FREETEXT_SIZE:Os._defaultFontSize=e;break;case f.FREETEXT_COLOR:Os._defaultColor=e}}updateParams(t,e){switch(t){case f.FREETEXT_SIZE:this.#Wn(e);break;case f.FREETEXT_COLOR:this.#qn(e)}}static get defaultPropertiesToUpdate(){return[[f.FREETEXT_SIZE,Os._defaultFontSize],[f.FREETEXT_COLOR,Os._defaultColor||Vt._defaultLineColor]]}get propertiesToUpdate(){return[[f.FREETEXT_SIZE,this.#Sn],[f.FREETEXT_COLOR,this.#cn]]}#Wn(t){const e=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--total-scale-factor))`,this.translate(0,-(t-this.#Sn)*this.parentScale),this.#Sn=t,this.#Xn()},i=this.#Sn;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:f.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#qn(t){const e=t=>{this.#cn=this.editorDiv.style.color=t},i=this.#cn;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:f.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const t=this.parentScale;return[-Os._internalPadding*t,-(Os._internalPadding+this.#Sn)*t]}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){if(!super.enableEditMode())return!1;this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.#Vn=new AbortController;const t=this._uiManager.combinedSignal(this.#Vn);return this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:t}),this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:t}),this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:t}),this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:t}),this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:t}),!0}disableEditMode(){return!!super.disableEditMode()&&(this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",this.#jn),this._isDraggable=!0,this.#Vn?.abort(),this.#Vn=null,this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing"),!0)}focusin(t){this._focusEventsAllowed&&(super.focusin(t),t.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(t){this.width||(this.enableEditMode(),t&&this.editorDiv.focus(),this._initialOptions?.isCentered&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}#Kn(){const t=[];this.editorDiv.normalize();let e=null;for(const i of this.editorDiv.childNodes)e?.nodeType===Node.TEXT_NODE&&"BR"===i.nodeName||(t.push(Os.#Yn(i)),e=i);return t.join("\n")}#Xn(){const[t,e]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:t,div:e}=this,s=e.style.display,n=e.classList.contains("hidden");e.classList.remove("hidden"),e.style.display="hidden",t.div.append(this.div),i=e.getBoundingClientRect(),e.remove(),e.style.display=s,e.classList.toggle("hidden",n)}this.rotation%180==this.parentRotation%180?(this.width=i.width/t,this.height=i.height/e):(this.width=i.height/t,this.height=i.width/e),this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const t=this.#$n,e=this.#$n=this.#Kn().trimEnd();if(t===e)return;const i=t=>{this.#$n=t,t?(this.#Qn(),this._uiManager.rebuild(this),this.#Xn()):this.remove()};this.addCommands({cmd:()=>{i(e)},undo:()=>{i(t)},mustExec:!1}),this.#Xn()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}keydown(t){t.target===this.div&&"Enter"===t.key&&(this.enterInEditMode(),t.preventDefault())}editorDivKeydown(t){Os._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}get canChangeContent(){return!0}render(){if(this.div)return this.div;let t,e;(this._isCopy||this.annotationElementId)&&(t=this.x,e=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",this.#jn),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2"),this.editorDiv.setAttribute("data-l10n-attrs","default-content"),this.enableEditing(),this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;if(i.fontSize=`calc(${this.#Sn}px * var(--total-scale-factor))`,i.color=this.#cn,this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),this._isCopy||this.annotationElementId){const[i,s]=this.parentDimensions;if(this.annotationElementId){const{position:n}=this._initialData;let[a,r]=this.getInitialTranslation();[a,r]=this.pageTranslationToScreen(a,r);const[o,l]=this.pageDimensions,[h,d]=this.pageTranslation;let c,u;switch(this.rotation){case 0:c=t+(n[0]-h)/o,u=e+this.height-(n[1]-d)/l;break;case 90:c=t+(n[0]-h)/o,u=e-(n[1]-d)/l,[a,r]=[r,-a];break;case 180:c=t-this.width+(n[0]-h)/o,u=e-(n[1]-d)/l,[a,r]=[-a,-r];break;case 270:c=t+(n[0]-h-this.height*l)/o,u=e+(n[1]-d-this.width*o)/l,[a,r]=[-r,a]}this.setAt(c*i,u*s,a,r)}else this._moveAfterPaste(t,e);this.#Qn(),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}static#Yn(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(Ns,"")}editorDivPaste(t){const e=t.clipboardData||window.clipboardData,{types:i}=e;if(1===i.length&&"text/plain"===i[0])return;t.preventDefault();const s=Os.#Jn(e.getData("text")||"").replaceAll(Ns,"\n");if(!s)return;const n=window.getSelection();if(!n.rangeCount)return;this.editorDiv.normalize(),n.deleteFromDocument();const a=n.getRangeAt(0);if(!s.includes("\n"))return a.insertNode(document.createTextNode(s)),this.editorDiv.normalize(),void n.collapseToStart();const{startContainer:r,startOffset:o}=a,l=[],h=[];if(r.nodeType===Node.TEXT_NODE){const t=r.parentElement;if(h.push(r.nodeValue.slice(o).replaceAll(Ns,"")),t!==this.editorDiv){let e=l;for(const i of this.editorDiv.childNodes)i!==t?e.push(Os.#Yn(i)):e=h}l.push(r.nodeValue.slice(0,o).replaceAll(Ns,""))}else if(r===this.editorDiv){let t=l,e=0;for(const i of this.editorDiv.childNodes)e++===o&&(t=h),t.push(Os.#Yn(i))}this.#$n=`${l.join("\n")}${s}${h.join("\n")}`,this.#Qn();const d=new Range;let c=Math.sumPrecise(l.map((t=>t.length)));for(const{firstChild:u}of this.editorDiv.childNodes)if(u.nodeType===Node.TEXT_NODE){const t=u.nodeValue.length;if(c<=t){d.setStart(u,c),d.setEnd(u,c);break}c-=t}n.removeAllRanges(),n.addRange(d)}#Qn(){if(this.editorDiv.replaceChildren(),this.#$n)for(const t of this.#$n.split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br")),this.editorDiv.append(e)}}#Zn(){return this.#$n.replaceAll(" "," ")}static#Jn(t){return t.replaceAll(" "," ")}get contentDiv(){return this.editorDiv}static async deserialize(t,e,i){let s=null;if(t instanceof ys){const{data:{defaultAppearanceData:{fontSize:e,fontColor:i},rect:n,rotation:a,id:r,popupRef:o},textContent:l,textPosition:h,parent:{page:{pageNumber:d}}}=t;if(!l||0===l.length)return null;s=t={annotationType:m.FREETEXT,color:Array.from(i),fontSize:e,value:l.join("\n"),position:h,pageIndex:d-1,rect:n.slice(0),rotation:a,id:r,deleted:!1,popupRef:o}}const n=await super.deserialize(t,e,i);return n.#Sn=t.fontSize,n.#cn=at.makeHexColor(...t.color),n.#$n=Os.#Jn(t.value),n.annotationElementId=t.id||null,n._initialData=s,n}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const e=Os._internalPadding*this.parentScale,i=this.getRect(e,e),s=Vt._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#cn),n={annotationType:m.FREETEXT,color:s,fontSize:this.#Sn,value:this.#Zn(),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(n.isCopy=!0,n):this.annotationElementId&&!this.#ta(n)?null:(n.id=this.annotationElementId,n)}#ta(t){const{value:e,fontSize:i,color:s,pageIndex:n}=this._initialData;return this._hasBeenMoved||t.value!==e||t.fontSize!==i||t.color.some(((t,e)=>t!==s[e]))||t.pageIndex!==n}renderAnnotationElement(t){const e=super.renderAnnotationElement(t);if(this.deleted)return e;const{style:i}=e;i.fontSize=`calc(${this.#Sn}px * var(--total-scale-factor))`,i.color=this.#cn,e.replaceChildren();for(const n of this.#$n.split("\n")){const t=document.createElement("div");t.append(n?document.createTextNode(n):document.createElement("br")),e.append(t)}const s=Os._internalPadding*this.parentScale;return t.updateEdited({rect:this.getRect(s,s),popupContent:this.#$n}),e}resetAnnotationElement(t){super.resetAnnotationElement(t),t.resetEdited()}}class Bs{static PRECISION=1e-4;toSVGPath(){$("Abstract method `toSVGPath` must be implemented.")}get box(){$("Abstract getter `box` must be implemented.")}serialize(t,e){$("Abstract method `serialize` must be implemented.")}static _rescale(t,e,i,s,n,a){a||=new Float32Array(t.length);for(let r=0,o=t.length;r<o;r+=2)a[r]=e+t[r]*s,a[r+1]=i+t[r+1]*n;return a}static _rescaleAndSwap(t,e,i,s,n,a){a||=new Float32Array(t.length);for(let r=0,o=t.length;r<o;r+=2)a[r]=e+t[r+1]*s,a[r+1]=i+t[r]*n;return a}static _translate(t,e,i,s){s||=new Float32Array(t.length);for(let n=0,a=t.length;n<a;n+=2)s[n]=e+t[n],s[n+1]=i+t[n+1];return s}static svgRound(t){return Math.round(1e4*t)}static _normalizePoint(t,e,i,s,n){switch(n){case 90:return[1-e/i,t/s];case 180:return[1-t/i,1-e/s];case 270:return[e/i,1-t/s];default:return[t/i,e/s]}}static _normalizePagePoint(t,e,i){switch(i){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,i,s,n,a){return[(t+5*i)/6,(e+5*s)/6,(5*i+n)/6,(5*s+a)/6,(i+n)/2,(s+a)/2]}}class zs{#ea;#ia=[];#sa;#na;#aa=[];#ra=new Float32Array(18);#oa;#la;#ha;#da;#ca;#ua;#pa=[];static#ga=8;static#ma=2;static#fa=zs.#ga+zs.#ma;constructor({x:t,y:e},i,s,n,a,r=0){this.#ea=i,this.#ua=n*s,this.#na=a,this.#ra.set([NaN,NaN,NaN,NaN,t,e],6),this.#sa=r,this.#da=zs.#ga*s,this.#ha=zs.#fa*s,this.#ca=s,this.#pa.push(t,e)}isEmpty(){return isNaN(this.#ra[8])}#ba(){const t=this.#ra.subarray(4,6),e=this.#ra.subarray(16,18),[i,s,n,a]=this.#ea;return[(this.#oa+(t[0]-e[0])/2-i)/n,(this.#la+(t[1]-e[1])/2-s)/a,(this.#oa+(e[0]-t[0])/2-i)/n,(this.#la+(e[1]-t[1])/2-s)/a]}add({x:t,y:e}){this.#oa=t,this.#la=e;const[i,s,n,a]=this.#ea;let[r,o,l,h]=this.#ra.subarray(8,12);const d=t-l,c=e-h,u=Math.hypot(d,c);if(u<this.#ha)return!1;const p=u-this.#da,g=p/u,m=g*d,f=g*c;let b=r,v=o;r=l,o=h,l+=m,h+=f,this.#pa?.push(t,e);const A=m/p,w=-f/p*this.#ua,y=A*this.#ua;if(this.#ra.set(this.#ra.subarray(2,8),0),this.#ra.set([l+w,h+y],4),this.#ra.set(this.#ra.subarray(14,18),12),this.#ra.set([l-w,h-y],16),isNaN(this.#ra[6]))return 0===this.#aa.length&&(this.#ra.set([r+w,o+y],2),this.#aa.push(NaN,NaN,NaN,NaN,(r+w-i)/n,(o+y-s)/a),this.#ra.set([r-w,o-y],14),this.#ia.push(NaN,NaN,NaN,NaN,(r-w-i)/n,(o-y-s)/a)),this.#ra.set([b,v,r,o,l,h],6),!this.isEmpty();this.#ra.set([b,v,r,o,l,h],6);return Math.abs(Math.atan2(v-o,b-r)-Math.atan2(f,m))<Math.PI/2?([r,o,l,h]=this.#ra.subarray(2,6),this.#aa.push(NaN,NaN,NaN,NaN,((r+l)/2-i)/n,((o+h)/2-s)/a),[r,o,b,v]=this.#ra.subarray(14,18),this.#ia.push(NaN,NaN,NaN,NaN,((b+r)/2-i)/n,((v+o)/2-s)/a),!0):([b,v,r,o,l,h]=this.#ra.subarray(0,6),this.#aa.push(((b+5*r)/6-i)/n,((v+5*o)/6-s)/a,((5*r+l)/6-i)/n,((5*o+h)/6-s)/a,((r+l)/2-i)/n,((o+h)/2-s)/a),[l,h,r,o,b,v]=this.#ra.subarray(12,18),this.#ia.push(((b+5*r)/6-i)/n,((v+5*o)/6-s)/a,((5*r+l)/6-i)/n,((5*o+h)/6-s)/a,((r+l)/2-i)/n,((o+h)/2-s)/a),!0)}toSVGPath(){if(this.isEmpty())return"";const t=this.#aa,e=this.#ia;if(isNaN(this.#ra[6])&&!this.isEmpty())return this.#va();const i=[];i.push(`M${t[4]} ${t[5]}`);for(let s=6;s<t.length;s+=6)isNaN(t[s])?i.push(`L${t[s+4]} ${t[s+5]}`):i.push(`C${t[s]} ${t[s+1]} ${t[s+2]} ${t[s+3]} ${t[s+4]} ${t[s+5]}`);this.#Aa(i);for(let s=e.length-6;s>=6;s-=6)isNaN(e[s])?i.push(`L${e[s+4]} ${e[s+5]}`):i.push(`C${e[s]} ${e[s+1]} ${e[s+2]} ${e[s+3]} ${e[s+4]} ${e[s+5]}`);return this.#wa(i),i.join(" ")}#va(){const[t,e,i,s]=this.#ea,[n,a,r,o]=this.#ba();return`M${(this.#ra[2]-t)/i} ${(this.#ra[3]-e)/s} L${(this.#ra[4]-t)/i} ${(this.#ra[5]-e)/s} L${n} ${a} L${r} ${o} L${(this.#ra[16]-t)/i} ${(this.#ra[17]-e)/s} L${(this.#ra[14]-t)/i} ${(this.#ra[15]-e)/s} Z`}#wa(t){const e=this.#ia;t.push(`L${e[4]} ${e[5]} Z`)}#Aa(t){const[e,i,s,n]=this.#ea,a=this.#ra.subarray(4,6),r=this.#ra.subarray(16,18),[o,l,h,d]=this.#ba();t.push(`L${(a[0]-e)/s} ${(a[1]-i)/n} L${o} ${l} L${h} ${d} L${(r[0]-e)/s} ${(r[1]-i)/n}`)}newFreeDrawOutline(t,e,i,s,n,a){return new Hs(t,e,i,s,n,a)}getOutlines(){const t=this.#aa,e=this.#ia,i=this.#ra,[s,n,a,r]=this.#ea,o=new Float32Array((this.#pa?.length??0)+2);for(let d=0,c=o.length-2;d<c;d+=2)o[d]=(this.#pa[d]-s)/a,o[d+1]=(this.#pa[d+1]-n)/r;if(o[o.length-2]=(this.#oa-s)/a,o[o.length-1]=(this.#la-n)/r,isNaN(i[6])&&!this.isEmpty())return this.#ya(o);const l=new Float32Array(this.#aa.length+24+this.#ia.length);let h=t.length;for(let d=0;d<h;d+=2)isNaN(t[d])?l[d]=l[d+1]=NaN:(l[d]=t[d],l[d+1]=t[d+1]);h=this.#_a(l,h);for(let d=e.length-6;d>=6;d-=6)for(let t=0;t<6;t+=2)isNaN(e[d+t])?(l[h]=l[h+1]=NaN,h+=2):(l[h]=e[d+t],l[h+1]=e[d+t+1],h+=2);return this.#xa(l,h),this.newFreeDrawOutline(l,o,this.#ea,this.#ca,this.#sa,this.#na)}#ya(t){const e=this.#ra,[i,s,n,a]=this.#ea,[r,o,l,h]=this.#ba(),d=new Float32Array(36);return d.set([NaN,NaN,NaN,NaN,(e[2]-i)/n,(e[3]-s)/a,NaN,NaN,NaN,NaN,(e[4]-i)/n,(e[5]-s)/a,NaN,NaN,NaN,NaN,r,o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,(e[16]-i)/n,(e[17]-s)/a,NaN,NaN,NaN,NaN,(e[14]-i)/n,(e[15]-s)/a],0),this.newFreeDrawOutline(d,t,this.#ea,this.#ca,this.#sa,this.#na)}#xa(t,e){const i=this.#ia;return t.set([NaN,NaN,NaN,NaN,i[4],i[5]],e),e+6}#_a(t,e){const i=this.#ra.subarray(4,6),s=this.#ra.subarray(16,18),[n,a,r,o]=this.#ea,[l,h,d,c]=this.#ba();return t.set([NaN,NaN,NaN,NaN,(i[0]-n)/r,(i[1]-a)/o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,d,c,NaN,NaN,NaN,NaN,(s[0]-n)/r,(s[1]-a)/o],e),e+24}}class Hs extends Bs{#ea;#Sa=new Float32Array(4);#sa;#na;#pa;#ca;#Ea;constructor(t,e,i,s,n,a){super(),this.#Ea=t,this.#pa=e,this.#ea=i,this.#ca=s,this.#sa=n,this.#na=a,this.lastPoint=[NaN,NaN],this.#Ca(a);const[r,o,l,h]=this.#Sa;for(let d=0,c=t.length;d<c;d+=2)t[d]=(t[d]-r)/l,t[d+1]=(t[d+1]-o)/h;for(let d=0,c=e.length;d<c;d+=2)e[d]=(e[d]-r)/l,e[d+1]=(e[d+1]-o)/h}toSVGPath(){const t=[`M${this.#Ea[4]} ${this.#Ea[5]}`];for(let e=6,i=this.#Ea.length;e<i;e+=6)isNaN(this.#Ea[e])?t.push(`L${this.#Ea[e+4]} ${this.#Ea[e+5]}`):t.push(`C${this.#Ea[e]} ${this.#Ea[e+1]} ${this.#Ea[e+2]} ${this.#Ea[e+3]} ${this.#Ea[e+4]} ${this.#Ea[e+5]}`);return t.push("Z"),t.join(" ")}serialize([t,e,i,s],n){const a=i-t,r=s-e;let o,l;switch(n){case 0:o=Bs._rescale(this.#Ea,t,s,a,-r),l=Bs._rescale(this.#pa,t,s,a,-r);break;case 90:o=Bs._rescaleAndSwap(this.#Ea,t,e,a,r),l=Bs._rescaleAndSwap(this.#pa,t,e,a,r);break;case 180:o=Bs._rescale(this.#Ea,i,e,-a,r),l=Bs._rescale(this.#pa,i,e,-a,r);break;case 270:o=Bs._rescaleAndSwap(this.#Ea,i,s,-a,-r),l=Bs._rescaleAndSwap(this.#pa,i,s,-a,-r)}return{outline:Array.from(o),points:[Array.from(l)]}}#Ca(t){const e=this.#Ea;let i=e[4],s=e[5];const n=[i,s,i,s];let a=i,r=s;const o=t?Math.max:Math.min;for(let h=6,d=e.length;h<d;h+=6){const t=e[h+4],l=e[h+5];if(isNaN(e[h]))at.pointBoundingBox(t,l,n),r<l?(a=t,r=l):r===l&&(a=o(a,t));else{const t=[1/0,1/0,-1/0,-1/0];at.bezierBoundingBox(i,s,...e.slice(h,h+6),t),at.rectBoundingBox(...t,n),r<t[3]?(a=t[2],r=t[3]):r===t[3]&&(a=o(a,t[2]))}i=t,s=l}const l=this.#Sa;l[0]=n[0]-this.#sa,l[1]=n[1]-this.#sa,l[2]=n[2]-n[0]+2*this.#sa,l[3]=n[3]-n[1]+2*this.#sa,this.lastPoint=[a,r]}get box(){return this.#Sa}newOutliner(t,e,i,s,n,a=0){return new zs(t,e,i,s,n,a)}getNewOutline(t,e){const[i,s,n,a]=this.#Sa,[r,o,l,h]=this.#ea,d=n*l,c=a*h,u=i*l+r,p=s*h+o,g=this.newOutliner({x:this.#pa[0]*d+u,y:this.#pa[1]*c+p},this.#ea,this.#ca,t,this.#na,e??this.#sa);for(let m=2;m<this.#pa.length;m+=2)g.add({x:this.#pa[m]*d+u,y:this.#pa[m+1]*c+p});return g.getOutlines()}}class Us{#ea;#Ta;#Ma=[];#Pa=[];constructor(t,e=0,i=0,s=!0){const n=[1/0,1/0,-1/0,-1/0],a=10**-4;for(const{x:u,y:p,width:g,height:m}of t){const t=Math.floor((u-e)/a)*a,i=Math.ceil((u+g+e)/a)*a,s=Math.floor((p-e)/a)*a,r=Math.ceil((p+m+e)/a)*a,o=[t,s,r,!0],l=[i,s,r,!1];this.#Ma.push(o,l),at.rectBoundingBox(t,s,i,r,n)}const r=n[2]-n[0]+2*i,o=n[3]-n[1]+2*i,l=n[0]-i,h=n[1]-i,d=this.#Ma.at(s?-1:-2),c=[d[0],d[2]];for(const u of this.#Ma){const[t,e,i]=u;u[0]=(t-l)/r,u[1]=(e-h)/o,u[2]=(i-h)/o}this.#ea=new Float32Array([l,h,r,o]),this.#Ta=c}getOutlines(){this.#Ma.sort(((t,e)=>t[0]-e[0]||t[1]-e[1]||t[2]-e[2]));const t=[];for(const e of this.#Ma)e[3]?(t.push(...this.#Ia(e)),this.#ka(e)):(this.#Da(e),t.push(...this.#Ia(e)));return this.#Ra(t)}#Ra(t){const e=[],i=new Set;for(const a of t){const[t,i,s]=a;e.push([t,i,a],[t,s,a])}e.sort(((t,e)=>t[1]-e[1]||t[0]-e[0]));for(let a=0,r=e.length;a<r;a+=2){const t=e[a][2],s=e[a+1][2];t.push(s),s.push(t),i.add(t),i.add(s)}const s=[];let n;for(;i.size>0;){const t=i.values().next().value;let[e,a,r,o,l]=t;i.delete(t);let h=e,d=a;for(n=[e,r],s.push(n);;){let t;if(i.has(o))t=o;else{if(!i.has(l))break;t=l}i.delete(t),[e,a,r,o,l]=t,h!==e&&(n.push(h,d,e,d===a?a:r),h=e),d=d===a?r:a}n.push(h,d)}return new Gs(s,this.#ea,this.#Ta)}#La(t){const e=this.#Pa;let i=0,s=e.length-1;for(;i<=s;){const n=i+s>>1,a=e[n][0];if(a===t)return n;a<t?i=n+1:s=n-1}return s+1}#ka([,t,e]){const i=this.#La(t);this.#Pa.splice(i,0,[t,e])}#Da([,t,e]){const i=this.#La(t);for(let s=i;s<this.#Pa.length;s++){const[i,n]=this.#Pa[s];if(i!==t)break;if(i===t&&n===e)return void this.#Pa.splice(s,1)}for(let s=i-1;s>=0;s--){const[i,n]=this.#Pa[s];if(i!==t)break;if(i===t&&n===e)return void this.#Pa.splice(s,1)}}#Ia(t){const[e,i,s]=t,n=[[e,i,s]],a=this.#La(s);for(let r=0;r<a;r++){const[t,i]=this.#Pa[r];for(let s=0,a=n.length;s<a;s++){const[,r,o]=n[s];if(!(i<=r||o<=t))if(r>=t)if(o>i)n[s][1]=i;else{if(1===a)return[];n.splice(s,1),s--,a--}else n[s][2]=t,o>i&&n.push([e,i,o])}}return n}}class Gs extends Bs{#ea;#Fa;constructor(t,e,i){super(),this.#Fa=t,this.#ea=e,this.lastPoint=i}toSVGPath(){const t=[];for(const e of this.#Fa){let[i,s]=e;t.push(`M${i} ${s}`);for(let n=2;n<e.length;n+=2){const a=e[n],r=e[n+1];a===i?(t.push(`V${r}`),s=r):r===s&&(t.push(`H${a}`),i=a)}t.push("Z")}return t.join(" ")}serialize([t,e,i,s],n){const a=[],r=i-t,o=s-e;for(const l of this.#Fa){const e=new Array(l.length);for(let i=0;i<l.length;i+=2)e[i]=t+l[i]*r,e[i+1]=s-l[i+1]*o;a.push(e)}return a}get box(){return this.#ea}get classNamesForOutlining(){return["highlightOutline"]}}class $s extends zs{newFreeDrawOutline(t,e,i,s,n,a){return new js(t,e,i,s,n,a)}}class js extends Hs{newOutliner(t,e,i,s,n,a=0){return new $s(t,e,i,s,n,a)}}class Vs{#Na=null;#Oa=null;#Ba;#za=null;#Ha=!1;#Ua=!1;#a=null;#Ga;#$a=null;#f=null;#ja;static#Va=null;static get _keyboardManager(){return q(this,"_keyboardManager",new Ht([[["Escape","mac+Escape"],Vs.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],Vs.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],Vs.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],Vs.prototype._moveToPrevious],[["Home","mac+Home"],Vs.prototype._moveToBeginning],[["End","mac+End"],Vs.prototype._moveToEnd]]))}constructor({editor:t=null,uiManager:e=null}){t?(this.#Ua=!1,this.#ja=f.HIGHLIGHT_COLOR,this.#a=t):(this.#Ua=!0,this.#ja=f.HIGHLIGHT_DEFAULT_COLOR),this.#f=t?._uiManager||e,this.#Ga=this.#f._eventBus,this.#Ba=t?.color||this.#f?.highlightColors.values().next().value||"#FFFF98",Vs.#Va||=Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"})}renderButton(){const t=this.#Na=document.createElement("button");t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.setAttribute("aria-haspopup",!0);const e=this.#f._signal;t.addEventListener("click",this.#Wa.bind(this),{signal:e}),t.addEventListener("keydown",this.#nn.bind(this),{signal:e});const i=this.#Oa=document.createElement("span");return i.className="swatch",i.setAttribute("aria-hidden",!0),i.style.backgroundColor=this.#Ba,t.append(i),t}renderMainDropdown(){const t=this.#za=this.#qa();return t.setAttribute("aria-orientation","horizontal"),t.setAttribute("aria-labelledby","highlightColorPickerLabel"),t}#qa(){const t=document.createElement("div"),e=this.#f._signal;t.addEventListener("contextmenu",St,{signal:e}),t.className="dropdown",t.role="listbox",t.setAttribute("aria-multiselectable",!1),t.setAttribute("aria-orientation","vertical"),t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");for(const[i,s]of this.#f.highlightColors){const n=document.createElement("button");n.tabIndex="0",n.role="option",n.setAttribute("data-color",s),n.title=i,n.setAttribute("data-l10n-id",Vs.#Va[i]);const a=document.createElement("span");n.append(a),a.className="swatch",a.style.backgroundColor=s,n.setAttribute("aria-selected",s===this.#Ba),n.addEventListener("click",this.#Xa.bind(this,s),{signal:e}),t.append(n)}return t.addEventListener("keydown",this.#nn.bind(this),{signal:e}),t}#Xa(t,e){e.stopPropagation(),this.#Ga.dispatch("switchannotationeditorparams",{source:this,type:this.#ja,value:t})}_colorSelectFromKeyboard(t){if(t.target===this.#Na)return void this.#Wa(t);const e=t.target.getAttribute("data-color");e&&this.#Xa(e,t)}_moveToNext(t){this.#Ka?t.target!==this.#Na?t.target.nextSibling?.focus():this.#za.firstChild?.focus():this.#Wa(t)}_moveToPrevious(t){t.target!==this.#za?.firstChild&&t.target!==this.#Na?(this.#Ka||this.#Wa(t),t.target.previousSibling?.focus()):this.#Ka&&this._hideDropdownFromKeyboard()}_moveToBeginning(t){this.#Ka?this.#za.firstChild?.focus():this.#Wa(t)}_moveToEnd(t){this.#Ka?this.#za.lastChild?.focus():this.#Wa(t)}#nn(t){Vs._keyboardManager.exec(this,t)}#Wa(t){if(this.#Ka)return void this.hideDropdown();if(this.#Ha=0===t.detail,this.#$a||(this.#$a=new AbortController,window.addEventListener("pointerdown",this.#d.bind(this),{signal:this.#f.combinedSignal(this.#$a)})),this.#za)return void this.#za.classList.remove("hidden");const e=this.#za=this.#qa();this.#Na.append(e)}#d(t){this.#za?.contains(t.target)||this.hideDropdown()}hideDropdown(){this.#za?.classList.add("hidden"),this.#$a?.abort(),this.#$a=null}get#Ka(){return this.#za&&!this.#za.classList.contains("hidden")}_hideDropdownFromKeyboard(){this.#Ua||(this.#Ka?(this.hideDropdown(),this.#Na.focus({preventScroll:!0,focusVisible:this.#Ha})):this.#a?.unselect())}updateColor(t){if(this.#Oa&&(this.#Oa.style.backgroundColor=t),!this.#za)return;const e=this.#f.highlightColors.values();for(const i of this.#za.children)i.setAttribute("aria-selected",e.next().value===t)}destroy(){this.#Na?.remove(),this.#Na=null,this.#Oa=null,this.#za?.remove(),this.#za=null}}class Ws extends Vt{#Ya=null;#Qa=0;#Ja;#Za=null;#n=null;#tr=null;#er=null;#ir=0;#sr=null;#nr=null;#w=null;#ar=!1;#Ta=null;#rr;#or=null;#lr="";#ua;#hr="";static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=12;static _type="highlight";static _editorType=m.HIGHLIGHT;static _freeHighlightId=-1;static _freeHighlight=null;static _freeHighlightClipId="";static get _keyboardManager(){const t=Ws.prototype;return q(this,"_keyboardManager",new Ht([[["ArrowLeft","mac+ArrowLeft"],t._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],t._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],t._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],t._moveCaret,{args:[3]}]]))}constructor(t){super({...t,name:"highlightEditor"}),this.color=t.color||Ws._defaultColor,this.#ua=t.thickness||Ws._defaultThickness,this.#rr=t.opacity||Ws._defaultOpacity,this.#Ja=t.boxes||null,this.#hr=t.methodOfCreation||"",this.#lr=t.text||"",this._isDraggable=!1,this.defaultL10nId="pdfjs-editor-highlight-editor",t.highlightId>-1?(this.#ar=!0,this.#dr(t),this.#cr()):this.#Ja&&(this.#Ya=t.anchorNode,this.#Qa=t.anchorOffset,this.#er=t.focusNode,this.#ir=t.focusOffset,this.#ur(),this.#cr(),this.rotate(this.rotation))}get telemetryInitialData(){return{action:"added",type:this.#ar?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:this.#ua,methodOfCreation:this.#hr}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}#ur(){const t=new Us(this.#Ja,.001);this.#nr=t.getOutlines(),[this.x,this.y,this.width,this.height]=this.#nr.box;const e=new Us(this.#Ja,.0025,.001,"ltr"===this._uiManager.direction);this.#tr=e.getOutlines();const{lastPoint:i}=this.#tr;this.#Ta=[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height]}#dr({highlightOutlines:t,highlightId:e,clipPathId:i}){this.#nr=t;if(this.#tr=t.getNewOutline(this.#ua/2****,.0025),e>=0)this.#w=e,this.#Za=i,this.parent.drawLayer.finalizeDraw(e,{bbox:t.box,path:{d:t.toSVGPath()}}),this.#or=this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:this.#tr.box,path:{d:this.#tr.toSVGPath()}},!0);else if(this.parent){const e=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(this.#w,{bbox:Ws.#pr(this.#nr.box,(e-this.rotation+360)%360),path:{d:t.toSVGPath()}}),this.parent.drawLayer.updateProperties(this.#or,{bbox:Ws.#pr(this.#tr.box,e),path:{d:this.#tr.toSVGPath()}})}const[s,n,a,r]=t.box;switch(this.rotation){case 0:this.x=s,this.y=n,this.width=a,this.height=r;break;case 90:{const[t,e]=this.parentDimensions;this.x=n,this.y=1-s,this.width=a*e/t,this.height=r*t/e;break}case 180:this.x=1-s,this.y=1-n,this.width=a,this.height=r;break;case 270:{const[t,e]=this.parentDimensions;this.x=1-n,this.y=s,this.width=a*e/t,this.height=r*t/e;break}}const{lastPoint:o}=this.#tr;this.#Ta=[(o[0]-s)/a,(o[1]-n)/r]}static initialize(t,e){Vt.initialize(t,e),Ws._defaultColor||=e.highlightColors?.values().next().value||"#fff066"}static updateDefaultParams(t,e){switch(t){case f.HIGHLIGHT_DEFAULT_COLOR:Ws._defaultColor=e;break;case f.HIGHLIGHT_THICKNESS:Ws._defaultThickness=e}}translateInPage(t,e){}get toolbarPosition(){return this.#Ta}updateParams(t,e){switch(t){case f.HIGHLIGHT_COLOR:this.#qn(e);break;case f.HIGHLIGHT_THICKNESS:this.#gr(e)}}static get defaultPropertiesToUpdate(){return[[f.HIGHLIGHT_DEFAULT_COLOR,Ws._defaultColor],[f.HIGHLIGHT_THICKNESS,Ws._defaultThickness]]}get propertiesToUpdate(){return[[f.HIGHLIGHT_COLOR,this.color||Ws._defaultColor],[f.HIGHLIGHT_THICKNESS,this.#ua||Ws._defaultThickness],[f.HIGHLIGHT_FREE,this.#ar]]}#qn(t){const e=(t,e)=>{this.color=t,this.#rr=e,this.parent?.drawLayer.updateProperties(this.#w,{root:{fill:t,"fill-opacity":e}}),this.#n?.updateColor(t)},i=this.color,s=this.#rr;this.addCommands({cmd:e.bind(this,t,Ws._defaultOpacity),undo:e.bind(this,i,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:f.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(t)},!0)}#gr(t){const e=this.#ua,i=t=>{this.#ua=t,this.#mr(t)};this.addCommands({cmd:i.bind(this,t),undo:i.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:f.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:t},!0)}async addEditToolbar(){const t=await super.addEditToolbar();return t?(this._uiManager.highlightColors&&(this.#n=new Vs({editor:this}),t.addColorPicker(this.#n)),t):null}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(this.#fr())}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,this.#fr())}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this),t&&this.div.focus()}remove(){this.#br(),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.#cr(),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){let e=!1;this.parent&&!t?this.#br():t&&(this.#cr(t),e=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(t),this.show(this._isVisible),e&&this.select()}#mr(t){if(!this.#ar)return;this.#dr({highlightOutlines:this.#nr.getNewOutline(t/2)}),this.fixAndSetPosition();const[e,i]=this.parentDimensions;this.setDims(this.width*e,this.height*i)}#br(){null!==this.#w&&this.parent&&(this.parent.drawLayer.remove(this.#w),this.#w=null,this.parent.drawLayer.remove(this.#or),this.#or=null)}#cr(t=this.parent){null===this.#w&&(({id:this.#w,clipPathId:this.#Za}=t.drawLayer.draw({bbox:this.#nr.box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":this.#rr},rootClass:{highlight:!0,free:this.#ar},path:{d:this.#nr.toSVGPath()}},!1,!0)),this.#or=t.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:this.#ar},bbox:this.#tr.box,path:{d:this.#tr.toSVGPath()}},this.#ar),this.#sr&&(this.#sr.style.clipPath=this.#Za))}static#pr([t,e,i,s],n){switch(n){case 90:return[1-e-s,t,s,i];case 180:return[1-t-i,1-e-s,i,s];case 270:return[e,1-t-i,s,i]}return[t,e,i,s]}rotate(t){const{drawLayer:e}=this.parent;let i;this.#ar?(t=(t-this.rotation+360)%360,i=Ws.#pr(this.#nr.box,t)):i=Ws.#pr([this.x,this.y,this.width,this.height],t),e.updateProperties(this.#w,{bbox:i,root:{"data-main-rotation":t}}),e.updateProperties(this.#or,{bbox:Ws.#pr(this.#tr.box,t),root:{"data-main-rotation":t}})}render(){if(this.div)return this.div;const t=super.render();this.#lr&&(t.setAttribute("aria-label",this.#lr),t.setAttribute("role","mark")),this.#ar?t.classList.add("free"):this.div.addEventListener("keydown",this.#vr.bind(this),{signal:this._uiManager._signal});const e=this.#sr=document.createElement("div");t.append(e),e.setAttribute("aria-hidden","true"),e.className="internal",e.style.clipPath=this.#Za;const[i,s]=this.parentDimensions;return this.setDims(this.width*i,this.height*s),Nt(this,this.#sr,["pointerover","pointerleave"]),this.enableEditing(),t}pointerover(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#or,{rootClass:{hovered:!0}})}pointerleave(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#or,{rootClass:{hovered:!1}})}#vr(t){Ws._keyboardManager.exec(this,t)}_moveCaret(t){switch(this.parent.unselect(this),t){case 0:case 2:this.#Ar(!0);break;case 1:case 3:this.#Ar(!1)}}#Ar(t){if(!this.#Ya)return;const e=window.getSelection();t?e.setPosition(this.#Ya,this.#Qa):e.setPosition(this.#er,this.#ir)}select(){super.select(),this.#or&&this.parent?.drawLayer.updateProperties(this.#or,{rootClass:{hovered:!1,selected:!0}})}unselect(){super.unselect(),this.#or&&(this.parent?.drawLayer.updateProperties(this.#or,{rootClass:{selected:!1}}),this.#ar||this.#Ar(!1))}get _mustFixPosition(){return!this.#ar}show(t=this._isVisible){super.show(t),this.parent&&(this.parent.drawLayer.updateProperties(this.#w,{rootClass:{hidden:!t}}),this.parent.drawLayer.updateProperties(this.#or,{rootClass:{hidden:!t}}))}#fr(){return this.#ar?this.rotation:0}#wr(){if(this.#ar)return null;const[t,e]=this.pageDimensions,[i,s]=this.pageTranslation,n=this.#Ja,a=new Float32Array(8*n.length);let r=0;for(const{x:o,y:l,width:h,height:d}of n){const n=o*t+i,c=(1-l)*e+s;a[r]=a[r+4]=n,a[r+1]=a[r+3]=c,a[r+2]=a[r+6]=n+h*t,a[r+5]=a[r+7]=c-d*e,r+=8}return a}#yr(t){return this.#nr.serialize(t,this.#fr())}static startHighlighting(t,e,{target:i,x:s,y:n}){const{x:a,y:r,width:o,height:l}=i.getBoundingClientRect(),h=new AbortController,d=t.combinedSignal(h),c=e=>{h.abort(),this.#_r(t,e)};window.addEventListener("blur",c,{signal:d}),window.addEventListener("pointerup",c,{signal:d}),window.addEventListener("pointerdown",Et,{capture:!0,passive:!1,signal:d}),window.addEventListener("contextmenu",St,{signal:d}),i.addEventListener("pointermove",this.#xr.bind(this,t),{signal:d}),this._freeHighlight=new $s({x:s,y:n},[a,r,o,l],t.scale,this._defaultThickness/2,e,.001),({id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0))}static#xr(t,e){this._freeHighlight.add(e)&&t.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})}static#_r(t,e){this._freeHighlight.isEmpty()?t.drawLayer.remove(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""}static async deserialize(t,e,i){let s=null;if(t instanceof Ps){const{data:{quadPoints:e,rect:i,rotation:n,id:a,color:r,opacity:o,popupRef:l},parent:{page:{pageNumber:h}}}=t;s=t={annotationType:m.HIGHLIGHT,color:Array.from(r),opacity:o,quadPoints:e,boxes:null,pageIndex:h-1,rect:i.slice(0),rotation:n,id:a,deleted:!1,popupRef:l}}else if(t instanceof Ms){const{data:{inkLists:e,rect:i,rotation:n,id:a,color:r,borderStyle:{rawWidth:o},popupRef:l},parent:{page:{pageNumber:h}}}=t;s=t={annotationType:m.HIGHLIGHT,color:Array.from(r),thickness:o,inkLists:e,boxes:null,pageIndex:h-1,rect:i.slice(0),rotation:n,id:a,deleted:!1,popupRef:l}}const{color:n,quadPoints:a,inkLists:r,opacity:o}=t,l=await super.deserialize(t,e,i);l.color=at.makeHexColor(...n),l.#rr=o||1,r&&(l.#ua=t.thickness),l.annotationElementId=t.id||null,l._initialData=s;const[h,d]=l.pageDimensions,[c,u]=l.pageTranslation;if(a){const t=l.#Ja=[];for(let e=0;e<a.length;e+=8)t.push({x:(a[e]-c)/h,y:1-(a[e+1]-u)/d,width:(a[e+2]-a[e])/h,height:(a[e+1]-a[e+5])/d});l.#ur(),l.#cr(),l.rotate(l.rotation)}else if(r){l.#ar=!0;const t=r[0],i={x:t[0]-c,y:d-(t[1]-u)},s=new $s(i,[0,0,h,d],1,l.#ua/2,!0,.001);for(let e=0,r=t.length;e<r;e+=2)i.x=t[e]-c,i.y=d-(t[e+1]-u),s.add(i);const{id:n,clipPathId:a}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:l.color,"fill-opacity":l._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:s.toSVGPath()}},!0,!0);l.#dr({highlightOutlines:s.getOutlines(),highlightId:n,clipPathId:a}),l.#cr(),l.rotate(l.parentRotation)}return l}serialize(t=!1){if(this.isEmpty()||t)return null;if(this.deleted)return this.serializeDeleted();const e=this.getRect(0,0),i=Vt._colorManager.convert(this.color),s={annotationType:m.HIGHLIGHT,color:i,opacity:this.#rr,thickness:this.#ua,quadPoints:this.#wr(),outlines:this.#yr(e),pageIndex:this.pageIndex,rect:e,rotation:this.#fr(),structTreeParentId:this._structTreeParentId};return this.annotationElementId&&!this.#ta(s)?null:(s.id=this.annotationElementId,s)}#ta(t){const{color:e}=this._initialData;return t.color.some(((t,i)=>t!==e[i]))}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}}class qs{#Sr=Object.create(null);updateProperty(t,e){this[t]=e,this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(const[e,i]of Object.entries(t))e.startsWith("_")||this.updateProperty(e,i)}updateSVGProperty(t,e){this.#Sr[t]=e}toSVGProperties(){const t=this.#Sr;return this.#Sr=Object.create(null),{root:t}}reset(){this.#Sr=Object.create(null)}updateAll(t=this){this.updateProperties(t)}clone(){$("Not implemented")}}class Xs extends Vt{#Er=null;#Cr;_drawId=null;static _currentDrawId=-1;static _currentParent=null;static#Tr=null;static#Mr=null;static#Pr=null;static#Ir=NaN;static#kr=null;static#Dr=null;static#Rr=NaN;static _INNER_MARGIN=3;constructor(t){super(t),this.#Cr=t.mustBeCommitted||!1,this._addOutlines(t)}_addOutlines(t){t.drawOutlines&&(this.#Lr(t),this.#cr())}#Lr({drawOutlines:t,drawId:e,drawingOptions:i}){this.#Er=t,this._drawingOptions||=i,e>=0?(this._drawId=e,this.parent.drawLayer.finalizeDraw(e,t.defaultProperties)):this._drawId=this.#Fr(t,this.parent),this.#Nr(t.box)}#Fr(t,e){const{id:i}=e.drawLayer.draw(Xs._mergeSVGProperties(this._drawingOptions.toSVGProperties(),t.defaultSVGProperties),!1,!1);return i}static _mergeSVGProperties(t,e){const i=new Set(Object.keys(t));for(const[s,n]of Object.entries(e))i.has(s)?Object.assign(t[s],n):t[s]=n;return t}static getDefaultDrawingOptions(t){$("Not implemented")}static get typesMap(){$("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(t,e){const i=this.typesMap.get(t);i&&this._defaultDrawingOptions.updateProperty(i,e),this._currentParent&&(Xs.#Tr.updateProperty(i,e),this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}updateParams(t,e){const i=this.constructor.typesMap.get(t);i&&this._updateProperty(t,i,e)}static get defaultPropertiesToUpdate(){const t=[],e=this._defaultDrawingOptions;for(const[i,s]of this.typesMap)t.push([i,e[s]]);return t}get propertiesToUpdate(){const t=[],{_drawingOptions:e}=this;for(const[i,s]of this.constructor.typesMap)t.push([i,e[s]]);return t}_updateProperty(t,e,i){const s=this._drawingOptions,n=s[e],a=t=>{s.updateProperty(e,t);const i=this.#Er.updateProperty(e,t);i&&this.#Nr(i),this.parent?.drawLayer.updateProperties(this._drawId,s.toSVGProperties())};this.addCommands({cmd:a.bind(this,i),undo:a.bind(this,n),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:t,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){this.parent?.drawLayer.updateProperties(this._drawId,Xs._mergeSVGProperties(this.#Er.getPathResizingSVGProperties(this.#Or()),{bbox:this.#Br()}))}_onResized(){this.parent?.drawLayer.updateProperties(this._drawId,Xs._mergeSVGProperties(this.#Er.getPathResizedSVGProperties(this.#Or()),{bbox:this.#Br()}))}_onTranslating(t,e){this.parent?.drawLayer.updateProperties(this._drawId,{bbox:this.#Br()})}_onTranslated(){this.parent?.drawLayer.updateProperties(this._drawId,Xs._mergeSVGProperties(this.#Er.getPathTranslatedSVGProperties(this.#Or(),this.parentDimensions),{bbox:this.#Br()}))}_onStartDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit(),this.disableEditMode(),this.disableEditing()}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this),this._isDraggable=!0,this.#Cr&&(this.#Cr=!1,this.commit(),this.parent.setSelected(this),t&&this.isOnScreen&&this.div.focus())}remove(){this.#br(),super.remove()}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.#cr(),this.#Nr(this.#Er.box),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){let e=!1;this.parent&&!t?(this._uiManager.removeShouldRescale(this),this.#br()):t&&(this._uiManager.addShouldRescale(this),this.#cr(t),e=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(t),e&&this.select()}#br(){null!==this._drawId&&this.parent&&(this.parent.drawLayer.remove(this._drawId),this._drawId=null,this._drawingOptions.reset())}#cr(t=this.parent){null!==this._drawId&&this.parent===t||(null===this._drawId?(this._drawingOptions.updateAll(),this._drawId=this.#Fr(this.#Er,t)):this.parent.drawLayer.updateParent(this._drawId,t.drawLayer))}#zr([t,e,i,s]){const{parentDimensions:[n,a],rotation:r}=this;switch(r){case 90:return[e,1-t,i*(a/n),s*(n/a)];case 180:return[1-t,1-e,i,s];case 270:return[1-e,t,i*(a/n),s*(n/a)];default:return[t,e,i,s]}}#Or(){const{x:t,y:e,width:i,height:s,parentDimensions:[n,a],rotation:r}=this;switch(r){case 90:return[1-e,t,i*(n/a),s*(a/n)];case 180:return[1-t,1-e,i,s];case 270:return[e,1-t,i*(n/a),s*(a/n)];default:return[t,e,i,s]}}#Nr(t){if([this.x,this.y,this.width,this.height]=this.#zr(t),this.div){this.fixAndSetPosition();const[t,e]=this.parentDimensions;this.setDims(this.width*t,this.height*e)}this._onResized()}#Br(){const{x:t,y:e,width:i,height:s,rotation:n,parentRotation:a,parentDimensions:[r,o]}=this;switch((4*n+a)/90){case 1:return[1-e-s,t,s,i];case 2:return[1-t-i,1-e-s,i,s];case 3:return[e,1-t-i,s,i];case 4:return[t,e-i*(r/o),s*(o/r),i*(r/o)];case 5:return[1-e,t,i*(r/o),s*(o/r)];case 6:return[1-t-s*(o/r),1-e,s*(o/r),i*(r/o)];case 7:return[e-i*(r/o),1-t-s*(o/r),i*(r/o),s*(o/r)];case 8:return[t-i,e-s,i,s];case 9:return[1-e,t-i,s,i];case 10:return[1-t,1-e,i,s];case 11:return[e-s,1-t,s,i];case 12:return[t-s*(o/r),e,s*(o/r),i*(r/o)];case 13:return[1-e-i*(r/o),t-s*(o/r),i*(r/o),s*(o/r)];case 14:return[1-t,1-e-i*(r/o),s*(o/r),i*(r/o)];case 15:return[e,1-t,i*(r/o),s*(o/r)];default:return[t,e,i,s]}}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,Xs._mergeSVGProperties({bbox:this.#Br()},this.#Er.updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&this.#Nr(this.#Er.updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){if(this.div)return this.div;let t,e;this._isCopy&&(t=this.x,e=this.y);const i=super.render();i.classList.add("draw");const s=document.createElement("div");i.append(s),s.setAttribute("aria-hidden","true"),s.className="internal";const[n,a]=this.parentDimensions;return this.setDims(this.width*n,this.height*a),this._uiManager.addShouldRescale(this),this.disableEditing(),this._isCopy&&this._moveAfterPaste(t,e),i}static createDrawerInstance(t,e,i,s,n){$("Not implemented")}static startDrawing(t,e,i,s){const{target:n,offsetX:a,offsetY:r,pointerId:o,pointerType:l}=s;if(Xs.#kr&&Xs.#kr!==l)return;const{viewport:{rotation:h}}=t,{width:d,height:c}=n.getBoundingClientRect(),u=Xs.#Mr=new AbortController,p=t.combinedSignal(u);Xs.#Ir||=o,Xs.#kr??=l,window.addEventListener("pointerup",(t=>{Xs.#Ir===t.pointerId?this._endDraw(t):Xs.#Dr?.delete(t.pointerId)}),{signal:p}),window.addEventListener("pointercancel",(t=>{Xs.#Ir===t.pointerId?this._currentParent.endDrawingSession():Xs.#Dr?.delete(t.pointerId)}),{signal:p}),window.addEventListener("pointerdown",(t=>{Xs.#kr===t.pointerType&&((Xs.#Dr||=new Set).add(t.pointerId),Xs.#Tr.isCancellable()&&(Xs.#Tr.removeLastElement(),Xs.#Tr.isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)))}),{capture:!0,passive:!1,signal:p}),window.addEventListener("contextmenu",St,{signal:p}),n.addEventListener("pointermove",this._drawMove.bind(this),{signal:p}),n.addEventListener("touchmove",(t=>{t.timeStamp===Xs.#Rr&&Et(t)}),{signal:p}),t.toggleDrawing(),e._editorUndoBar?.hide(),Xs.#Tr?t.drawLayer.updateProperties(this._currentDrawId,Xs.#Tr.startNew(a,r,d,c,h)):(e.updateUIForDefaultProperties(this),Xs.#Tr=this.createDrawerInstance(a,r,d,c,h),Xs.#Pr=this.getDefaultDrawingOptions(),this._currentParent=t,({id:this._currentDrawId}=t.drawLayer.draw(this._mergeSVGProperties(Xs.#Pr.toSVGProperties(),Xs.#Tr.defaultSVGProperties),!0,!1)))}static _drawMove(t){if(Xs.#Rr=-1,!Xs.#Tr)return;const{offsetX:e,offsetY:i,pointerId:s}=t;Xs.#Ir===s&&(Xs.#Dr?.size>=1?this._endDraw(t):(this._currentParent.drawLayer.updateProperties(this._currentDrawId,Xs.#Tr.add(e,i)),Xs.#Rr=t.timeStamp,Et(t)))}static _cleanup(t){t&&(this._currentDrawId=-1,this._currentParent=null,Xs.#Tr=null,Xs.#Pr=null,Xs.#kr=null,Xs.#Rr=NaN),Xs.#Mr&&(Xs.#Mr.abort(),Xs.#Mr=null,Xs.#Ir=NaN,Xs.#Dr=null)}static _endDraw(t){const e=this._currentParent;if(e)if(e.toggleDrawing(!0),this._cleanup(!1),t?.target===e.div&&e.drawLayer.updateProperties(this._currentDrawId,Xs.#Tr.end(t.offsetX,t.offsetY)),this.supportMultipleDrawings){const t=Xs.#Tr,i=this._currentDrawId,s=t.getLastElement();e.addCommands({cmd:()=>{e.drawLayer.updateProperties(i,t.setLastElement(s))},undo:()=>{e.drawLayer.updateProperties(i,t.removeLastElement())},mustExec:!1,type:f.DRAW_STEP})}else this.endDrawing(!1)}static endDrawing(t){const e=this._currentParent;if(!e)return null;if(e.toggleDrawing(!0),e.cleanUndoStack(f.DRAW_STEP),!Xs.#Tr.isEmpty()){const{pageDimensions:[i,s],scale:n}=e,a=e.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:Xs.#Tr.getOutlines(i*n,s*n,n,this._INNER_MARGIN),drawingOptions:Xs.#Pr,mustBeCommitted:!t});return this._cleanup(!0),a}return e.drawLayer.remove(this._currentDrawId),this._cleanup(!0),null}createDrawingOptions(t){}static deserializeDraw(t,e,i,s,n,a){$("Not implemented")}static async deserialize(t,e,i){const{rawDims:{pageWidth:s,pageHeight:n,pageX:a,pageY:r}}=e.viewport,o=this.deserializeDraw(a,r,s,n,this._INNER_MARGIN,t),l=await super.deserialize(t,e,i);return l.createDrawingOptions(t),l.#Lr({drawOutlines:o}),l.#cr(),l.onScaleChanging(),l.rotate(),l}serializeDraw(t){const[e,i]=this.pageTranslation,[s,n]=this.pageDimensions;return this.#Er.serialize([e,i,s,n],t)}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}}class Ks{#ra=new Float64Array(6);#Mn;#Hr;#as;#ua;#pa;#Ur="";#Gr=0;#Fa=new Ys;#$r;#jr;constructor(t,e,i,s,n,a){this.#$r=i,this.#jr=s,this.#as=n,this.#ua=a,[t,e]=this.#Vr(t,e);const r=this.#Mn=[NaN,NaN,NaN,NaN,t,e];this.#pa=[t,e],this.#Hr=[{line:r,points:this.#pa}],this.#ra.set(r,0)}updateProperty(t,e){"stroke-width"===t&&(this.#ua=e)}#Vr(t,e){return Bs._normalizePoint(t,e,this.#$r,this.#jr,this.#as)}isEmpty(){return!this.#Hr||0===this.#Hr.length}isCancellable(){return this.#pa.length<=10}add(t,e){[t,e]=this.#Vr(t,e);const[i,s,n,a]=this.#ra.subarray(2,6),r=t-n,o=e-a;return Math.hypot(this.#$r*r,this.#jr*o)<=2?null:(this.#pa.push(t,e),isNaN(i)?(this.#ra.set([n,a,t,e],2),this.#Mn.push(NaN,NaN,NaN,NaN,t,e),{path:{d:this.toSVGPath()}}):(isNaN(this.#ra[0])&&this.#Mn.splice(6,6),this.#ra.set([i,s,n,a,t,e],0),this.#Mn.push(...Bs.createBezierPoints(i,s,n,a,t,e)),{path:{d:this.toSVGPath()}}))}end(t,e){const i=this.add(t,e);return i||(2===this.#pa.length?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,i,s,n){this.#$r=i,this.#jr=s,this.#as=n,[t,e]=this.#Vr(t,e);const a=this.#Mn=[NaN,NaN,NaN,NaN,t,e];this.#pa=[t,e];const r=this.#Hr.at(-1);return r&&(r.line=new Float32Array(r.line),r.points=new Float32Array(r.points)),this.#Hr.push({line:a,points:this.#pa}),this.#ra.set(a,0),this.#Gr=0,this.toSVGPath(),null}getLastElement(){return this.#Hr.at(-1)}setLastElement(t){return this.#Hr?(this.#Hr.push(t),this.#Mn=t.line,this.#pa=t.points,this.#Gr=0,{path:{d:this.toSVGPath()}}):this.#Fa.setLastElement(t)}removeLastElement(){if(!this.#Hr)return this.#Fa.removeLastElement();this.#Hr.pop(),this.#Ur="";for(let t=0,e=this.#Hr.length;t<e;t++){const{line:e,points:i}=this.#Hr[t];this.#Mn=e,this.#pa=i,this.#Gr=0,this.toSVGPath()}return{path:{d:this.#Ur}}}toSVGPath(){const t=Bs.svgRound(this.#Mn[4]),e=Bs.svgRound(this.#Mn[5]);if(2===this.#pa.length)return this.#Ur=`${this.#Ur} M ${t} ${e} Z`,this.#Ur;if(this.#pa.length<=6){const i=this.#Ur.lastIndexOf("M");this.#Ur=`${this.#Ur.slice(0,i)} M ${t} ${e}`,this.#Gr=6}if(4===this.#pa.length){const t=Bs.svgRound(this.#Mn[10]),e=Bs.svgRound(this.#Mn[11]);return this.#Ur=`${this.#Ur} L ${t} ${e}`,this.#Gr=12,this.#Ur}const i=[];0===this.#Gr&&(i.push(`M ${t} ${e}`),this.#Gr=6);for(let s=this.#Gr,n=this.#Mn.length;s<n;s+=6){const[t,e,n,a,r,o]=this.#Mn.slice(s,s+6).map(Bs.svgRound);i.push(`C${t} ${e} ${n} ${a} ${r} ${o}`)}return this.#Ur+=i.join(" "),this.#Gr=this.#Mn.length,this.#Ur}getOutlines(t,e,i,s){const n=this.#Hr.at(-1);return n.line=new Float32Array(n.line),n.points=new Float32Array(n.points),this.#Fa.build(this.#Hr,t,e,i,this.#as,this.#ua,s),this.#ra=null,this.#Mn=null,this.#Hr=null,this.#Ur=null,this.#Fa}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}class Ys extends Bs{#Sa;#Wr=0;#sa;#Hr;#$r;#jr;#qr;#as;#ua;build(t,e,i,s,n,a,r){this.#$r=e,this.#jr=i,this.#qr=s,this.#as=n,this.#ua=a,this.#sa=r??0,this.#Hr=t,this.#Xr()}get thickness(){return this.#ua}setLastElement(t){return this.#Hr.push(t),{path:{d:this.toSVGPath()}}}removeLastElement(){return this.#Hr.pop(),{path:{d:this.toSVGPath()}}}toSVGPath(){const t=[];for(const{line:e}of this.#Hr)if(t.push(`M${Bs.svgRound(e[4])} ${Bs.svgRound(e[5])}`),6!==e.length)if(12===e.length&&isNaN(e[6]))t.push(`L${Bs.svgRound(e[10])} ${Bs.svgRound(e[11])}`);else for(let i=6,s=e.length;i<s;i+=6){const[s,n,a,r,o,l]=e.subarray(i,i+6).map(Bs.svgRound);t.push(`C${s} ${n} ${a} ${r} ${o} ${l}`)}else t.push("Z");return t.join("")}serialize([t,e,i,s],n){const a=[],r=[],[o,l,h,d]=this.#Kr();let c,u,p,g,m,f,b,v,A;switch(this.#as){case 0:A=Bs._rescale,c=t,u=e+s,p=i,g=-s,m=t+o*i,f=e+(1-l-d)*s,b=t+(o+h)*i,v=e+(1-l)*s;break;case 90:A=Bs._rescaleAndSwap,c=t,u=e,p=i,g=s,m=t+l*i,f=e+o*s,b=t+(l+d)*i,v=e+(o+h)*s;break;case 180:A=Bs._rescale,c=t+i,u=e,p=-i,g=s,m=t+(1-o-h)*i,f=e+l*s,b=t+(1-o)*i,v=e+(l+d)*s;break;case 270:A=Bs._rescaleAndSwap,c=t+i,u=e+s,p=-i,g=-s,m=t+(1-l-d)*i,f=e+(1-o-h)*s,b=t+(1-l)*i,v=e+(1-o)*s}for(const{line:w,points:y}of this.#Hr)a.push(A(w,c,u,p,g,n?new Array(w.length):null)),r.push(A(y,c,u,p,g,n?new Array(y.length):null));return{lines:a,points:r,rect:[m,f,b,v]}}static deserialize(t,e,i,s,n,{paths:{lines:a,points:r},rotation:o,thickness:l}){const h=[];let d,c,u,p,g;switch(o){case 0:g=Bs._rescale,d=-t/i,c=e/s+1,u=1/i,p=-1/s;break;case 90:g=Bs._rescaleAndSwap,d=-e/s,c=-t/i,u=1/s,p=1/i;break;case 180:g=Bs._rescale,d=t/i+1,c=-e/s,u=-1/i,p=1/s;break;case 270:g=Bs._rescaleAndSwap,d=e/s+1,c=t/i+1,u=-1/s,p=-1/i}if(!a){a=[];for(const t of r){const e=t.length;if(2===e){a.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1]]));continue}if(4===e){a.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1],NaN,NaN,NaN,NaN,t[2],t[3]]));continue}const i=new Float32Array(3*(e-2));a.push(i);let[s,n,r,o]=t.subarray(0,4);i.set([NaN,NaN,NaN,NaN,s,n],0);for(let a=4;a<e;a+=2){const e=t[a],l=t[a+1];i.set(Bs.createBezierPoints(s,n,r,o,e,l),3*(a-2)),[s,n,r,o]=[r,o,e,l]}}}for(let f=0,b=a.length;f<b;f++)h.push({line:g(a[f].map((t=>t??NaN)),d,c,u,p),points:g(r[f].map((t=>t??NaN)),d,c,u,p)});const m=new this.prototype.constructor;return m.build(h,i,s,1,o,l,n),m}#Yr(t=this.#ua){const e=this.#sa+t/2*this.#qr;return this.#as%180==0?[e/this.#$r,e/this.#jr]:[e/this.#jr,e/this.#$r]}#Kr(){const[t,e,i,s]=this.#Sa,[n,a]=this.#Yr(0);return[t+n,e+a,i-2*n,s-2*a]}#Xr(){const t=this.#Sa=new Float32Array([1/0,1/0,-1/0,-1/0]);for(const{line:s}of this.#Hr){if(s.length<=12){for(let e=4,i=s.length;e<i;e+=6)at.pointBoundingBox(s[e],s[e+1],t);continue}let e=s[4],i=s[5];for(let n=6,a=s.length;n<a;n+=6){const[a,r,o,l,h,d]=s.subarray(n,n+6);at.bezierBoundingBox(e,i,a,r,o,l,h,d,t),e=h,i=d}}const[e,i]=this.#Yr();t[0]=ct(t[0]-e,0,1),t[1]=ct(t[1]-i,0,1),t[2]=ct(t[2]+e,0,1),t[3]=ct(t[3]+i,0,1),t[2]-=t[0],t[3]-=t[1]}get box(){return this.#Sa}updateProperty(t,e){return"stroke-width"===t?this.#gr(e):null}#gr(t){const[e,i]=this.#Yr();this.#ua=t;const[s,n]=this.#Yr(),[a,r]=[s-e,n-i],o=this.#Sa;return o[0]-=a,o[1]-=r,o[2]+=2*a,o[3]+=2*r,o}updateParentDimensions([t,e],i){const[s,n]=this.#Yr();this.#$r=t,this.#jr=e,this.#qr=i;const[a,r]=this.#Yr(),o=a-s,l=r-n,h=this.#Sa;return h[0]-=o,h[1]-=l,h[2]+=2*o,h[3]+=2*l,h}updateRotation(t){return this.#Wr=t,{path:{transform:this.rotationTransform}}}get viewBox(){return this.#Sa.map(Bs.svgRound).join(" ")}get defaultProperties(){const[t,e]=this.#Sa;return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Bs.svgRound(t)} ${Bs.svgRound(e)}`}}}get rotationTransform(){const[,,t,e]=this.#Sa;let i=0,s=0,n=0,a=0,r=0,o=0;switch(this.#Wr){case 90:s=e/t,n=-t/e,r=t;break;case 180:i=-1,a=-1,r=t,o=e;break;case 270:s=-e/t,n=t/e,o=e;break;default:return""}return`matrix(${i} ${s} ${n} ${a} ${Bs.svgRound(r)} ${Bs.svgRound(o)})`}getPathResizingSVGProperties([t,e,i,s]){const[n,a]=this.#Yr(),[r,o,l,h]=this.#Sa;if(Math.abs(l-n)<=Bs.PRECISION||Math.abs(h-a)<=Bs.PRECISION){const n=t+i/2-(r+l/2),a=e+s/2-(o+h/2);return{path:{"transform-origin":`${Bs.svgRound(t)} ${Bs.svgRound(e)}`,transform:`${this.rotationTransform} translate(${n} ${a})`}}}const d=(i-2*n)/(l-2*n),c=(s-2*a)/(h-2*a),u=l/i,p=h/s;return{path:{"transform-origin":`${Bs.svgRound(r)} ${Bs.svgRound(o)}`,transform:`${this.rotationTransform} scale(${u} ${p}) translate(${Bs.svgRound(n)} ${Bs.svgRound(a)}) scale(${d} ${c}) translate(${Bs.svgRound(-n)} ${Bs.svgRound(-a)})`}}}getPathResizedSVGProperties([t,e,i,s]){const[n,a]=this.#Yr(),r=this.#Sa,[o,l,h,d]=r;if(r[0]=t,r[1]=e,r[2]=i,r[3]=s,Math.abs(h-n)<=Bs.PRECISION||Math.abs(d-a)<=Bs.PRECISION){const n=t+i/2-(o+h/2),a=e+s/2-(l+d/2);for(const{line:t,points:e}of this.#Hr)Bs._translate(t,n,a,t),Bs._translate(e,n,a,e);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Bs.svgRound(t)} ${Bs.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}const c=(i-2*n)/(h-2*n),u=(s-2*a)/(d-2*a),p=-c*(o+n)+t+n,g=-u*(l+a)+e+a;if(1!==c||1!==u||0!==p||0!==g)for(const{line:m,points:f}of this.#Hr)Bs._rescale(m,p,g,c,u,m),Bs._rescale(f,p,g,c,u,f);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Bs.svgRound(t)} ${Bs.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([t,e],i){const[s,n]=i,a=this.#Sa,r=t-a[0],o=e-a[1];if(this.#$r===s&&this.#jr===n)for(const{line:l,points:h}of this.#Hr)Bs._translate(l,r,o,l),Bs._translate(h,r,o,h);else{const t=this.#$r/s,e=this.#jr/n;this.#$r=s,this.#jr=n;for(const{line:i,points:s}of this.#Hr)Bs._rescale(i,r,o,t,e,i),Bs._rescale(s,r,o,t,e,s);a[2]*=t,a[3]*=e}return a[0]=t,a[1]=e,{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${Bs.svgRound(t)} ${Bs.svgRound(e)}`}}}get defaultSVGProperties(){const t=this.#Sa;return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${Bs.svgRound(t[0])} ${Bs.svgRound(t[1])}`,transform:this.rotationTransform||null},bbox:t}}}class Qs extends qs{constructor(t){super(),this._viewParameters=t,super.updateProperties({fill:"none",stroke:Vt._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(t,e){"stroke-width"===t&&(e??=this["stroke-width"],e*=this._viewParameters.realScale),super.updateSVGProperty(t,e)}clone(){const t=new Qs(this._viewParameters);return t.updateAll(this),t}}class Js extends Xs{static _type="ink";static _editorType=m.INK;static _defaultDrawingOptions=null;constructor(t){super({...t,name:"inkEditor"}),this._willKeepAspectRatio=!0,this.defaultL10nId="pdfjs-editor-ink-editor"}static initialize(t,e){Vt.initialize(t,e),this._defaultDrawingOptions=new Qs(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();return e.updateProperties(t),e}static get supportMultipleDrawings(){return!0}static get typesMap(){return q(this,"typesMap",new Map([[f.INK_THICKNESS,"stroke-width"],[f.INK_COLOR,"stroke"],[f.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(t,e,i,s,n){return new Ks(t,e,i,s,n,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(t,e,i,s,n,a){return Ys.deserialize(t,e,i,s,n,a)}static async deserialize(t,e,i){let s=null;if(t instanceof Ms){const{data:{inkLists:e,rect:i,rotation:n,id:a,color:r,opacity:o,borderStyle:{rawWidth:l},popupRef:h},parent:{page:{pageNumber:d}}}=t;s=t={annotationType:m.INK,color:Array.from(r),thickness:l,opacity:o,paths:{points:e},boxes:null,pageIndex:d-1,rect:i.slice(0),rotation:n,id:a,deleted:!1,popupRef:h}}const n=await super.deserialize(t,e,i);return n.annotationElementId=t.id||null,n._initialData=s,n}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();const{_drawId:t,_drawingOptions:e,parent:i}=this;e.updateSVGProperty("stroke-width"),i.drawLayer.updateProperties(t,e.toSVGProperties())}static onScaleChangingWhenDrawing(){const t=this._currentParent;t&&(super.onScaleChangingWhenDrawing(),this._defaultDrawingOptions.updateSVGProperty("stroke-width"),t.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}createDrawingOptions({color:t,thickness:e,opacity:i}){this._drawingOptions=Js.getDefaultDrawingOptions({stroke:at.makeHexColor(...t),"stroke-width":e,"stroke-opacity":i})}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const{lines:e,points:i,rect:s}=this.serializeDraw(t),{_drawingOptions:{stroke:n,"stroke-opacity":a,"stroke-width":r}}=this,o={annotationType:m.INK,color:Vt._colorManager.convert(n),opacity:a,thickness:r,paths:{lines:e,points:i},pageIndex:this.pageIndex,rect:s,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(o.isCopy=!0,o):this.annotationElementId&&!this.#ta(o)?null:(o.id=this.annotationElementId,o)}#ta(t){const{color:e,thickness:i,opacity:s,pageIndex:n}=this._initialData;return this._hasBeenMoved||this._hasBeenResized||t.color.some(((t,i)=>t!==e[i]))||t.thickness!==i||t.opacity!==s||t.pageIndex!==n}renderAnnotationElement(t){const{points:e,rect:i}=this.serializeDraw(!1);return t.updateEdited({rect:i,thickness:this._drawingOptions["stroke-width"],points:e}),null}}class Zs extends Ys{toSVGPath(){let t=super.toSVGPath();return t.endsWith("Z")||(t+="Z"),t}}class tn{static#Qr={maxDim:512,sigmaSFactor:.02,sigmaR:25,kernelSize:16};static#Jr(t,e,i,s){return s-=e,0===(i-=t)?s>0?0:4:1===i?s+6:2-s}static#Zr=new Int32Array([0,1,-1,1,-1,0,-1,-1,0,-1,1,-1,1,0,1,1]);static#to(t,e,i,s,n,a,r){const o=this.#Jr(i,s,n,a);for(let l=0;l<8;l++){const n=(-l+o-r+16)%8;if(0!==t[(i+this.#Zr[2*n])*e+(s+this.#Zr[2*n+1])])return n}return-1}static#eo(t,e,i,s,n,a,r){const o=this.#Jr(i,s,n,a);for(let l=0;l<8;l++){const n=(l+o+r+16)%8;if(0!==t[(i+this.#Zr[2*n])*e+(s+this.#Zr[2*n+1])])return n}return-1}static#io(t,e,i,s){const n=t.length,a=new Int32Array(n);for(let h=0;h<n;h++)a[h]=t[h]<=s?1:0;for(let h=1;h<i-1;h++)a[h*e]=a[h*e+e-1]=0;for(let h=0;h<e;h++)a[h]=a[e*i-1-h]=0;let r,o=1;const l=[];for(let h=1;h<i-1;h++){r=1;for(let t=1;t<e-1;t++){const i=h*e+t,s=a[i];if(0===s)continue;let n=h,d=t;if(1===s&&0===a[i-1])o+=1,d-=1;else{if(!(s>=1&&0===a[i+1])){1!==s&&(r=Math.abs(s));continue}o+=1,d+=1,s>1&&(r=s)}const c=[t,h],u=d===t+1,p={isHole:u,points:c,id:o,parent:0};let g;l.push(p);for(const t of l)if(t.id===r){g=t;break}g?g.isHole?p.parent=u?g.parent:r:p.parent=u?r:g.parent:p.parent=u?r:0;const m=this.#to(a,e,h,t,n,d,0);if(-1===m){a[i]=-o,1!==a[i]&&(r=Math.abs(a[i]));continue}let f=this.#Zr[2*m],b=this.#Zr[2*m+1];const v=h+f,A=t+b;n=v,d=A;let w=h,y=t;for(;;){const s=this.#eo(a,e,w,y,n,d,1);f=this.#Zr[2*s],b=this.#Zr[2*s+1];const l=w+f,u=y+b;c.push(u,l);const p=w*e+y;if(0===a[p+1]?a[p]=-o:1===a[p]&&(a[p]=o),l===h&&u===t&&w===v&&y===A){1!==a[i]&&(r=Math.abs(a[i]));break}n=w,d=y,w=l,y=u}}}return l}static#so(t,e,i,s){if(i-e<=4){for(let n=e;n<i-2;n+=2)s.push(t[n],t[n+1]);return}const n=t[e],a=t[e+1],r=t[i-4]-n,o=t[i-3]-a,l=Math.hypot(r,o),h=r/l,d=o/l,c=h*a-d*n,u=o/r,p=1/l,g=Math.atan(u),m=Math.cos(g),f=Math.sin(g),b=p*(Math.abs(m)+Math.abs(f)),v=p*(1-b+b**2),A=Math.max(Math.atan(Math.abs(f+m)*v),Math.atan(Math.abs(f-m)*v));let w=0,y=e;for(let _=e+2;_<i-2;_+=2){const e=Math.abs(c-h*t[_+1]+d*t[_]);e>w&&(y=_,w=e)}w>(l*A)**2?(this.#so(t,e,y+2,s),this.#so(t,y,i,s)):s.push(n,a)}static#no(t){const e=[],i=t.length;return this.#so(t,0,i,e),e.push(t[i-2],t[i-1]),e.length<=4?null:e}static#ao(t,e,i,s,n,a){const r=new Float32Array(a**2),o=-2*s**2,l=a>>1;for(let g=0;g<a;g++){const t=(g-l)**2;for(let e=0;e<a;e++)r[g*a+e]=Math.exp((t+(e-l)**2)/o)}const h=new Float32Array(256),d=-2*n**2;for(let g=0;g<256;g++)h[g]=Math.exp(g**2/d);const c=t.length,u=new Uint8Array(c),p=new Uint32Array(256);for(let g=0;g<i;g++)for(let s=0;s<e;s++){const n=g*e+s,o=t[n];let d=0,c=0;for(let u=0;u<a;u++){const n=g+u-l;if(!(n<0||n>=i))for(let i=0;i<a;i++){const p=s+i-l;if(p<0||p>=e)continue;const g=t[n*e+p],m=r[u*a+i]*h[Math.abs(g-o)];d+=g*m,c+=m}}p[u[n]=Math.round(d/c)]++}return[u,p]}static#ro(t){const e=new Uint32Array(256);for(const i of t)e[i]++;return e}static#oo(t){const e=t.length,i=new Uint8ClampedArray(e>>2);let s=-1/0,n=1/0;for(let r=0,o=i.length;r<o;r++){if(0===t[3+(r<<2)]){s=i[r]=255;continue}const e=i[r]=t[r<<2];e>s&&(s=e),e<n&&(n=e)}const a=255/(s-n);for(let r=0;r<e;r++)i[r]=(i[r]-n)*a;return i}static#lo(t){let e,i=-1/0,s=-1/0;const n=t.findIndex((t=>0!==t));let a=n,r=n;for(e=n;e<256;e++){const n=t[e];n>i&&(e-a>s&&(s=e-a,r=e-1),i=n,a=e)}for(e=r-1;e>=0&&!(t[e]>t[e+1]);e--);return e}static#ho(t){const e=t,{width:i,height:s}=t,{maxDim:n}=this.#Qr;let a=i,r=s;if(i>n||s>n){let o=i,l=s,h=Math.log2(Math.max(i,s)/n);const d=Math.floor(h);h=h===d?d-1:d;for(let i=0;i<h;i++){a=Math.ceil(o/2),r=Math.ceil(l/2);const i=new OffscreenCanvas(a,r);i.getContext("2d").drawImage(t,0,0,o,l,0,0,a,r),o=a,l=r,t!==e&&t.close(),t=i.transferToImageBitmap()}const c=Math.min(n/a,n/r);a=Math.round(a*c),r=Math.round(r*c)}const o=new OffscreenCanvas(a,r).getContext("2d",{willReadFrequently:!0});o.filter="grayscale(1)",o.drawImage(t,0,0,t.width,t.height,0,0,a,r);const l=o.getImageData(0,0,a,r).data;return[this.#oo(l),a,r]}static extractContoursFromText(t,{fontFamily:e,fontStyle:i,fontWeight:s},n,a,r,o){let l=new OffscreenCanvas(1,1),h=l.getContext("2d",{alpha:!1});const d=h.font=`${i} ${s} 200px ${e}`,{actualBoundingBoxLeft:c,actualBoundingBoxRight:u,actualBoundingBoxAscent:p,actualBoundingBoxDescent:g,fontBoundingBoxAscent:m,fontBoundingBoxDescent:f,width:b}=h.measureText(t),v=1.5,A=Math.ceil(Math.max(Math.abs(c)+Math.abs(u)||0,b)*v),w=Math.ceil(Math.max(Math.abs(p)+Math.abs(g)||200,Math.abs(m)+Math.abs(f)||200)*v);l=new OffscreenCanvas(A,w),h=l.getContext("2d",{alpha:!0,willReadFrequently:!0}),h.font=d,h.filter="grayscale(1)",h.fillStyle="white",h.fillRect(0,0,A,w),h.fillStyle="black",h.fillText(t,.5*A/2,1.5*w/2);const y=this.#oo(h.getImageData(0,0,A,w).data),_=this.#ro(y),x=this.#lo(_),S=this.#io(y,A,w,x);return this.processDrawnLines({lines:{curves:S,width:A,height:w},pageWidth:n,pageHeight:a,rotation:r,innerMargin:o,mustSmooth:!0,areContours:!0})}static process(t,e,i,s,n){const[a,r,o]=this.#ho(t),[l,h]=this.#ao(a,r,o,Math.hypot(r,o)*this.#Qr.sigmaSFactor,this.#Qr.sigmaR,this.#Qr.kernelSize),d=this.#lo(h),c=this.#io(l,r,o,d);return this.processDrawnLines({lines:{curves:c,width:r,height:o},pageWidth:e,pageHeight:i,rotation:s,innerMargin:n,mustSmooth:!0,areContours:!0})}static processDrawnLines({lines:t,pageWidth:e,pageHeight:i,rotation:s,innerMargin:n,mustSmooth:a,areContours:r}){s%180!=0&&([e,i]=[i,e]);const{curves:o,width:l,height:h}=t,d=t.thickness??0,c=[],u=Math.min(e/l,i/h),p=u/e,g=u/i,m=[];for(const{points:b}of o){const t=a?this.#no(b):b;if(!t)continue;m.push(t);const e=t.length,i=new Float32Array(e),s=new Float32Array(3*(2===e?2:e-2));if(c.push({line:s,points:i}),2===e){i[0]=t[0]*p,i[1]=t[1]*g,s.set([NaN,NaN,NaN,NaN,i[0],i[1]],0);continue}let[n,r,o,l]=t;n*=p,r*=g,o*=p,l*=g,i.set([n,r,o,l],0),s.set([NaN,NaN,NaN,NaN,n,r],0);for(let a=4;a<e;a+=2){const e=i[a]=t[a]*p,h=i[a+1]=t[a+1]*g;s.set(Bs.createBezierPoints(n,r,o,l,e,h),3*(a-2)),[n,r,o,l]=[o,l,e,h]}}if(0===c.length)return null;const f=r?new Zs:new Ys;return f.build(c,e,i,1,s,r?0:d,n),{outline:f,newCurves:m,areContours:r,thickness:d,width:l,height:h}}static async compressSignature({outlines:t,areContours:e,thickness:i,width:s,height:n}){let a,r=1/0,o=-1/0,l=0;for(const b of t){l+=b.length;for(let t=2,e=b.length;t<e;t++){const e=b[t]-b[t-2];r=Math.min(r,e),o=Math.max(o,e)}}a=r>=-128&&o<=127?Int8Array:r>=-32768&&o<=32767?Int16Array:Int32Array;const h=t.length,d=8+3*h,c=new Uint32Array(d);let u=0;c[u++]=d*Uint32Array.BYTES_PER_ELEMENT+(l-2*h)*a.BYTES_PER_ELEMENT,c[u++]=0,c[u++]=s,c[u++]=n,c[u++]=e?0:1,c[u++]=Math.max(0,Math.floor(i??0)),c[u++]=h,c[u++]=a.BYTES_PER_ELEMENT;for(const b of t)c[u++]=b.length-2,c[u++]=b[0],c[u++]=b[1];const p=new CompressionStream("deflate-raw"),g=p.writable.getWriter();await g.ready,g.write(c);const m=a.prototype.constructor;for(const b of t){const t=new m(b.length-2);for(let e=2,i=b.length;e<i;e++)t[e-2]=b[e]-b[e-2];g.write(t)}g.close();const f=await new Response(p.readable).arrayBuffer();return ut(new Uint8Array(f))}static async decompressSignature(t){try{const i=(e=t,Uint8Array.fromBase64?Uint8Array.fromBase64(e):it(atob(e))),{readable:s,writable:n}=new DecompressionStream("deflate-raw"),a=n.getWriter();await a.ready,a.write(i).then((async()=>{await a.ready,await a.close()})).catch((()=>{}));let r=null,o=0;for await(const t of s)r||=new Uint8Array(new Uint32Array(t.buffer,0,4)[0]),r.set(t,o),o+=t.length;const l=new Uint32Array(r.buffer,0,r.length>>2),h=l[1];if(0!==h)throw new Error(`Invalid version: ${h}`);const d=l[2],c=l[3],u=0===l[4],p=l[5],g=l[6],m=l[7],f=[],b=(8+3*g)*Uint32Array.BYTES_PER_ELEMENT;let v;switch(m){case Int8Array.BYTES_PER_ELEMENT:v=new Int8Array(r.buffer,b);break;case Int16Array.BYTES_PER_ELEMENT:v=new Int16Array(r.buffer,b);break;case Int32Array.BYTES_PER_ELEMENT:v=new Int32Array(r.buffer,b)}o=0;for(let t=0;t<g;t++){const e=l[3*t+8],i=new Float32Array(e+2);f.push(i);for(let s=0;s<2;s++)i[s]=l[3*t+8+s+1];for(let t=0;t<e;t++)i[t+2]=i[t]+v[o++]}return{areContours:u,thickness:p,outlines:f,width:d,height:c}}catch(i){return G(),null}var e}}class en extends qs{constructor(){super(),super.updateProperties({fill:Vt._defaultLineColor,"stroke-width":0})}clone(){const t=new en;return t.updateAll(this),t}}class sn extends Qs{constructor(t){super(t),super.updateProperties({stroke:Vt._defaultLineColor,"stroke-width":1})}clone(){const t=new sn(this._viewParameters);return t.updateAll(this),t}}class nn extends Xs{#do=!1;#co=null;#uo=null;#po=null;static _type="signature";static _editorType=m.SIGNATURE;static _defaultDrawingOptions=null;constructor(t){super({...t,mustBeCommitted:!0,name:"signatureEditor"}),this._willKeepAspectRatio=!0,this.#uo=t.signatureData||null,this.#co=null,this.defaultL10nId="pdfjs-editor-signature-editor1"}static initialize(t,e){Vt.initialize(t,e),this._defaultDrawingOptions=new en,this._defaultDrawnSignatureOptions=new sn(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();return e.updateProperties(t),e}static get supportMultipleDrawings(){return!1}static get typesMap(){return q(this,"typesMap",new Map)}static get isDrawer(){return!1}get telemetryFinalData(){return{type:"signature",hasDescription:!!this.#co}}static computeTelemetryFinalData(t){const e=t.get("hasDescription");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}get isResizable(){return!0}onScaleChanging(){null!==this._drawId&&super.onScaleChanging()}render(){if(this.div)return this.div;let t,e;const{_isCopy:i}=this;if(i&&(this._isCopy=!1,t=this.x,e=this.y),super.render(),null===this._drawId)if(this.#uo){const{lines:t,mustSmooth:e,areContours:i,description:s,uuid:n,heightInPage:a}=this.#uo,{rawDims:{pageWidth:r,pageHeight:o},rotation:l}=this.parent.viewport,h=tn.processDrawnLines({lines:t,pageWidth:r,pageHeight:o,rotation:l,innerMargin:nn._INNER_MARGIN,mustSmooth:e,areContours:i});this.addSignature(h,a,s,n)}else this.div.setAttribute("data-l10n-args",JSON.stringify({description:""})),this.div.hidden=!0,this._uiManager.getSignature(this);return i&&(this._isCopy=!0,this._moveAfterPaste(t,e)),this.div}setUuid(t){this.#po=t,this.addEditToolbar()}getUuid(){return this.#po}get description(){return this.#co}set description(t){this.#co=t,super.addEditToolbar().then((e=>{e?.updateEditSignatureButton(t)}))}getSignaturePreview(){const{newCurves:t,areContours:e,thickness:i,width:s,height:n}=this.#uo,a=Math.max(s,n);return{areContours:e,outline:tn.processDrawnLines({lines:{curves:t.map((t=>({points:t}))),thickness:i,width:s,height:n},pageWidth:a,pageHeight:a,rotation:0,innerMargin:0,mustSmooth:!1,areContours:e}).outline}}async addEditToolbar(){const t=await super.addEditToolbar();return t?(this._uiManager.signatureManager&&null!==this.#co&&(await t.addEditSignatureButton(this._uiManager.signatureManager,this.#po,this.#co),t.show()),t):null}addSignature(t,e,i,s){const{x:n,y:a}=this,{outline:r}=this.#uo=t;let o;this.#do=r instanceof Zs,this.#co=i,this.div.setAttribute("data-l10n-args",JSON.stringify({description:i})),this.#do?o=nn.getDefaultDrawingOptions():(o=nn._defaultDrawnSignatureOptions.clone(),o.updateProperties({"stroke-width":r.thickness})),this._addOutlines({drawOutlines:r,drawingOptions:o});const[l,h]=this.parentDimensions,[,d]=this.pageDimensions;let c=e/d;c=c>=1?.5:c,this.width*=c/this.height,this.width>=1&&(c*=.9/this.width,this.width=.9),this.height=c,this.setDims(l*this.width,h*this.height),this.x=n,this.y=a,this.center(),this._onResized(),this.onScaleChanging(),this.rotate(),this._uiManager.addToAnnotationStorage(this),this.setUuid(s),this._reportTelemetry({action:"pdfjs.signature.inserted",data:{hasBeenSaved:!!s,hasDescription:!!i}}),this.div.hidden=!1}getFromImage(t){const{rawDims:{pageWidth:e,pageHeight:i},rotation:s}=this.parent.viewport;return tn.process(t,e,i,s,nn._INNER_MARGIN)}getFromText(t,e){const{rawDims:{pageWidth:i,pageHeight:s},rotation:n}=this.parent.viewport;return tn.extractContoursFromText(t,e,i,s,n,nn._INNER_MARGIN)}getDrawnSignature(t){const{rawDims:{pageWidth:e,pageHeight:i},rotation:s}=this.parent.viewport;return tn.processDrawnLines({lines:t,pageWidth:e,pageHeight:i,rotation:s,innerMargin:nn._INNER_MARGIN,mustSmooth:!1,areContours:!1})}createDrawingOptions({areContours:t,thickness:e}){t?this._drawingOptions=nn.getDefaultDrawingOptions():(this._drawingOptions=nn._defaultDrawnSignatureOptions.clone(),this._drawingOptions.updateProperties({"stroke-width":e}))}serialize(t=!1){if(this.isEmpty())return null;const{lines:e,points:i,rect:s}=this.serializeDraw(t),{_drawingOptions:{"stroke-width":n}}=this,a={annotationType:m.SIGNATURE,isSignature:!0,areContours:this.#do,color:[0,0,0],thickness:this.#do?0:n,pageIndex:this.pageIndex,rect:s,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(a.paths={lines:e,points:i},a.uuid=this.#po,a.isCopy=!0):a.lines=e,this.#co&&(a.accessibilityData={type:"Figure",alt:this.#co}),a}static deserializeDraw(t,e,i,s,n,a){return a.areContours?Zs.deserialize(t,e,i,s,n,a):Ys.deserialize(t,e,i,s,n,a)}static async deserialize(t,e,i){const s=await super.deserialize(t,e,i);return s.#do=t.areContours,s.#co=t.accessibilityData?.alt||"",s.#po=t.uuid,s}}class an extends Vt{#go=null;#mo=null;#fo=null;#bo=null;#vo=null;#Ao="";#wo=null;#yo=!1;#_o=null;#xo=!1;#So=!1;static _type="stamp";static _editorType=m.STAMP;constructor(t){super({...t,name:"stampEditor"}),this.#bo=t.bitmapUrl,this.#vo=t.bitmapFile,this.defaultL10nId="pdfjs-editor-stamp-editor"}static initialize(t,e){Vt.initialize(t,e)}static isHandlingMimeForPasting(t){return Rt.includes(t)}static paste(t,e){e.pasteEditor({mode:m.STAMP},{bitmapFile:t.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1),super.altTextFinish()}get telemetryFinalData(){return{type:"stamp",hasAltText:!!this.altTextData?.altText}}static computeTelemetryFinalData(t){const e=t.get("hasAltText");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}#Eo(t,e=!1){t?(this.#go=t.bitmap,e||(this.#mo=t.id,this.#xo=t.isSvg),t.file&&(this.#Ao=t.file.name),this.#Co()):this.remove()}#To(){if(this.#fo=null,this._uiManager.enableWaiting(!1),this.#wo){if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#go)return this._editToolbar.hide(),void this._uiManager.editAltText(this,!0);if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#go){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch{}}this.div.focus()}}async mlGuessAltText(t=null,e=!0){if(this.hasAltTextData())return null;const{mlManager:i}=this._uiManager;if(!i)throw new Error("No ML.");if(!await i.isEnabledFor("altText"))throw new Error("ML isn't enabled for alt text.");const{data:s,width:n,height:a}=t||this.copyCanvas(null,null,!0).imageData,r=await i.guess({name:"altText",request:{data:s,width:n,height:a,channels:s.length/(n*a)}});if(!r)throw new Error("No response from the AI service.");if(r.error)throw new Error("Error from the AI service.");if(r.cancel)return null;if(!r.output)throw new Error("No valid response from the AI service.");const o=r.output;return await this.setGuessedAltText(o),e&&!this.hasAltTextData()&&(this.altTextData={alt:o,decorative:!1}),o}#Mo(){if(this.#mo)return this._uiManager.enableWaiting(!0),void this._uiManager.imageManager.getFromId(this.#mo).then((t=>this.#Eo(t,!0))).finally((()=>this.#To()));if(this.#bo){const t=this.#bo;return this.#bo=null,this._uiManager.enableWaiting(!0),void(this.#fo=this._uiManager.imageManager.getFromUrl(t).then((t=>this.#Eo(t))).finally((()=>this.#To())))}if(this.#vo){const t=this.#vo;return this.#vo=null,this._uiManager.enableWaiting(!0),void(this.#fo=this._uiManager.imageManager.getFromFile(t).then((t=>this.#Eo(t))).finally((()=>this.#To())))}const t=document.createElement("input");t.type="file",t.accept=Rt.join(",");const e=this._uiManager._signal;this.#fo=new Promise((i=>{t.addEventListener("change",(async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);const e=await this._uiManager.imageManager.getFromFile(t.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}}),this.#Eo(e)}else this.remove();i()}),{signal:e}),t.addEventListener("cancel",(()=>{this.remove(),i()}),{signal:e})})).finally((()=>this.#To())),t.click()}remove(){this.#mo&&(this.#go=null,this._uiManager.imageManager.deleteId(this.#mo),this.#wo?.remove(),this.#wo=null,this.#_o&&(clearTimeout(this.#_o),this.#_o=null)),super.remove()}rebuild(){this.parent?(super.rebuild(),null!==this.div&&(this.#mo&&null===this.#wo&&this.#Mo(),this.isAttachedToDOM||this.parent.add(this))):this.#mo&&this.#Mo()}onceAdded(t){this._isDraggable=!0,t&&this.div.focus()}isEmpty(){return!(this.#fo||this.#go||this.#bo||this.#vo||this.#mo||this.#yo)}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;return this._isCopy&&(t=this.x,e=this.y),super.render(),this.div.hidden=!0,this.addAltTextButton(),this.#yo||(this.#go?this.#Co():this.#Mo()),this._isCopy&&this._moveAfterPaste(t,e),this._uiManager.addShouldRescale(this),this.div}setCanvas(t,e){const{id:i,bitmap:s}=this._uiManager.imageManager.getFromCanvas(t,e);e.remove(),i&&this._uiManager.imageManager.isValidId(i)&&(this.#mo=i,s&&(this.#go=s),this.#yo=!1,this.#Co())}_onResized(){this.onScaleChanging()}onScaleChanging(){if(!this.parent)return;null!==this.#_o&&clearTimeout(this.#_o);this.#_o=setTimeout((()=>{this.#_o=null,this.#Po()}),200)}#Co(){const{div:t}=this;let{width:e,height:i}=this.#go;const[s,n]=this.pageDimensions,a=.75;if(this.width)e=this.width*s,i=this.height*n;else if(e>a*s||i>a*n){const t=Math.min(a*s/e,a*n/i);e*=t,i*=t}const[r,o]=this.parentDimensions;this.setDims(e*r/s,i*o/n),this._uiManager.enableWaiting(!1);const l=this.#wo=document.createElement("canvas");l.setAttribute("role","img"),this.addContainer(l),this.width=e/s,this.height=i/n,this._initialOptions?.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&!this.annotationElementId||(t.hidden=!1),this.#Po(),this.#So||(this.parent.addUndoableEditor(this),this.#So=!0),this._reportTelemetry({action:"inserted_image"}),this.#Ao&&this.div.setAttribute("aria-description",this.#Ao)}copyCanvas(t,e,i=!1){t||(t=224);const{width:s,height:n}=this.#go,a=new Dt;let r=this.#go,o=s,l=n,h=null;if(e){if(s>e||n>e){const t=Math.min(e/s,e/n);o=Math.floor(s*t),l=Math.floor(n*t)}h=document.createElement("canvas");const t=h.width=Math.ceil(o*a.sx),i=h.height=Math.ceil(l*a.sy);this.#xo||(r=this.#Io(t,i));const d=h.getContext("2d");d.filter=this._uiManager.hcmFilter;let c="white",u="#cfcfd8";"none"!==this._uiManager.hcmFilter?u="black":window.matchMedia?.("(prefers-color-scheme: dark)").matches&&(c="#8f8f9d",u="#42414d");const p=15,g=p*a.sx,m=p*a.sy,f=new OffscreenCanvas(2*g,2*m),b=f.getContext("2d");b.fillStyle=c,b.fillRect(0,0,2*g,2*m),b.fillStyle=u,b.fillRect(0,0,g,m),b.fillRect(g,m,g,m),d.fillStyle=d.createPattern(f,"repeat"),d.fillRect(0,0,t,i),d.drawImage(r,0,0,r.width,r.height,0,0,t,i)}let d=null;if(i){let e,i;if(a.symmetric&&r.width<t&&r.height<t)e=r.width,i=r.height;else if(r=this.#go,s>t||n>t){const a=Math.min(t/s,t/n);e=Math.floor(s*a),i=Math.floor(n*a),this.#xo||(r=this.#Io(e,i))}const o=new OffscreenCanvas(e,i).getContext("2d",{willReadFrequently:!0});o.drawImage(r,0,0,r.width,r.height,0,0,e,i),d={width:e,height:i,data:o.getImageData(0,0,e,i).data}}return{canvas:h,width:o,height:l,imageData:d}}#Io(t,e){const{width:i,height:s}=this.#go;let n=i,a=s,r=this.#go;for(;n>2*t||a>2*e;){const i=n,s=a;n>2*t&&(n=n>=16384?Math.floor(n/2)-1:Math.ceil(n/2)),a>2*e&&(a=a>=16384?Math.floor(a/2)-1:Math.ceil(a/2));const o=new OffscreenCanvas(n,a);o.getContext("2d").drawImage(r,0,0,i,s,0,0,n,a),r=o.transferToImageBitmap()}return r}#Po(){const[t,e]=this.parentDimensions,{width:i,height:s}=this,n=new Dt,a=Math.ceil(i*t*n.sx),r=Math.ceil(s*e*n.sy),o=this.#wo;if(!o||o.width===a&&o.height===r)return;o.width=a,o.height=r;const l=this.#xo?this.#go:this.#Io(a,r),h=o.getContext("2d");h.filter=this._uiManager.hcmFilter,h.drawImage(l,0,0,l.width,l.height,0,0,a,r)}#ko(t){if(t){if(this.#xo){const t=this._uiManager.imageManager.getSvgUrl(this.#mo);if(t)return t}const t=document.createElement("canvas");({width:t.width,height:t.height}=this.#go);return t.getContext("2d").drawImage(this.#go,0,0),t.toDataURL()}if(this.#xo){const[t,e]=this.pageDimensions,i=Math.round(this.width*t*gt.PDF_TO_CSS_UNITS),s=Math.round(this.height*e*gt.PDF_TO_CSS_UNITS),n=new OffscreenCanvas(i,s);return n.getContext("2d").drawImage(this.#go,0,0,this.#go.width,this.#go.height,0,0,i,s),n.transferToImageBitmap()}return structuredClone(this.#go)}static async deserialize(t,e,i){let s=null,n=!1;if(t instanceof Rs){const{data:{rect:a,rotation:r,id:o,structParent:l,popupRef:h},container:d,parent:{page:{pageNumber:c}},canvas:u}=t;let p,g;u?(delete t.canvas,({id:p,bitmap:g}=i.imageManager.getFromCanvas(d.id,u)),u.remove()):(n=!0,t._hasNoCanvas=!0);const f=(await e._structTree.getAriaAttributes(`${dt}${o}`))?.get("aria-label")||"";s=t={annotationType:m.STAMP,bitmapId:p,bitmap:g,pageIndex:c-1,rect:a.slice(0),rotation:r,id:o,deleted:!1,accessibilityData:{decorative:!1,altText:f},isSvg:!1,structParent:l,popupRef:h}}const a=await super.deserialize(t,e,i),{rect:r,bitmap:o,bitmapUrl:l,bitmapId:h,isSvg:d,accessibilityData:c}=t;n?(i.addMissingCanvas(t.id,a),a.#yo=!0):h&&i.imageManager.isValidId(h)?(a.#mo=h,o&&(a.#go=o)):a.#bo=l,a.#xo=d;const[u,p]=a.pageDimensions;return a.width=(r[2]-r[0])/u,a.height=(r[3]-r[1])/p,a.annotationElementId=t.id||null,c&&(a.altTextData=c),a._initialData=s,a.#So=!!s,a}serialize(t=!1,e=null){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const i={annotationType:m.STAMP,bitmapId:this.#mo,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#xo,structTreeParentId:this._structTreeParentId};if(t)return i.bitmapUrl=this.#ko(!0),i.accessibilityData=this.serializeAltText(!0),i.isCopy=!0,i;const{decorative:s,altText:n}=this.serializeAltText(!1);if(!s&&n&&(i.accessibilityData={type:"Figure",alt:n}),this.annotationElementId){const t=this.#ta(i);if(t.isSame)return null;t.isSameAltText?delete i.accessibilityData:i.accessibilityData.structParent=this._initialData.structParent??-1}if(i.id=this.annotationElementId,null===e)return i;e.stamps||=new Map;const a=this.#xo?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(e.stamps.has(this.#mo)){if(this.#xo){const t=e.stamps.get(this.#mo);a>t.area&&(t.area=a,t.serialized.bitmap.close(),t.serialized.bitmap=this.#ko(!1))}}else e.stamps.set(this.#mo,{area:a,serialized:i}),i.bitmap=this.#ko(!1);return i}#ta(t){const{pageIndex:e,accessibilityData:{altText:i}}=this._initialData,s=t.pageIndex===e,n=(t.accessibilityData?.alt||"")===i;return{isSame:!this._hasBeenMoved&&!this._hasBeenResized&&s&&n,isSameAltText:n}}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}}class rn{#On;#Do=!1;#Ro=null;#Lo=null;#Fo=null;#No=new Map;#Oo=!1;#Bo=!1;#zo=!1;#Ho=null;#Uo=null;#Go=null;#$o=null;#f;static _initialized=!1;static#G=new Map([Os,Js,an,Ws,nn].map((t=>[t._editorType,t])));constructor({uiManager:t,pageIndex:e,div:i,structTreeLayer:s,accessibilityManager:n,annotationLayer:a,drawLayer:r,textLayer:o,viewport:l,l10n:h}){const d=[...rn.#G.values()];if(!rn._initialized){rn._initialized=!0;for(const e of d)e.initialize(h,t)}t.registerEditorTypes(d),this.#f=t,this.pageIndex=e,this.div=i,this.#On=n,this.#Ro=a,this.viewport=l,this.#Go=o,this.drawLayer=r,this._structTree=s,this.#f.addLayer(this)}get isEmpty(){return 0===this.#No.size}get isInvisible(){return this.isEmpty&&this.#f.getMode()===m.NONE}updateToolbar(t){this.#f.updateToolbar(t)}updateMode(t=this.#f.getMode()){switch(this.#jo(),t){case m.NONE:return this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),void this.disableClick();case m.INK:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick();break;case m.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const i of rn.#G.values())e.toggle(`${i._type}Editing`,t===i._editorType);this.div.hidden=!1}hasTextLayer(t){return t===this.#Go?.div}setEditingState(t){this.#f.setEditingState(t)}addCommands(t){this.#f.addCommands(t)}cleanUndoStack(t){this.#f.cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){this.#Ro?.div.classList.toggle("disabled",!t)}async enable(){this.#zo=!0,this.div.tabIndex=0,this.togglePointerEvents(!0);const t=new Set;for(const i of this.#No.values())i.enableEditing(),i.show(!0),i.annotationElementId&&(this.#f.removeChangedExistingAnnotation(i),t.add(i.annotationElementId));if(!this.#Ro)return void(this.#zo=!1);const e=this.#Ro.getEditableAnnotations();for(const i of e){if(i.hide(),this.#f.isDeletedAnnotationElement(i.data.id))continue;if(t.has(i.data.id))continue;const e=await this.deserialize(i);e&&(this.addOrRebuild(e),e.enableEditing())}this.#zo=!1}disable(){this.#Bo=!0,this.div.tabIndex=-1,this.togglePointerEvents(!1);const t=new Map,e=new Map;for(const s of this.#No.values())s.disableEditing(),s.annotationElementId&&(null===s.serialize()?(e.set(s.annotationElementId,s),this.getEditableAnnotation(s.annotationElementId)?.show(),s.remove()):t.set(s.annotationElementId,s));if(this.#Ro){const i=this.#Ro.getEditableAnnotations();for(const s of i){const{id:i}=s.data;if(this.#f.isDeletedAnnotationElement(i))continue;let n=e.get(i);n?(n.resetAnnotationElement(s),n.show(!1),s.show()):(n=t.get(i),n&&(this.#f.addChangedExistingAnnotation(n),n.renderAnnotationElement(s)&&n.show(!1)),s.show())}}this.#jo(),this.isEmpty&&(this.div.hidden=!0);const{classList:i}=this.div;for(const s of rn.#G.values())i.remove(`${s._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),this.#Bo=!1}getEditableAnnotation(t){return this.#Ro?.getEditableAnnotation(t)||null}setActiveEditor(t){this.#f.getActive()!==t&&this.#f.setActiveEditor(t)}enableTextSelection(){if(this.div.tabIndex=-1,this.#Go?.div&&!this.#$o){this.#$o=new AbortController;const t=this.#f.combinedSignal(this.#$o);this.#Go.div.addEventListener("pointerdown",this.#Vo.bind(this),{signal:t}),this.#Go.div.classList.add("highlighting")}}disableTextSelection(){this.div.tabIndex=0,this.#Go?.div&&this.#$o&&(this.#$o.abort(),this.#$o=null,this.#Go.div.classList.remove("highlighting"))}#Vo(t){this.#f.unselectAll();const{target:e}=t;if(e===this.#Go.div||("img"===e.getAttribute("role")||e.classList.contains("endOfContent"))&&this.#Go.div.contains(e)){const{isMac:e}=st.platform;if(0!==t.button||t.ctrlKey&&e)return;this.#f.showAllEditors("highlight",!0,!0),this.#Go.div.classList.add("free"),this.toggleDrawing(),Ws.startHighlighting(this,"ltr"===this.#f.direction,{target:this.#Go.div,x:t.x,y:t.y}),this.#Go.div.addEventListener("pointerup",(()=>{this.#Go.div.classList.remove("free"),this.toggleDrawing(!0)}),{once:!0,signal:this.#f._signal}),t.preventDefault()}}enableClick(){if(this.#Lo)return;this.#Lo=new AbortController;const t=this.#f.combinedSignal(this.#Lo);this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});const e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t}),this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){this.#Lo?.abort(),this.#Lo=null}attach(t){this.#No.set(t.id,t);const{annotationElementId:e}=t;e&&this.#f.isDeletedAnnotationElement(e)&&this.#f.removeDeletedAnnotationElement(t)}detach(t){this.#No.delete(t.id),this.#On?.removePointerInTextLayer(t.contentDiv),!this.#Bo&&t.annotationElementId&&this.#f.addDeletedAnnotationElement(t)}remove(t){this.detach(t),this.#f.removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1}changeParent(t){t.parent!==this&&(t.parent&&t.annotationElementId&&(this.#f.addDeletedAnnotationElement(t.annotationElementId),Vt.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),t.parent?.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(t.parent!==this||!t.isAttachedToDOM){if(this.changeParent(t),this.#f.addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(!this.#zo),this.#f.addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;t.div.contains(e)&&!this.#Fo&&(t._focusEventsAllowed=!1,this.#Fo=setTimeout((()=>{this.#Fo=null,t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this.#f._signal}),e.focus())}),0)),t._structTreeParentId=this.#On?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||=this,t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return this.#f.getId()}get#Wo(){return rn.#G.get(this.#f.getMode())}combinedSignal(t){return this.#f.combinedSignal(t)}#qo(t){const e=this.#Wo;return e?new e.prototype.constructor(t):null}canCreateNewEmptyEditor(){return this.#Wo?.canCreateNewEmptyEditor()}async pasteEditor(t,e){this.updateToolbar(t),await this.#f.updateMode(t.mode);const{offsetX:i,offsetY:s}=this.#Xo(),n=this.getNextId(),a=this.#qo({parent:this,id:n,x:i,y:s,uiManager:this.#f,isCentered:!0,...e});a&&this.add(a)}async deserialize(t){return await(rn.#G.get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,this.#f))||null}createAndAddNewEditor(t,e,i={}){const s=this.getNextId(),n=this.#qo({parent:this,id:s,x:t.offsetX,y:t.offsetY,uiManager:this.#f,isCentered:e,...i});return n&&this.add(n),n}#Xo(){const{x:t,y:e,width:i,height:s}=this.div.getBoundingClientRect(),n=Math.max(0,t),a=Math.max(0,e),r=(n+Math.min(window.innerWidth,t+i))/2-t,o=(a+Math.min(window.innerHeight,e+s))/2-e,[l,h]=this.viewport.rotation%180==0?[r,o]:[o,r];return{offsetX:l,offsetY:h}}addNewEditor(t={}){this.createAndAddNewEditor(this.#Xo(),!0,t)}setSelected(t){this.#f.setSelected(t)}toggleSelected(t){this.#f.toggleSelected(t)}unselect(t){this.#f.unselect(t)}pointerup(t){const{isMac:e}=st.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;if(!this.#Oo)return;if(this.#Oo=!1,this.#Wo?.isDrawer&&this.#Wo.supportMultipleDrawings)return;if(!this.#Do)return void(this.#Do=!0);const i=this.#f.getMode();i!==m.STAMP&&i!==m.SIGNATURE?this.createAndAddNewEditor(t,!1):this.#f.unselectAll()}pointerdown(t){if(this.#f.getMode()===m.HIGHLIGHT&&this.enableTextSelection(),this.#Oo)return void(this.#Oo=!1);const{isMac:e}=st.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;if(this.#Oo=!0,this.#Wo?.isDrawer)return void this.startDrawingSession(t);const i=this.#f.getActive();this.#Do=!i||i.isEmpty()}startDrawingSession(t){if(this.div.focus({preventScroll:!0}),this.#Ho)return void this.#Wo.startDrawing(this,this.#f,!1,t);this.#f.setCurrentDrawingSession(this),this.#Ho=new AbortController;const e=this.#f.combinedSignal(this.#Ho);this.div.addEventListener("blur",(({relatedTarget:t})=>{t&&!this.div.contains(t)&&(this.#Uo=null,this.commitOrRemove())}),{signal:e}),this.#Wo.startDrawing(this,this.#f,!1,t)}pause(t){if(t){const{activeElement:t}=document;this.div.contains(t)&&(this.#Uo=t)}else this.#Uo&&setTimeout((()=>{this.#Uo?.focus(),this.#Uo=null}),0)}endDrawingSession(t=!1){return this.#Ho?(this.#f.setCurrentDrawingSession(null),this.#Ho.abort(),this.#Ho=null,this.#Uo=null,this.#Wo.endDrawing(t)):null}findNewParent(t,e,i){const s=this.#f.findParent(e,i);return null!==s&&s!==this&&(s.changeParent(t),!0)}commitOrRemove(){return!!this.#Ho&&(this.endDrawingSession(),!0)}onScaleChanging(){this.#Ho&&this.#Wo.onScaleChangingWhenDrawing(this)}destroy(){this.commitOrRemove(),this.#f.getActive()?.parent===this&&(this.#f.commitOrRemove(),this.#f.setActiveEditor(null)),this.#Fo&&(clearTimeout(this.#Fo),this.#Fo=null);for(const t of this.#No.values())this.#On?.removePointerInTextLayer(t.contentDiv),t.setParent(null),t.isAttachedToDOM=!1,t.div.remove();this.div=null,this.#No.clear(),this.#f.removeLayer(this)}#jo(){for(const t of this.#No.values())t.isEmpty()&&t.remove()}render({viewport:t}){this.viewport=t,kt(this.div,t);for(const e of this.#f.getEditors(this.pageIndex))this.add(e),e.rebuild();this.updateMode()}update({viewport:t}){this.#f.commitOrRemove(),this.#jo();const e=this.viewport.rotation,i=t.rotation;if(this.viewport=t,kt(this.div,{rotation:i}),e!==i)for(const s of this.#No.values())s.rotate(i)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return this.#f.viewParameters.realScale}}class on{#mn=null;#Ko=new Map;#Yo=new Map;static#w=0;constructor({pageIndex:t}){this.pageIndex=t}setParent(t){if(this.#mn){if(this.#mn!==t){if(this.#Ko.size>0)for(const e of this.#Ko.values())e.remove(),t.append(e);this.#mn=t}}else this.#mn=t}static get _svgFactory(){return q(this,"_svgFactory",new ns)}static#Qo(t,[e,i,s,n]){const{style:a}=t;a.top=100*i+"%",a.left=100*e+"%",a.width=100*s+"%",a.height=100*n+"%"}#Jo(){const t=on._svgFactory.create(1,1,!0);return this.#mn.append(t),t.setAttribute("aria-hidden",!0),t}#Zo(t,e){const i=on._svgFactory.createElement("clipPath");t.append(i);const s=`clip_${e}`;i.setAttribute("id",s),i.setAttribute("clipPathUnits","objectBoundingBox");const n=on._svgFactory.createElement("use");return i.append(n),n.setAttribute("href",`#${e}`),n.classList.add("clip"),s}#tl(t,e){for(const[i,s]of Object.entries(e))null===s?t.removeAttribute(i):t.setAttribute(i,s)}draw(t,e=!1,i=!1){const s=on.#w++,n=this.#Jo(),a=on._svgFactory.createElement("defs");n.append(a);const r=on._svgFactory.createElement("path");a.append(r);const o=`path_p${this.pageIndex}_${s}`;r.setAttribute("id",o),r.setAttribute("vector-effect","non-scaling-stroke"),e&&this.#Yo.set(s,r);const l=i?this.#Zo(a,o):null,h=on._svgFactory.createElement("use");return n.append(h),h.setAttribute("href",`#${o}`),this.updateProperties(n,t),this.#Ko.set(s,n),{id:s,clipPathId:`url(#${l})`}}drawOutline(t,e){const i=on.#w++,s=this.#Jo(),n=on._svgFactory.createElement("defs");s.append(n);const a=on._svgFactory.createElement("path");n.append(a);const r=`path_p${this.pageIndex}_${i}`;let o;if(a.setAttribute("id",r),a.setAttribute("vector-effect","non-scaling-stroke"),e){const t=on._svgFactory.createElement("mask");n.append(t),o=`mask_p${this.pageIndex}_${i}`,t.setAttribute("id",o),t.setAttribute("maskUnits","objectBoundingBox");const e=on._svgFactory.createElement("rect");t.append(e),e.setAttribute("width","1"),e.setAttribute("height","1"),e.setAttribute("fill","white");const s=on._svgFactory.createElement("use");t.append(s),s.setAttribute("href",`#${r}`),s.setAttribute("stroke","none"),s.setAttribute("fill","black"),s.setAttribute("fill-rule","nonzero"),s.classList.add("mask")}const l=on._svgFactory.createElement("use");s.append(l),l.setAttribute("href",`#${r}`),o&&l.setAttribute("mask",`url(#${o})`);const h=l.cloneNode();return s.append(h),l.classList.add("mainOutline"),h.classList.add("secondaryOutline"),this.updateProperties(s,t),this.#Ko.set(i,s),i}finalizeDraw(t,e){this.#Yo.delete(t),this.updateProperties(t,e)}updateProperties(t,e){if(!e)return;const{root:i,bbox:s,rootClass:n,path:a}=e,r="number"==typeof t?this.#Ko.get(t):t;if(r){if(i&&this.#tl(r,i),s&&on.#Qo(r,s),n){const{classList:t}=r;for(const[e,i]of Object.entries(n))t.toggle(e,i)}if(a){const t=r.firstChild.firstChild;this.#tl(t,a)}}}updateParent(t,e){if(e===this)return;const i=this.#Ko.get(t);i&&(e.#mn.append(i),this.#Ko.delete(t),e.#Ko.set(t,i))}remove(t){this.#Yo.delete(t),null!==this.#mn&&(this.#Ko.get(t).remove(),this.#Ko.delete(t))}destroy(){this.#mn=null;for(const t of this.#Ko.values())t.remove();this.#Ko.clear(),this.#Yo.clear()}}globalThis._pdfjsTestingUtils={HighlightOutliner:Us},globalThis.pdfjsLib={AbortException:tt,AnnotationEditorLayer:rn,AnnotationEditorParamsType:f,AnnotationEditorType:m,AnnotationEditorUIManager:Gt,AnnotationLayer:Fs,AnnotationMode:g,AnnotationType:E,build:Zi,ColorPicker:Vs,createValidAbsoluteUrl:V,DOMSVGFactory:ns,DrawLayer:on,FeatureTest:st,fetchData:mt,getDocument:$i,getFilenameFromUrl:wt,getPdfFilenameFromUrl:yt,getUuid:ht,getXfaPageViewport:Tt,GlobalWorkerOptions:ui,ImageKind:S,InvalidPDFException:Q,isDataScheme:vt,isPdfFile:At,isValidExplicitDest:ne,MathClamp:ct,noContextMenu:St,normalizeUnicode:lt,OPS:D,OutputScale:Dt,PasswordResponses:O,PDFDataRangeTransport:Vi,PDFDateString:Ct,PDFWorker:Xi,PermissionFlag:b,PixelsPerInch:gt,RenderingCancelledException:bt,ResponseException:J,setLayerDimensions:kt,shadow:q,SignatureExtractor:tn,stopEvent:Et,SupportedImageMimeTypes:Rt,TextLayer:Ui,TouchManager:jt,updateUrlHash:W,Util:at,VerbosityLevel:k,version:Ji,XfaLayer:as}}}]);