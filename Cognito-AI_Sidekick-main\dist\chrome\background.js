(()=>{var e={140:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DIFF_STATUS_UPDATED=t.DIFF_STATUS_REMOVED=t.DIFF_STATUS_KEYS_UPDATED=t.DIFF_STATUS_ARRAY_UPDATED=void 0;t.DIFF_STATUS_UPDATED="updated",t.DIFF_STATUS_REMOVED="removed",t.DIFF_STATUS_KEYS_UPDATED="updated_keys",t.DIFF_STATUS_ARRAY_UPDATED="updated_array"},368:e=>{var t=9007199254740991,r="[object Arguments]",n="[object Function]",o="[object GeneratorFunction]",i=/^(?:0|[1-9]\d*)$/;var a=Object.prototype,c=a.hasOwnProperty,u=a.toString,s=a.propertyIsEnumerable,f=Math.max;function l(e,t){var n=v(e)||function(e){return function(e){return function(e){return!!e&&"object"==typeof e}(e)&&b(e)}(e)&&c.call(e,"callee")&&(!s.call(e,"callee")||u.call(e)==r)}(e)?function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}(e.length,String):[],o=n.length,i=!!o;for(var a in e)!t&&!c.call(e,a)||i&&("length"==a||h(a,o))||n.push(a);return n}function d(e,t,r){var n=e[t];c.call(e,t)&&y(n,r)&&(void 0!==r||t in e)||(e[t]=r)}function p(e){if(!g(e))return function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t}(e);var t,r,n,o=(r=(t=e)&&t.constructor,n="function"==typeof r&&r.prototype||a,t===n),i=[];for(var u in e)("constructor"!=u||!o&&c.call(e,u))&&i.push(u);return i}function h(e,r){return!!(r=null==r?t:r)&&("number"==typeof e||i.test(e))&&e>-1&&e%1==0&&e<r}function y(e,t){return e===t||e!=e&&t!=t}var v=Array.isArray;function b(e){return null!=e&&function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=t}(e.length)&&!function(e){var t=g(e)?u.call(e):"";return t==n||t==o}(e)}function g(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}var m,_,w,S=(m=function(e,t){!function(e,t,r,n){r||(r={});for(var o=-1,i=t.length;++o<i;){var a=t[o],c=n?n(r[a],e[a],a,r,e):void 0;d(r,a,void 0===c?e[a]:c)}}(t,function(e){return b(e)?l(e,!0):p(e)}(t),e)},_=function(e,t){var r=-1,n=t.length,o=n>1?t[n-1]:void 0,i=n>2?t[2]:void 0;for(o=m.length>3&&"function"==typeof o?(n--,o):void 0,i&&function(e,t,r){if(!g(r))return!1;var n=typeof t;return!!("number"==n?b(r)&&h(t,r.length):"string"==n&&t in r)&&y(r[t],e)}(t[0],t[1],i)&&(o=n<3?void 0:o,n=1),e=Object(e);++r<n;){var a=t[r];a&&m(e,a,r,o)}return e},w=f(void 0===w?_.length-1:w,0),function(){for(var e=arguments,t=-1,r=f(e.length-w,0),n=Array(r);++t<r;)n[t]=e[w+t];t=-1;for(var o=Array(w+1);++t<w;)o[t]=e[t];return o[w]=n,function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}(_,this,o)});e.exports=S},1732:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=u(r(368)),o=r(9529),i=r(7575),a=u(r(3807)),c=r(6183);function u(e){return e&&e.__esModule?e:{default:e}}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function f(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,l(n.key),n)}}function l(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}var d="\nLooks like there is an error in the background page. You might want to inspect your background page for more details.\n",p={channelName:o.DEFAULT_CHANNEL_NAME,state:{},serializer:i.noop,deserializer:i.noop,patchStrategy:a.default},h=function(){return e=function e(){var t=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p,n=r.channelName,a=void 0===n?p.channelName:n,u=r.state,s=void 0===u?p.state:u,f=r.serializer,l=void 0===f?p.serializer:f,d=r.deserializer,h=void 0===d?p.deserializer:d,y=r.patchStrategy,v=void 0===y?p.patchStrategy:y;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!a)throw new Error("channelName is required in options");if("function"!=typeof l)throw new Error("serializer must be a function");if("function"!=typeof h)throw new Error("deserializer must be a function");if("function"!=typeof v)throw new Error("patchStrategy must be one of the included patching strategies or a custom patching function");this.channelName=a,this.readyResolved=!1,this.readyPromise=new Promise((function(e){return t.readyResolve=e})),this.browserAPI=(0,c.getBrowserAPI)(),this.initializeStore=this.initializeStore.bind(this),this.browserAPI.runtime.sendMessage({type:o.FETCH_STATE_TYPE,channelName:a},void 0,this.initializeStore),this.deserializer=h,this.serializedPortListener=(0,i.withDeserializer)(h)((function(){var e;return(e=t.browserAPI.runtime.onMessage).addListener.apply(e,arguments)})),this.serializedMessageSender=(0,i.withSerializer)(l)((function(){var e;return(e=t.browserAPI.runtime).sendMessage.apply(e,arguments)})),this.listeners=[],this.state=s,this.patchStrategy=v,this.serializedPortListener((function(e){if(e&&e.channelName===t.channelName)switch(e.type){case o.STATE_TYPE:t.replaceState(e.payload),t.readyResolved||(t.readyResolved=!0,t.readyResolve());break;case o.PATCH_STATE_TYPE:t.patchState(e.payload)}}),(function(e){return Boolean(e)&&"string"==typeof e.type&&e.channelName===t.channelName})),this.dispatch=this.dispatch.bind(this),this.getState=this.getState.bind(this),this.subscribe=this.subscribe.bind(this)},t=[{key:"ready",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null!==e?this.readyPromise.then(e):this.readyPromise}},{key:"subscribe",value:function(e){var t=this;return this.listeners.push(e),function(){t.listeners=t.listeners.filter((function(t){return t!==e}))}}},{key:"patchState",value:function(e){this.state=this.patchStrategy(this.state,e),this.listeners.forEach((function(e){return e()}))}},{key:"replaceState",value:function(e){this.state=e,this.listeners.forEach((function(e){return e()}))}},{key:"getState",value:function(){return this.state}},{key:"replaceReducer",value:function(){}},{key:"dispatch",value:function(e){var t=this;return new Promise((function(r,i){t.serializedMessageSender({type:o.DISPATCH_TYPE,channelName:t.channelName,payload:e},null,(function(e){if(e){var o=e.error,a=e.value;if(o){var c=new Error("".concat(d).concat(o));i((0,n.default)(c,o))}else r(a&&a.payload)}else{var u=t.browserAPI.runtime.lastError,s=new Error("".concat(d).concat(u));i((0,n.default)(s,u))}}))}))}},{key:"initializeStore",value:function(e){e&&e.type===o.FETCH_STATE_TYPE&&(this.replaceState(e.payload),this.readyResolved||(this.readyResolved=!0,this.readyResolve()))}}],t&&f(e.prototype,t),r&&f(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,r}();t.default=h},3207:(e,t,r)=>{"use strict";Object.defineProperty(t,"nK",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(t,"Iq",{enumerable:!0,get:function(){return i.default}});var n=c(r(1732)),o=c(r(9449)),i=c(r(6745)),a=c(r(3988));function c(e){return e&&e.__esModule?e:{default:e}}},3807:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=Object.assign({},e);return t.forEach((function(e){var t=e.change,o=e.key,i=e.value;switch(t){case n.DIFF_STATUS_UPDATED:r[o]=i;break;case n.DIFF_STATUS_REMOVED:Reflect.deleteProperty(r,o)}})),r};var n=r(140)},3988:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=function(e){return function(){return function(t){return function(r){var n=e[r.type];return t(n?n(r):r)}}}}},6183:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getBrowserAPI=function(){var e;try{e=self.chrome||self.browser||browser}catch(t){e=browser}if(!e)throw new Error("Browser API is not present");return e}},6745:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,o=r(9529),i=r(7575),a=r(6183),c=(n=r(8642))&&n.__esModule?n:{default:n},u=r(8571);function s(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return f(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw i}}}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var l={channelName:o.DEFAULT_CHANNEL_NAME,dispatchResponder:function(e,t){Promise.resolve(e).then((function(e){t({error:null,value:e})})).catch((function(e){t({error:e.message,value:null})}))},serializer:i.noop,deserializer:i.noop,diffStrategy:c.default};t.default=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:l).channelName,t=void 0===e?l.channelName:e,r=(0,a.getBrowserAPI)(),c=function(e){return e.type===o.DISPATCH_TYPE&&e.channelName===t},f=(0,u.createDeferredListener)((function(e){return e.type===o.FETCH_STATE_TYPE&&e.channelName===t})),d=(0,u.createDeferredListener)(c);return r.runtime.onMessage.addListener(f.listener),r.runtime.onMessage.addListener(d.listener),function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l,u=a.dispatchResponder,p=void 0===u?l.dispatchResponder:u,h=a.serializer,y=void 0===h?l.serializer:h,v=a.deserializer,b=void 0===v?l.deserializer:v,g=a.diffStrategy,m=void 0===g?l.diffStrategy:g;if("function"!=typeof y)throw new Error("serializer must be a function");if("function"!=typeof b)throw new Error("deserializer must be a function");if("function"!=typeof m)throw new Error("diffStrategy must be one of the included diffing strategies or a custom diff function");var _=(0,i.withSerializer)(y)((function(){for(var e,t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];var i=function(){r.runtime.lastError};return(e=r.runtime).sendMessage.apply(e,n.concat([i])),r.tabs.query({},(function(e){var t,o=s(e);try{for(o.s();!(t=o.n()).done;){var a,c=t.value;(a=r.tabs).sendMessage.apply(a,[c.id].concat(n,[i]))}}catch(u){o.e(u)}finally{o.f()}}))})),w=e.getState();e.subscribe((function(){var r=e.getState(),n=m(w,r);n.length&&(w=r,_({type:o.PATCH_STATE_TYPE,payload:n,channelName:t}))})),_({type:o.STATE_TYPE,payload:w,channelName:t}),f.setListener((function(t,r,n){var i=e.getState();n({type:o.FETCH_STATE_TYPE,payload:i})})),(0,i.withDeserializer)(b)(d.setListener)((function(t,r,o){var i=Object.assign({},t.payload,{_sender:r}),a=null;try{a=e.dispatch(i)}catch(n){a=Promise.reject(n.message)}p(a,o)}),c)}}},7575:(e,t)=>{"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){i(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function i(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Object.defineProperty(t,"__esModule",{value:!0}),t.withSerializer=t.withDeserializer=t.noop=void 0;var a=t.noop=function(e){return e},c=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return o(o({},e),e.payload?{payload:t(e.payload)}:{})};t.withDeserializer=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a;return function(t){return function(r,n){return t(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,r=arguments.length>2?arguments[2]:void 0;return r?function(n){for(var o=arguments.length,i=new Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];return r.apply(void 0,[n].concat(i))?e.apply(void 0,[c(n,t)].concat(i)):e.apply(void 0,[n].concat(i))}:function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];return e.apply(void 0,[c(r,t)].concat(o))}}(r,e,n))}}},t.withSerializer=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a;return function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];if(o.length<=r)throw new Error("Message in request could not be serialized. "+"Expected message in position ".concat(r," but only received ").concat(o.length," args."));return o[r]=c(o[r],e),t.apply(void 0,o)}}}},8571:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createDeferredListener=void 0;t.createDeferredListener=function(e){var t=function(){},r=new Promise((function(e){return t=e}));return{setListener:t,listener:function(t,n,o){if(e(t,n,o))return r.then((function(e){e(t,n,o)})),!0}}}},8642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=[];return Object.keys(t).forEach((function(o){e[o]!==t[o]&&r.push({key:o,value:t[o],change:n.DIFF_STATUS_UPDATED})})),Object.keys(e).forEach((function(e){t.hasOwnProperty(e)||r.push({key:e,change:n.DIFF_STATUS_REMOVED})})),r};var n=r(140)},9448:function(e,t,r){!function(e){"use strict";function t(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}function n(e,t){Object.defineProperty(this,"kind",{value:e,enumerable:!0}),t&&t.length&&Object.defineProperty(this,"path",{value:t,enumerable:!0})}function o(e,t,r){o.super_.call(this,"E",e),Object.defineProperty(this,"lhs",{value:t,enumerable:!0}),Object.defineProperty(this,"rhs",{value:r,enumerable:!0})}function i(e,t){i.super_.call(this,"N",e),Object.defineProperty(this,"rhs",{value:t,enumerable:!0})}function a(e,t){a.super_.call(this,"D",e),Object.defineProperty(this,"lhs",{value:t,enumerable:!0})}function c(e,t,r){c.super_.call(this,"A",e),Object.defineProperty(this,"index",{value:t,enumerable:!0}),Object.defineProperty(this,"item",{value:r,enumerable:!0})}function u(e,t,r){var n=e.slice((r||t)+1||e.length);return e.length=t<0?e.length+t:t,e.push.apply(e,n),e}function s(e){var t=void 0===e?"undefined":N(e);return"object"!==t?t:e===Math?"math":null===e?"null":Array.isArray(e)?"array":"[object Date]"===Object.prototype.toString.call(e)?"date":"function"==typeof e.toString&&/^\/.*\//.test(e.toString())?"regexp":"object"}function f(e,t,r,n,l,d,p){p=p||[];var h=(l=l||[]).slice(0);if(void 0!==d){if(n){if("function"==typeof n&&n(h,d))return;if("object"===(void 0===n?"undefined":N(n))){if(n.prefilter&&n.prefilter(h,d))return;if(n.normalize){var y=n.normalize(h,d,e,t);y&&(e=y[0],t=y[1])}}}h.push(d)}"regexp"===s(e)&&"regexp"===s(t)&&(e=e.toString(),t=t.toString());var v=void 0===e?"undefined":N(e),b=void 0===t?"undefined":N(t),g="undefined"!==v||p&&p[p.length-1].lhs&&p[p.length-1].lhs.hasOwnProperty(d),m="undefined"!==b||p&&p[p.length-1].rhs&&p[p.length-1].rhs.hasOwnProperty(d);if(!g&&m)r(new i(h,t));else if(!m&&g)r(new a(h,e));else if(s(e)!==s(t))r(new o(h,e,t));else if("date"===s(e)&&e-t!==0)r(new o(h,e,t));else if("object"===v&&null!==e&&null!==t)if(p.filter((function(t){return t.lhs===e})).length)e!==t&&r(new o(h,e,t));else{if(p.push({lhs:e,rhs:t}),Array.isArray(e)){var _;for(e.length,_=0;_<e.length;_++)_>=t.length?r(new c(h,_,new a(void 0,e[_]))):f(e[_],t[_],r,n,h,_,p);for(;_<t.length;)r(new c(h,_,new i(void 0,t[_++])))}else{var w=Object.keys(e),S=Object.keys(t);w.forEach((function(o,i){var a=S.indexOf(o);a>=0?(f(e[o],t[o],r,n,h,o,p),S=u(S,a)):f(e[o],void 0,r,n,h,o,p)})),S.forEach((function(e){f(void 0,t[e],r,n,h,e,p)}))}p.length=p.length-1}else e!==t&&("number"===v&&isNaN(e)&&isNaN(t)||r(new o(h,e,t)))}function l(e,t,r,n){return n=n||[],f(e,t,(function(e){e&&n.push(e)}),r),n.length?n:void 0}function d(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":d(o[r.path[n]],r.index,r.item);break;case"D":delete o[r.path[n]];break;case"E":case"N":o[r.path[n]]=r.rhs}}else switch(r.kind){case"A":d(e[t],r.index,r.item);break;case"D":e=u(e,t);break;case"E":case"N":e[t]=r.rhs}return e}function p(e,t,r){if(e&&t&&r&&r.kind){for(var n=e,o=-1,i=r.path?r.path.length-1:0;++o<i;)void 0===n[r.path[o]]&&(n[r.path[o]]="number"==typeof r.path[o]?[]:{}),n=n[r.path[o]];switch(r.kind){case"A":d(r.path?n[r.path[o]]:n,r.index,r.item);break;case"D":delete n[r.path[o]];break;case"E":case"N":n[r.path[o]]=r.rhs}}}function h(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":h(o[r.path[n]],r.index,r.item);break;case"D":case"E":o[r.path[n]]=r.lhs;break;case"N":delete o[r.path[n]]}}else switch(r.kind){case"A":h(e[t],r.index,r.item);break;case"D":case"E":e[t]=r.lhs;break;case"N":e=u(e,t)}return e}function y(e,t,r){if(e&&t&&r&&r.kind){var n,o,i=e;for(o=r.path.length-1,n=0;n<o;n++)void 0===i[r.path[n]]&&(i[r.path[n]]={}),i=i[r.path[n]];switch(r.kind){case"A":h(i[r.path[n]],r.index,r.item);break;case"D":case"E":i[r.path[n]]=r.lhs;break;case"N":delete i[r.path[n]]}}}function v(e,t,r){e&&t&&f(e,t,(function(n){r&&!r(e,t,n)||p(e,t,n)}))}function b(e){return"color: "+M[e].color+"; font-weight: bold"}function g(e){var t=e.kind,r=e.path,n=e.lhs,o=e.rhs,i=e.index,a=e.item;switch(t){case"E":return[r.join("."),n,"→",o];case"N":return[r.join("."),o];case"D":return[r.join(".")];case"A":return[r.join(".")+"["+i+"]",a];default:return[]}}function m(e,t,r,n){var o=l(e,t);try{n?r.groupCollapsed("diff"):r.group("diff")}catch(e){r.log("diff")}o?o.forEach((function(e){var t=e.kind,n=g(e);r.log.apply(r,["%c "+M[t].text,b(t)].concat(C(n)))})):r.log("—— no diff ——");try{r.groupEnd()}catch(e){r.log("—— diff end —— ")}}function _(e,t,r,n){switch(void 0===e?"undefined":N(e)){case"object":return"function"==typeof e[n]?e[n].apply(e,C(r)):e[n];case"function":return e(t);default:return e}}function w(e){var t=e.timestamp,r=e.duration;return function(e,n,o){var i=["action"];return i.push("%c"+String(e.type)),t&&i.push("%c@ "+n),r&&i.push("%c(in "+o.toFixed(2)+" ms)"),i.join(" ")}}function S(e,t){var r=t.logger,n=t.actionTransformer,o=t.titleFormatter,i=void 0===o?w(t):o,a=t.collapsed,c=t.colors,u=t.level,s=t.diff,f=void 0===t.titleFormatter;e.forEach((function(o,l){var d=o.started,p=o.startedTime,h=o.action,y=o.prevState,v=o.error,b=o.took,g=o.nextState,w=e[l+1];w&&(g=w.prevState,b=w.started-d);var S=n(h),E="function"==typeof a?a((function(){return g}),h,o):a,P=j(p),A=c.title?"color: "+c.title(S)+";":"",T=["color: gray; font-weight: lighter;"];T.push(A),t.timestamp&&T.push("color: gray; font-weight: lighter;"),t.duration&&T.push("color: gray; font-weight: lighter;");var O=i(S,P,b);try{E?c.title&&f?r.groupCollapsed.apply(r,["%c "+O].concat(T)):r.groupCollapsed(O):c.title&&f?r.group.apply(r,["%c "+O].concat(T)):r.group(O)}catch(e){r.log(O)}var D=_(u,S,[y],"prevState"),N=_(u,S,[S],"action"),C=_(u,S,[v,y],"error"),k=_(u,S,[g],"nextState");if(D)if(c.prevState){var M="color: "+c.prevState(y)+"; font-weight: bold";r[D]("%c prev state",M,y)}else r[D]("prev state",y);if(N)if(c.action){var x="color: "+c.action(S)+"; font-weight: bold";r[N]("%c action    ",x,S)}else r[N]("action    ",S);if(v&&C)if(c.error){var z="color: "+c.error(v,y)+"; font-weight: bold;";r[C]("%c error     ",z,v)}else r[C]("error     ",v);if(k)if(c.nextState){var F="color: "+c.nextState(g)+"; font-weight: bold";r[k]("%c next state",F,g)}else r[k]("next state",g);s&&m(y,g,r,E);try{r.groupEnd()}catch(e){r.log("—— log end ——")}}))}function E(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Object.assign({},x,e),r=t.logger,n=t.stateTransformer,o=t.errorTransformer,i=t.predicate,a=t.logErrors,c=t.diffPredicate;if(void 0===r)return function(){return function(e){return function(t){return e(t)}}};if(e.getState&&e.dispatch)return function(){return function(e){return function(t){return e(t)}}};var u=[];return function(e){var r=e.getState;return function(e){return function(s){if("function"==typeof i&&!i(r,s))return e(s);var f={};u.push(f),f.started=D.now(),f.startedTime=new Date,f.prevState=n(r()),f.action=s;var l=void 0;if(a)try{l=e(s)}catch(e){f.error=o(e)}else l=e(s);f.took=D.now()-f.started,f.nextState=n(r());var d=t.diff&&"function"==typeof c?c(r,s):t.diff;if(S(u,Object.assign({},t,{diff:d})),u.length=0,f.error)throw f.error;return l}}}}var P,A,T=function(e,t){return new Array(t+1).join(e)},O=function(e,t){return T("0",t-e.toString().length)+e},j=function(e){return O(e.getHours(),2)+":"+O(e.getMinutes(),2)+":"+O(e.getSeconds(),2)+"."+O(e.getMilliseconds(),3)},D="undefined"!=typeof performance&&null!==performance&&"function"==typeof performance.now?performance:Date,N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},C=function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)},k=[];P="object"===(void 0===r.g?"undefined":N(r.g))&&r.g?r.g:"undefined"!=typeof window?window:{},(A=P.DeepDiff)&&k.push((function(){void 0!==A&&P.DeepDiff===l&&(P.DeepDiff=A,A=void 0)})),t(o,n),t(i,n),t(a,n),t(c,n),Object.defineProperties(l,{diff:{value:l,enumerable:!0},observableDiff:{value:f,enumerable:!0},applyDiff:{value:v,enumerable:!0},applyChange:{value:p,enumerable:!0},revertChange:{value:y,enumerable:!0},isConflict:{value:function(){return void 0!==A},enumerable:!0},noConflict:{value:function(){return k&&(k.forEach((function(e){e()})),k=null),l},enumerable:!0}});var M={E:{color:"#2196F3",text:"CHANGED:"},N:{color:"#4CAF50",text:"ADDED:"},D:{color:"#F44336",text:"DELETED:"},A:{color:"#2196F3",text:"ARRAY:"}},x={level:"log",logger:console,logErrors:!0,collapsed:void 0,predicate:void 0,duration:!1,timestamp:!0,stateTransformer:function(e){return e},actionTransformer:function(e){return e},errorTransformer:function(e){return e},colors:{title:function(){return"inherit"},prevState:function(){return"#9E9E9E"},action:function(){return"#03A9F4"},nextState:function(){return"#4CAF50"},error:function(){return"#F20404"}},diff:!1,diffPredicate:void 0,transformer:void 0},z=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.dispatch,r=e.getState;return"function"==typeof t||"function"==typeof r?E()({dispatch:t,getState:r}):void 0};e.defaults=x,e.createLogger=E,e.logger=z,e.default=z,Object.defineProperty(e,"__esModule",{value:!0})}(t)},9449:(e,t)=>{"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function n(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){for(var t=arguments.length,o=new Array(t>1?t-1:0),i=1;i<t;i++)o[i-1]=arguments[i];var a=function(){throw new Error("Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.")},c={getState:e.getState.bind(e),dispatch:function(){return a.apply(void 0,arguments)}};return o=(o||[]).map((function(e){return e(c)})),a=n.apply(void 0,function(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(o))(e.dispatch),e.dispatch=a,e}},9529:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.STATE_TYPE=t.PATCH_STATE_TYPE=t.FETCH_STATE_TYPE=t.DISPATCH_TYPE=t.DEFAULT_CHANNEL_NAME=void 0;t.DISPATCH_TYPE="webext.dispatch",t.FETCH_STATE_TYPE="webext.fetch_state",t.STATE_TYPE="webext.state",t.PATCH_STATE_TYPE="webext.patch_state",t.DEFAULT_CHANNEL_NAME="webext.channel"}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),(()=>{"use strict";function e(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var t=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")(),n=()=>Math.random().toString(36).substring(7).split("").join("."),o={INIT:`@@redux/INIT${n()}`,REPLACE:`@@redux/REPLACE${n()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${n()}`};function i(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function a(r,n,c){if("function"!=typeof r)throw new Error(e(2));if("function"==typeof n&&"function"==typeof c||"function"==typeof c&&"function"==typeof arguments[3])throw new Error(e(0));if("function"==typeof n&&void 0===c&&(c=n,n=void 0),void 0!==c){if("function"!=typeof c)throw new Error(e(1));return c(a)(r,n)}let u=r,s=n,f=new Map,l=f,d=0,p=!1;function h(){l===f&&(l=new Map,f.forEach(((e,t)=>{l.set(t,e)})))}function y(){if(p)throw new Error(e(3));return s}function v(t){if("function"!=typeof t)throw new Error(e(4));if(p)throw new Error(e(5));let r=!0;h();const n=d++;return l.set(n,t),function(){if(r){if(p)throw new Error(e(6));r=!1,h(),l.delete(n),f=null}}}function b(t){if(!i(t))throw new Error(e(7));if(void 0===t.type)throw new Error(e(8));if("string"!=typeof t.type)throw new Error(e(17));if(p)throw new Error(e(9));try{p=!0,s=u(s,t)}finally{p=!1}return(f=l).forEach((e=>{e()})),t}b({type:o.INIT});return{dispatch:b,subscribe:v,getState:y,replaceReducer:function(t){if("function"!=typeof t)throw new Error(e(10));u=t,b({type:o.REPLACE})},[t]:function(){const r=v;return{subscribe(t){if("object"!=typeof t||null===t)throw new Error(e(11));function n(){const e=t;e.next&&e.next(y())}n();return{unsubscribe:r(n)}},[t](){return this}}}}}function c(t){const r=Object.keys(t),n={};for(let e=0;e<r.length;e++){const o=r[e];0,"function"==typeof t[o]&&(n[o]=t[o])}const i=Object.keys(n);let a;try{!function(t){Object.keys(t).forEach((r=>{const n=t[r];if(void 0===n(void 0,{type:o.INIT}))throw new Error(e(12));if(void 0===n(void 0,{type:o.PROBE_UNKNOWN_ACTION()}))throw new Error(e(13))}))}(n)}catch(c){a=c}return function(t={},r){if(a)throw a;let o=!1;const c={};for(let a=0;a<i.length;a++){const u=i[a],s=n[u],f=t[u],l=s(f,r);if(void 0===l){r&&r.type;throw new Error(e(14))}c[u]=l,o=o||l!==f}return o=o||i.length!==Object.keys(t).length,o?c:t}}function u(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce(((e,t)=>(...r)=>e(t(...r))))}var s=Symbol.for("immer-nothing"),f=Symbol.for("immer-draftable"),l=Symbol.for("immer-state");function d(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var p=Object.getPrototypeOf;function h(e){return!!e&&!!e[l]}function y(e){return!!e&&(b(e)||Array.isArray(e)||!!e[f]||!!e.constructor?.[f]||S(e)||E(e))}var v=Object.prototype.constructor.toString();function b(e){if(!e||"object"!=typeof e)return!1;const t=p(e);if(null===t)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===v}function g(e,t){0===m(e)?Reflect.ownKeys(e).forEach((r=>{t(r,e[r],e)})):e.forEach(((r,n)=>t(n,r,e)))}function m(e){const t=e[l];return t?t.type_:Array.isArray(e)?1:S(e)?2:E(e)?3:0}function _(e,t){return 2===m(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function w(e,t,r){const n=m(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function S(e){return e instanceof Map}function E(e){return e instanceof Set}function P(e){return e.copy_||e.base_}function A(e,t){if(S(e))return new Map(e);if(E(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=b(e);if(!0===t||"class_only"===t&&!r){const t=Object.getOwnPropertyDescriptors(e);delete t[l];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){const o=r[n],i=t[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[o]})}return Object.create(p(e),t)}{const t=p(e);if(null!==t&&r)return{...e};const n=Object.create(t);return Object.assign(n,e)}}function T(e,t=!1){return j(e)||h(e)||!y(e)||(m(e)>1&&(e.set=e.add=e.clear=e.delete=O),Object.freeze(e),t&&Object.entries(e).forEach((([e,t])=>T(t,!0)))),e}function O(){d(2)}function j(e){return Object.isFrozen(e)}var D,N={};function C(e){const t=N[e];return t||d(0),t}function k(){return D}function M(e,t){t&&(C("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function x(e){z(e),e.drafts_.forEach(R),e.drafts_=null}function z(e){e===D&&(D=e.parent_)}function F(e){return D={drafts_:[],parent_:D,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function R(e){const t=e[l];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function I(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return void 0!==e&&e!==r?(r[l].modified_&&(x(t),d(4)),y(e)&&(e=L(t,e),t.parent_||Y(t,e)),t.patches_&&C("Patches").generateReplacementPatches_(r[l].base_,e,t.patches_,t.inversePatches_)):e=L(t,r,[]),x(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==s?e:void 0}function L(e,t,r){if(j(t))return t;const n=t[l];if(!n)return g(t,((o,i)=>U(e,n,t,o,i,r))),t;if(n.scope_!==e)return t;if(!n.modified_)return Y(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const t=n.copy_;let o=t,i=!1;3===n.type_&&(o=new Set(t),t.clear(),i=!0),g(o,((o,a)=>U(e,n,t,o,a,r,i))),Y(e,t,!1),r&&e.patches_&&C("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function U(e,t,r,n,o,i,a){if(h(o)){const a=L(e,o,i&&t&&3!==t.type_&&!_(t.assigned_,n)?i.concat(n):void 0);if(w(r,n,a),!h(a))return;e.canAutoFreeze_=!1}else a&&r.add(o);if(y(o)&&!j(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;L(e,o),t&&t.scope_.parent_||"symbol"==typeof n||!Object.prototype.propertyIsEnumerable.call(r,n)||Y(e,o)}}function Y(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&T(t,r)}var H={get(e,t){if(t===l)return e;const r=P(e);if(!_(r,t))return function(e,t,r){const n=K(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}(e,r,t);const n=r[t];return e.finalized_||!y(n)?n:n===$(e.base_,t)?(q(e),e.copy_[t]=V(n,e)):n},has:(e,t)=>t in P(e),ownKeys:e=>Reflect.ownKeys(P(e)),set(e,t,r){const n=K(P(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const n=$(P(e),t),a=n?.[l];if(a&&a.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(((o=r)===(i=n)?0!==o||1/o==1/i:o!=o&&i!=i)&&(void 0!==r||_(e.base_,t)))return!0;q(e),W(e)}var o,i;return e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==$(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,q(e),W(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const r=P(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){d(11)},getPrototypeOf:e=>p(e.base_),setPrototypeOf(){d(12)}},B={};function $(e,t){const r=e[l];return(r?P(r):e)[t]}function K(e,t){if(!(t in e))return;let r=p(e);for(;r;){const e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=p(r)}}function W(e){e.modified_||(e.modified_=!0,e.parent_&&W(e.parent_))}function q(e){e.copy_||(e.copy_=A(e.base_,e.scope_.immer_.useStrictShallowCopy_))}g(H,((e,t)=>{B[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),B.deleteProperty=function(e,t){return B.set.call(this,e,t,void 0)},B.set=function(e,t,r){return H.set.call(this,e[0],t,r,e[0])};function V(e,t){const r=S(e)?C("MapSet").proxyMap_(e,t):E(e)?C("MapSet").proxySet_(e,t):function(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:k(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let o=n,i=H;r&&(o=[n],i=B);const{revoke:a,proxy:c}=Proxy.revocable(o,i);return n.draft_=c,n.revoke_=a,c}(e,t);return(t?t.scope_:k()).drafts_.push(r),r}function X(e){if(!y(e)||j(e))return e;const t=e[l];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=A(e,t.scope_.immer_.useStrictShallowCopy_)}else r=A(e,!0);return g(r,((e,t)=>{w(r,e,X(t))})),t&&(t.finalized_=!1),r}var G=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{if("function"==typeof e&&"function"!=typeof t){const r=t;t=e;const n=this;return function(e=r,...o){return n.produce(e,(e=>t.call(this,e,...o)))}}let n;if("function"!=typeof t&&d(6),void 0!==r&&"function"!=typeof r&&d(7),y(e)){const o=F(this),i=V(e,void 0);let a=!0;try{n=t(i),a=!1}finally{a?x(o):z(o)}return M(o,r),I(n,o)}if(!e||"object"!=typeof e){if(n=t(e),void 0===n&&(n=e),n===s&&(n=void 0),this.autoFreeze_&&T(n,!0),r){const t=[],o=[];C("Patches").generateReplacementPatches_(e,n,t,o),r(t,o)}return n}d(1)},this.produceWithPatches=(e,t)=>{if("function"==typeof e)return(t,...r)=>this.produceWithPatches(t,(t=>e(t,...r)));let r,n;return[this.produce(e,t,((e,t)=>{r=e,n=t})),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){y(e)||d(8),h(e)&&(e=function(e){h(e)||d(10);return X(e)}(e));const t=F(this),r=V(e,void 0);return r[l].isManual_=!0,z(t),r}finishDraft(e,t){const r=e&&e[l];r&&r.isManual_||d(9);const{scope_:n}=r;return M(n,t),I(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));const n=C("Patches").applyPatches_;return h(e)?n(e,t):this.produce(e,(e=>n(e,t)))}},J=G.produce;G.produceWithPatches.bind(G),G.setAutoFreeze.bind(G),G.setUseStrictShallowCopy.bind(G),G.applyPatches.bind(G),G.createDraft.bind(G),G.finishDraft.bind(G);function Q(e,t="expected a function, instead received "+typeof e){if("function"!=typeof e)throw new TypeError(t)}var Z=e=>Array.isArray(e)?e:[e];function ee(e){const t=Array.isArray(e[0])?e[0]:e;return function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every((e=>"function"==typeof e))){const r=e.map((e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e)).join(", ");throw new TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}Symbol(),Object.getPrototypeOf({});var te="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function re(e,t={}){let r={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:n}=t;let o,i=0;function a(){let t=r;const{length:a}=arguments;for(let e=0,r=a;e<r;e++){const r=arguments[e];if("function"==typeof r||"object"==typeof r&&null!==r){let e=t.o;null===e&&(t.o=e=new WeakMap);const n=e.get(r);void 0===n?(t={s:0,v:void 0,o:null,p:null},e.set(r,t)):t=n}else{let e=t.p;null===e&&(t.p=e=new Map);const n=e.get(r);void 0===n?(t={s:0,v:void 0,o:null,p:null},e.set(r,t)):t=n}}const c=t;let u;if(1===t.s)u=t.v;else if(u=e.apply(null,arguments),i++,n){const e=o?.deref?.()??o;null!=e&&n(e,u)&&(u=e,0!==i&&i--);o="object"==typeof u&&null!==u||"function"==typeof u?new te(u):u}return c.s=1,c.v=u,u}return a.clearCache=()=>{r={s:0,v:void 0,o:null,p:null},a.resetResultsCount()},a.resultsCount=()=>i,a.resetResultsCount=()=>{i=0},a}function ne(e,...t){const r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,o=0,i={},a=e.pop();"object"==typeof a&&(i=a,a=e.pop()),Q(a,`createSelector expects an output function after the inputs, but received: [${typeof a}]`);const c={...r,...i},{memoize:u,memoizeOptions:s=[],argsMemoize:f=re,argsMemoizeOptions:l=[],devModeChecks:d={}}=c,p=Z(s),h=Z(l),y=ee(e),v=u((function(){return n++,a.apply(null,arguments)}),...p);const b=f((function(){o++;const e=function(e,t){const r=[],{length:n}=e;for(let o=0;o<n;o++)r.push(e[o].apply(null,t));return r}(y,arguments);return t=v.apply(null,e),t}),...h);return Object.assign(b,{resultFunc:a,memoizedResultFunc:v,dependencies:y,dependencyRecomputations:()=>o,resetDependencyRecomputations:()=>{o=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:u,argsMemoize:f})};return Object.assign(n,{withTypes:()=>n}),n}var oe=ne(re),ie=Object.assign(((e,t=oe)=>{!function(e,t="expected an object, instead received "+typeof e){if("object"!=typeof e)throw new TypeError(t)}(e,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof e);const r=Object.keys(e);return t(r.map((t=>e[t])),((...e)=>e.reduce(((e,t,n)=>(e[r[n]]=t,e)),{})))}),{withTypes:()=>ie});function ae(e){return({dispatch:t,getState:r})=>n=>o=>"function"==typeof o?o(t,r,e):n(o)}var ce=ae(),ue=ae,se="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?u:u.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function fe(e,t){function r(...r){if(t){let n=t(...r);if(!n)throw new Error(Te(0));return{type:e,payload:n.payload,..."meta"in n&&{meta:n.meta},..."error"in n&&{error:n.error}}}return{type:e,payload:r[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>function(e){return i(e)&&"type"in e&&"string"==typeof e.type}(t)&&t.type===e,r}var le=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function de(e){return y(e)?J(e,(()=>{})):e}function pe(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var he="RTK_autoBatch",ye=e=>t=>{setTimeout(t,e)},ve=e=>function(t){const{autoBatch:r=!0}=t??{};let n=new le(e);return r&&n.push(((e={type:"raf"})=>t=>(...r)=>{const n=t(...r);let o=!0,i=!1,a=!1;const c=new Set,u="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:ye(10):"callback"===e.type?e.queueNotification:ye(e.timeout),s=()=>{a=!1,i&&(i=!1,c.forEach((e=>e())))};return Object.assign({},n,{subscribe(e){const t=n.subscribe((()=>o&&e()));return c.add(e),()=>{t(),c.delete(e)}},dispatch(e){try{return o=!e?.meta?.[he],i=!o,i&&(a||(a=!0,u(s))),n.dispatch(e)}finally{o=!0}}})})("object"==typeof r?r:void 0)),n};function be(t){const r=function(e){const{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{};let i=new le;return t&&("boolean"==typeof t?i.push(ce):i.push(ue(t.extraArgument))),i},{reducer:n,middleware:o,devTools:s=!0,duplicateMiddlewareCheck:f=!0,preloadedState:l,enhancers:d}=t||{};let p,h;if("function"==typeof n)p=n;else{if(!i(n))throw new Error(Te(1));p=c(n)}h="function"==typeof o?o(r):r();let y=u;s&&(y=se({trace:!1,..."object"==typeof s&&s}));const v=function(...t){return r=>(n,o)=>{const i=r(n,o);let a=()=>{throw new Error(e(15))};const c={getState:i.getState,dispatch:(e,...t)=>a(e,...t)},s=t.map((e=>e(c)));return a=u(...s)(i.dispatch),{...i,dispatch:a}}}(...h),b=ve(v);return a(p,l,y(..."function"==typeof d?d(b):b()))}function ge(e){const t={},r=[];let n;const o={addCase(e,r){const n="string"==typeof e?e:e.type;if(!n)throw new Error(Te(28));if(n in t)throw new Error(Te(29));return t[n]=r,o},addMatcher:(e,t)=>(r.push({matcher:e,reducer:t}),o),addDefaultCase:e=>(n=e,o)};return e(o),[t,r,n]}var me=Symbol.for("rtk-slice-createasyncthunk");function _e(e,t){return`${e}/${t}`}function we({creators:e}={}){const t=e?.asyncThunk?.[me];return function(e){const{name:r,reducerPath:n=r}=e;if(!r)throw new Error(Te(11));const o=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},i=Object.keys(o),a={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},c={addCase(e,t){const r="string"==typeof e?e:e.type;if(!r)throw new Error(Te(12));if(r in a.sliceCaseReducersByType)throw new Error(Te(13));return a.sliceCaseReducersByType[r]=t,c},addMatcher:(e,t)=>(a.sliceMatchers.push({matcher:e,reducer:t}),c),exposeAction:(e,t)=>(a.actionCreators[e]=t,c),exposeCaseReducer:(e,t)=>(a.sliceCaseReducersByName[e]=t,c)};function u(){const[t={},r=[],n]="function"==typeof e.extraReducers?ge(e.extraReducers):[e.extraReducers],o={...t,...a.sliceCaseReducersByType};return function(e,t){let r,[n,o,i]=ge(t);if("function"==typeof e)r=()=>de(e());else{const t=de(e);r=()=>t}function a(e=r(),t){let a=[n[t.type],...o.filter((({matcher:e})=>e(t))).map((({reducer:e})=>e))];return 0===a.filter((e=>!!e)).length&&(a=[i]),a.reduce(((e,r)=>{if(r){if(h(e)){const n=r(e,t);return void 0===n?e:n}if(y(e))return J(e,(e=>r(e,t)));{const n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}}return e}),e)}return a.getInitialState=r,a}(e.initialState,(e=>{for(let t in o)e.addCase(t,o[t]);for(let t of a.sliceMatchers)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)}))}i.forEach((n=>{const i=o[n],a={reducerName:n,type:_e(r,n),createNotation:"function"==typeof e.reducers};!function(e){return"asyncThunk"===e._reducerDefinitionType}(i)?function({type:e,reducerName:t,createNotation:r},n,o){let i,a;if("reducer"in n){if(r&&!function(e){return"reducerWithPrepare"===e._reducerDefinitionType}(n))throw new Error(Te(17));i=n.reducer,a=n.prepare}else i=n;o.addCase(e,i).exposeCaseReducer(t,i).exposeAction(t,a?fe(e,a):fe(e))}(a,i,c):function({type:e,reducerName:t},r,n,o){if(!o)throw new Error(Te(18));const{payloadCreator:i,fulfilled:a,pending:c,rejected:u,settled:s,options:f}=r,l=o(e,i,f);n.exposeAction(t,l),a&&n.addCase(l.fulfilled,a);c&&n.addCase(l.pending,c);u&&n.addCase(l.rejected,u);s&&n.addMatcher(l.settled,s);n.exposeCaseReducer(t,{fulfilled:a||Pe,pending:c||Pe,rejected:u||Pe,settled:s||Pe})}(a,i,c,t)}));const s=e=>e,f=new Map,l=new WeakMap;let d;function p(e,t){return d||(d=u()),d(e,t)}function v(){return d||(d=u()),d.getInitialState()}function b(t,r=!1){function n(e){let o=e[t];return void 0===o&&r&&(o=pe(l,n,v)),o}function o(t=s){const n=pe(f,r,(()=>new WeakMap));return pe(n,t,(()=>{const n={};for(const[o,i]of Object.entries(e.selectors??{}))n[o]=Se(i,t,(()=>pe(l,t,v)),r);return n}))}return{reducerPath:t,getSelectors:o,get selectors(){return o(n)},selectSlice:n}}const g={name:r,reducer:p,actions:a.actionCreators,caseReducers:a.sliceCaseReducersByName,getInitialState:v,...b(n),injectInto(e,{reducerPath:t,...r}={}){const o=t??n;return e.inject({reducerPath:o,reducer:p},r),{...g,...b(o,!0)}}};return g}}function Se(e,t,r,n){function o(o,...i){let a=t(o);return void 0===a&&n&&(a=r()),e(a,...i)}return o.unwrapped=e,o}var Ee=we();function Pe(){}var{assign:Ae}=Object;Symbol.for("rtk-state-proxy-original");function Te(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var Oe=r(9448),je=r(3207);const De={isLoaded:!1},Ne=Ee({name:"content",initialState:De,reducers:{reset:()=>De,contentLoaded:e=>{e.isLoaded=!0}}}),{actions:Ce,reducer:ke}=Ne;var Me,xe;!function(e){e.Default="default",e.ConfirmDeleteCard="confirmDeleteCard"}(Me||(Me={})),function(e){e.Default="default"}(xe||(xe={}));const ze={isOpen:!1},Fe=Ee({name:"sidePanel",initialState:ze,reducers:{reset:()=>ze}}),{actions:Re,reducer:Ie}=Fe,Le={},Ue=((0,je.nK)(Le),Oe.logger,(0,je.nK)(Le),Oe.logger,[(0,je.nK)(Le),Oe.logger]),Ye=({channelName:e}={})=>{const t=be({devTools:!0,reducer:c({sidePanel:Ie,content:ke}),middleware:e=>e().concat(Ue)});if(e){(0,je.Iq)({channelName:e})(t)}return t};var He;!function(e){e.ContentPort="content",e.SidePanelPort="sidePanel",e.SAVE_NOTE_TO_FILE="save-note-to-file"}(He||(He={}));Ye({channelName:He.ContentPort}),chrome.sidePanel.setPanelBehavior({openPanelOnActionClick:!0}).catch(console.error)})()})();